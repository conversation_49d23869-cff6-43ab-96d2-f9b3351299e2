import { supabase } from './supabase'

// Migration helper functions
export const runMigration = async (migrationName, migrationFunction) => {
  try {
    console.log(`Running migration: ${migrationName}`)
    await migrationFunction()
    console.log(`Migration completed: ${migrationName}`)
  } catch (error) {
    console.error(`Migration failed: ${migrationName}`, error)
    throw error
  }
}

// Check if tables exist
export const checkTablesExist = async () => {
  try {
    const { data, error } = await supabase
      .from('tasks')
      .select('id')
      .limit(1)
    
    return !error
  } catch (error) {
    return false
  }
}

// Migrate localStorage data to Supabase (if needed)
export const migrateLocalStorageData = async (userId) => {
  try {
    const localTasks = localStorage.getItem('idea-tasks')
    if (!localTasks) return

    const tasks = JSON.parse(localTasks)
    if (tasks.length === 0) return

    console.log(`Migrating ${tasks.length} tasks from localStorage to Supabase`)

    for (const task of tasks) {
      // Create task in Supabase
      const { data: newTask, error: taskError } = await supabase
        .from('tasks')
        .insert([{
          user_id: userId,
          title: task.title,
          description: task.description || '',
          status: task.status,
          priority: task.priority,
          category: task.category,
          tags: task.tags || [],
          start_date: task.startDate,
          due_date: task.dueDate,
          estimated_hours: task.estimatedHours || 0,
          actual_hours: task.actualHours || 0,
          progress: task.progress || 0
        }])
        .select()
        .single()

      if (taskError) {
        console.error('Error migrating task:', task.title, taskError)
        continue
      }

      // Migrate follow-ups
      if (task.followUps && task.followUps.length > 0) {
        const followUps = task.followUps.map(fu => ({
          task_id: newTask.id,
          description: fu.description || fu.text,
          due_date: fu.dueDate,
          completed: fu.completed || false
        }))

        const { error: followUpError } = await supabase
          .from('follow_ups')
          .insert(followUps)

        if (followUpError) {
          console.error('Error migrating follow-ups for task:', task.title, followUpError)
        }
      }

      // Migrate process steps
      if (task.processSteps && task.processSteps.length > 0) {
        const processSteps = task.processSteps.map((step, index) => ({
          task_id: newTask.id,
          title: step.title,
          description: step.description || '',
          completed: step.completed || false,
          step_order: step.order || index
        }))

        const { error: stepError } = await supabase
          .from('process_steps')
          .insert(processSteps)

        if (stepError) {
          console.error('Error migrating process steps for task:', task.title, stepError)
        }
      }
    }

    // Backup localStorage data before clearing
    localStorage.setItem('idea-tasks-backup', localTasks)
    localStorage.removeItem('idea-tasks')
    
    console.log('Migration completed successfully')
  } catch (error) {
    console.error('Migration failed:', error)
    throw error
  }
}
