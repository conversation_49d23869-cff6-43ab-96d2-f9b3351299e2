import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://nbzbqptayogfxdtmfavm.supabase.co'
// You'll need to get the anon key from your Supabase dashboard
// Go to Settings > API > anon public key
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5iemJxcHRheW9nZnhkdG1mYXZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0MjM0MzgsImV4cCI6MjA2Mzk5OTQzOH0.Akt07b4XsKGj6rlmaDfutoSwQnLrLt1firTq7tzO_uY'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
