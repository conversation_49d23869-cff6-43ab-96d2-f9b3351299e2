import { useState } from 'react';
import {
  Plus,
  Search,
  Filter,
  Lightbulb,
  X,
  LayoutDashboard,
  LayoutList
} from 'lucide-react';
import { TaskStatus, TaskPriority, TaskCategory } from '../types/task';

const Header = ({ onNewTask, filter, setFilter, stats, viewMode, setViewMode }) => {
  const [showFilters, setShowFilters] = useState(false);

  const clearFilters = () => {
    setFilter({ status: '', priority: '', category: '', search: '' });
  };

  const hasActiveFilters = filter.status || filter.priority || filter.category || filter.search;

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Header */}
        <div className="flex items-center justify-between h-16">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            <div className="bg-gradient-to-r from-primary-500 to-purple-600 rounded-xl p-2">
              <Lightbulb className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Idea Manager</h1>
              <p className="text-sm text-gray-500 hidden sm:block">Transform ideas into reality</p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="hidden lg:flex items-center space-x-6 bg-gray-50 rounded-lg px-4 py-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-900">{stats.ideas}</span>
              <span className="text-sm text-gray-600">Ideas</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-900">{stats.inProgress}</span>
              <span className="text-sm text-gray-600">Active</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-900">{stats.completed}</span>
              <span className="text-sm text-gray-600">Done</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative hidden md:block">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search ideas..."
                className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                value={filter.search}
                onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('dashboard')}
                className={`p-1.5 rounded-md transition-all duration-200 ${
                  viewMode === 'dashboard'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Dashboard View"
              >
                <LayoutDashboard className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded-md transition-all duration-200 ${
                  viewMode === 'list'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List View"
              >
                <LayoutList className="w-4 h-4" />
              </button>
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-2 rounded-lg border transition-all duration-200 ${
                hasActiveFilters || showFilters
                  ? 'bg-primary-50 border-primary-200 text-primary-700 shadow-sm'
                  : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
              }`}
              title="Filters"
            >
              <Filter className="w-5 h-5" />
              {hasActiveFilters && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full"></div>
              )}
            </button>

            {/* New Task Button */}
            <button
              onClick={onNewTask}
              className="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <Plus className="w-4 h-4" />
              <span className="hidden sm:inline">New Idea</span>
            </button>
          </div>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden pb-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search ideas..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
              value={filter.search}
              onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
            />
          </div>
        </div>

        {/* Expandable Filters */}
        {showFilters && (
          <div className="border-t border-gray-100 py-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Filter & Sort</h3>
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="text-sm text-gray-500 hover:text-gray-700 flex items-center space-x-1"
                >
                  <X className="w-4 h-4" />
                  <span>Clear all</span>
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-2">Status</label>
                <select
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={filter.status}
                  onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value }))}
                >
                  <option value="">All Statuses</option>
                  <option value={TaskStatus.IDEA}>💡 Ideas</option>
                  <option value={TaskStatus.PLANNING}>📋 Planning</option>
                  <option value={TaskStatus.IN_PROGRESS}>⚡ In Progress</option>
                  <option value={TaskStatus.REVIEW}>👀 Review</option>
                  <option value={TaskStatus.COMPLETED}>✅ Completed</option>
                  <option value={TaskStatus.ON_HOLD}>⏸️ On Hold</option>
                  <option value={TaskStatus.CANCELLED}>❌ Cancelled</option>
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-2">Priority</label>
                <select
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={filter.priority}
                  onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value }))}
                >
                  <option value="">All Priorities</option>
                  <option value={TaskPriority.URGENT}>🔴 Urgent</option>
                  <option value={TaskPriority.HIGH}>🟠 High</option>
                  <option value={TaskPriority.MEDIUM}>🟡 Medium</option>
                  <option value={TaskPriority.LOW}>🟢 Low</option>
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-2">Category</label>
                <select
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  value={filter.category}
                  onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value }))}
                >
                  <option value="">All Categories</option>
                  <option value={TaskCategory.FEATURE}>✨ Feature</option>
                  <option value={TaskCategory.BUG_FIX}>🐛 Bug Fix</option>
                  <option value={TaskCategory.IMPROVEMENT}>🚀 Improvement</option>
                  <option value={TaskCategory.RESEARCH}>🔍 Research</option>
                  <option value={TaskCategory.DOCUMENTATION}>📚 Documentation</option>
                  <option value={TaskCategory.TESTING}>🧪 Testing</option>
                  <option value={TaskCategory.OTHER}>📋 Other</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
