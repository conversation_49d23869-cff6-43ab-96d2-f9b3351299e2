import React, { useState } from 'react';
import {
  ChevronDown,
  ChevronRight,
  Plus,
  ArrowUpCircle,
  Clock,
  Target,
  Flag
} from 'lucide-react';
import TaskCard from './TaskCard';
import { TaskStatus, TaskPriority } from '../types/task';
import { isOverdue } from '../utils/helpers';

const TaskList = ({
  tasks,
  onEdit,
  onDelete,
  onToggleFollowUp,
  onToggleProcessStep,
  onNewTask,
  filter
}) => {
  const [expandedSections, setExpandedSections] = useState({
    overdue: true,
    priority: true,
    status: true
  });

  // Filter and organize tasks
  const overdueTasks = tasks.filter(task => isOverdue(task.dueDate) && task.status !== TaskStatus.COMPLETED);
  const priorityTasks = tasks.filter(task =>
    task.priority === TaskPriority.URGENT || task.priority === TaskPriority.HIGH
  );

  // Group tasks by status
  const tasksByStatus = tasks.reduce((acc, task) => {
    if (!acc[task.status]) acc[task.status] = [];
    acc[task.status].push(task);
    return acc;
  }, {});

  const statusOrder = [
    TaskStatus.IDEA,
    TaskStatus.PLANNING,
    TaskStatus.IN_PROGRESS,
    TaskStatus.REVIEW,
    TaskStatus.COMPLETED,
    TaskStatus.ON_HOLD,
    TaskStatus.CANCELLED
  ];

  const statusConfig = {
    [TaskStatus.IDEA]: { label: '💡 Ideas', color: 'purple' },
    [TaskStatus.PLANNING]: { label: '📋 Planning', color: 'blue' },
    [TaskStatus.IN_PROGRESS]: { label: '⚡ In Progress', color: 'orange' },
    [TaskStatus.REVIEW]: { label: '👀 Review', color: 'amber' },
    [TaskStatus.COMPLETED]: { label: '✅ Completed', color: 'green' },
    [TaskStatus.ON_HOLD]: { label: '⏸️ On Hold', color: 'gray' },
    [TaskStatus.CANCELLED]: { label: '❌ Cancelled', color: 'red' },
  };

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const SectionHeader = ({ title, count, isExpanded, onToggle, icon: Icon, color, actionButton }) => (
    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 mb-4">
      <button
        onClick={onToggle}
        className="flex items-center space-x-3 flex-1 text-left"
      >
        {isExpanded ? <ChevronDown className="w-5 h-5 text-gray-400" /> : <ChevronRight className="w-5 h-5 text-gray-400" />}
        {Icon && <Icon className={`w-5 h-5 text-${color}-600`} />}
        <h3 className="font-semibold text-gray-900">{title}</h3>
        <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800`}>
          {count}
        </span>
      </button>
      {actionButton}
    </div>
  );

  const TaskGrid = ({ tasks, emptyMessage, emptyAction }) => {
    if (tasks.length === 0) {
      return (
        <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
          <p className="text-gray-500 mb-4">{emptyMessage}</p>
          {emptyAction}
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {tasks.map(task => (
          <TaskCard
            key={task.id}
            task={task}
            onEdit={onEdit}
            onDelete={onDelete}
            onToggleFollowUp={onToggleFollowUp}
            onToggleProcessStep={onToggleProcessStep}
          />
        ))}
      </div>
    );
  };

  // If no tasks match filters
  if (tasks.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <span className="text-4xl">🔍</span>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
        <p className="text-gray-600 mb-6">
          {filter.search || filter.status || filter.priority || filter.category
            ? "Try adjusting your filters or create a new task."
            : "Start by creating your first idea or task!"}
        </p>
        <button
          onClick={onNewTask}
          className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center space-x-2"
        >
          <Plus className="w-5 h-5" />
          <span>Create New Task</span>
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Indicator */}
      {(filter.search || filter.status || filter.priority || filter.category) && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium text-blue-900">
                Filtered Results
                {filter.status && ` • Status: ${filter.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
                {filter.priority && ` • Priority: ${filter.priority.charAt(0).toUpperCase() + filter.priority.slice(1)}`}
                {filter.category && ` • Category: ${filter.category.charAt(0).toUpperCase() + filter.category.slice(1)}`}
                {filter.search && ` • Search: "${filter.search}"`}
              </span>
            </div>
            <span className="text-sm text-blue-700">
              {tasks.length} task{tasks.length !== 1 ? 's' : ''} found
            </span>
          </div>
        </div>
      )}
      {/* Overdue Tasks - High Priority Section */}
      {overdueTasks.length > 0 && (
        <div>
          <SectionHeader
            title="Overdue Tasks"
            count={overdueTasks.length}
            isExpanded={expandedSections.overdue}
            onToggle={() => toggleSection('overdue')}
            icon={Clock}
            color="red"
          />
          {expandedSections.overdue && (
            <TaskGrid
              tasks={overdueTasks}
              emptyMessage="No overdue tasks"
            />
          )}
        </div>
      )}

      {/* High Priority Tasks */}
      {priorityTasks.length > 0 && (
        <div>
          <SectionHeader
            title="High Priority"
            count={priorityTasks.length}
            isExpanded={expandedSections.priority}
            onToggle={() => toggleSection('priority')}
            icon={Flag}
            color="orange"
          />
          {expandedSections.priority && (
            <TaskGrid
              tasks={priorityTasks}
              emptyMessage="No high priority tasks"
            />
          )}
        </div>
      )}

      {/* Tasks by Status */}
      <div>
        <SectionHeader
          title="All Tasks by Status"
          count={tasks.length}
          isExpanded={expandedSections.status}
          onToggle={() => toggleSection('status')}
          icon={Target}
          color="blue"
          actionButton={
            <button
              onClick={onNewTask}
              className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium inline-flex items-center space-x-2 text-sm"
            >
              <Plus className="w-4 h-4" />
              <span>Add Task</span>
            </button>
          }
        />

        {expandedSections.status && (
          <div className="space-y-6">
            {statusOrder
              .filter(status => tasksByStatus[status]?.length > 0)
              .map(status => {
                const config = statusConfig[status];
                const statusTasks = tasksByStatus[status];

                return (
                  <div key={status} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                        <span>{config.label}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium bg-${config.color}-100 text-${config.color}-800`}>
                          {statusTasks.length}
                        </span>
                      </h4>
                      {status === TaskStatus.IDEA && (
                        <button
                          onClick={onNewTask}
                          className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center space-x-1"
                        >
                          <Plus className="w-4 h-4" />
                          <span>Add Idea</span>
                        </button>
                      )}
                    </div>
                    <TaskGrid
                      tasks={statusTasks}
                      emptyMessage={`No ${config.label.toLowerCase()} tasks`}
                      emptyAction={status === TaskStatus.IDEA && (
                        <button
                          onClick={onNewTask}
                          className="text-primary-600 hover:text-primary-700 font-medium"
                        >
                          Create your first idea
                        </button>
                      )}
                    />
                  </div>
                );
              })}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskList;
