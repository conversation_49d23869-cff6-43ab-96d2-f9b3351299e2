import React from 'react';
import {
  Plus,
  <PERSON>bulb,
  PenTool,
  Zap,
  Eye,
  CheckCircle,
  TrendingUp,
  ArrowR<PERSON>,
  X as XIcon
} from 'lucide-react';
import { TaskStatus } from '../types/task';

const Dashboard = ({ tasks, onNewTask, filter, setFilter, stats, onViewModeChange }) => {

  // Group tasks by status for workflow visualization
  const tasksByStatus = {
    [TaskStatus.IDEA]: tasks.filter(t => t.status === TaskStatus.IDEA),
    [TaskStatus.PLANNING]: tasks.filter(t => t.status === TaskStatus.PLANNING),
    [TaskStatus.IN_PROGRESS]: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS),
    [TaskStatus.REVIEW]: tasks.filter(t => t.status === TaskStatus.REVIEW),
    [TaskStatus.COMPLETED]: tasks.filter(t => t.status === TaskStatus.COMPLETED),
    [TaskStatus.ON_HOLD]: tasks.filter(t => t.status === TaskStatus.ON_HOLD),
  };

  const workflowStages = [
    {
      key: TaskStatus.IDEA,
      title: 'Ideas',
      icon: Lightbulb,
      color: 'purple',
      description: 'Capture and explore new concepts',
      count: tasksByStatus[TaskStatus.IDEA].length,
      action: 'Brainstorm',
      nextStage: 'planning'
    },
    {
      key: TaskStatus.PLANNING,
      title: 'Planning',
      icon: PenTool,
      color: 'blue',
      description: 'Define scope and create action plans',
      count: tasksByStatus[TaskStatus.PLANNING].length,
      action: 'Plan',
      nextStage: 'execution'
    },
    {
      key: TaskStatus.IN_PROGRESS,
      title: 'In Progress',
      icon: Zap,
      color: 'orange',
      description: 'Actively working on implementation',
      count: tasksByStatus[TaskStatus.IN_PROGRESS].length,
      action: 'Execute',
      nextStage: 'review'
    },
    {
      key: TaskStatus.REVIEW,
      title: 'Review',
      icon: Eye,
      color: 'amber',
      description: 'Testing and validation phase',
      count: tasksByStatus[TaskStatus.REVIEW].length,
      action: 'Review',
      nextStage: 'completion'
    },
    {
      key: TaskStatus.COMPLETED,
      title: 'Completed',
      icon: CheckCircle,
      color: 'green',
      description: 'Successfully finished tasks',
      count: tasksByStatus[TaskStatus.COMPLETED].length,
      action: 'Celebrate',
      nextStage: null
    }
  ];

  const QuickActionCard = ({ stage, isActive, onClick }) => {
    const Icon = stage.icon;
    const colorClasses = {
      purple: 'bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100',
      blue: 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100',
      orange: 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100',
      amber: 'bg-amber-50 border-amber-200 text-amber-700 hover:bg-amber-100',
      green: 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100',
    };

    return (
      <button
        onClick={onClick}
        className={`p-4 rounded-xl border-2 transition-all duration-200 text-left w-full ${
          isActive ? 'ring-2 ring-offset-2 ring-primary-500 ' + colorClasses[stage.color] : colorClasses[stage.color]
        }`}
      >
        <div className="flex items-center justify-between mb-2">
          <Icon className="w-6 h-6" />
          <span className="text-2xl font-bold">{stage.count}</span>
        </div>
        <h3 className="font-semibold mb-1">{stage.title}</h3>
        <p className="text-sm opacity-80">{stage.description}</p>
        <div className="mt-2 flex items-center text-sm font-medium">
          <span>{stage.action}</span>
          {stage.nextStage && <ArrowRight className="w-3 h-3 ml-1" />}
        </div>
      </button>
    );
  };

  const handleStageClick = (status) => {
    setFilter(prev => ({ ...prev, status }));
    // Switch to list view to show filtered tasks
    if (onViewModeChange) {
      onViewModeChange('list');
    }
  };

  const handleNewIdeaClick = () => {
    onNewTask();
  };

  return (
    <div className="space-y-6">
      {/* Workflow Overview */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Your Innovation Pipeline</h2>
            <p className="text-gray-600">Track ideas from conception to completion</p>
          </div>
          <button
            onClick={handleNewIdeaClick}
            className="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-6 py-3 rounded-xl font-medium flex items-center space-x-2 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <Plus className="w-5 h-5" />
            <span>New Idea</span>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {workflowStages.map((stage, index) => (
            <div key={stage.key} className="relative">
              <QuickActionCard
                stage={stage}
                isActive={filter.status === stage.key}
                onClick={() => handleStageClick(stage.key)}
              />
              {index < workflowStages.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-2 transform -translate-y-1/2 z-10">
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Quick Stats & Insights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-blue-100 rounded-lg p-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Progress Overview</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Active</span>
              <span className="font-semibold">{stats.inProgress + stats.planning}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Completion Rate</span>
              <span className="font-semibold">
                {stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-purple-100 rounded-lg p-2">
              <Lightbulb className="w-5 h-5 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Ideas Pipeline</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">New Ideas</span>
              <span className="font-semibold">{stats.ideas}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Ready to Plan</span>
              <span className="font-semibold">{tasksByStatus[TaskStatus.IDEA].length}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-green-100 rounded-lg p-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Recent Wins</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Completed</span>
              <span className="font-semibold">{stats.completed}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">This Month</span>
              <span className="font-semibold">
                {tasksByStatus[TaskStatus.COMPLETED].filter(task => {
                  const completedDate = new Date(task.updatedAt || task.createdAt);
                  const thisMonth = new Date();
                  return completedDate.getMonth() === thisMonth.getMonth() &&
                         completedDate.getFullYear() === thisMonth.getFullYear();
                }).length}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Workflow Guidance */}
      {filter.status && (
        <div className="bg-gradient-to-r from-primary-50 to-purple-50 rounded-xl border border-primary-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="bg-primary-100 rounded-lg p-2">
                {workflowStages.find(s => s.key === filter.status)?.icon &&
                  React.createElement(workflowStages.find(s => s.key === filter.status).icon, {
                    className: "w-5 h-5 text-primary-600"
                  })
                }
              </div>
              <h3 className="font-semibold text-gray-900">
                {workflowStages.find(s => s.key === filter.status)?.title} Stage
              </h3>
            </div>
            <button
              onClick={() => {
                setFilter(prev => ({ ...prev, status: '' }));
                // Switch back to dashboard view when clearing filter
                if (onViewModeChange) {
                  onViewModeChange('dashboard');
                }
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              <XIcon className="w-5 h-5" />
            </button>
          </div>
          <p className="text-gray-700 mb-4">
            {workflowStages.find(s => s.key === filter.status)?.description}
          </p>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleNewIdeaClick}
              className="bg-white text-primary-700 border border-primary-200 px-4 py-2 rounded-lg font-medium hover:bg-primary-50 transition-colors"
            >
              Add New Task
            </button>
            {workflowStages.find(s => s.key === filter.status)?.nextStage && (
              <span className="text-sm text-gray-600">
                Next: Move tasks to {workflowStages.find(s => s.key === filter.status)?.nextStage}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
