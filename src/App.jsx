import { useState, useEffect } from 'react';
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import TaskList from './components/TaskList';
import TaskForm from './components/TaskForm';
import OnboardingGuide from './components/OnboardingGuide';
import AIAssistant from './components/AIAssistant';
import AuthModal from './components/Auth/AuthModal';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { useSupabaseTasks } from './hooks/useSupabaseTasks';
import { Loader2 } from 'lucide-react';

// Main App component with authentication
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

// App content that requires authentication
function AppContent() {
  const { user, loading: authLoading } = useAuth();
  const {
    tasks,
    loading: tasksLoading,
    filter,
    setFilter,
    createTask,
    updateTask,
    deleteTask,
    toggleFollowUp,
    toggleProcessStep,
    getTaskStats
  } = useSupabaseTasks();

  // Debug logging
  useEffect(() => {
    console.log('AppContent render - User:', user?.id || 'No user', 'AuthLoading:', authLoading, 'TasksLoading:', tasksLoading, 'Tasks count:', tasks.length)
  }, [user, authLoading, tasksLoading, tasks.length])

  const [showTaskForm, setShowTaskForm] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [viewMode, setViewMode] = useState('dashboard'); // 'dashboard' or 'list'
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [currentTask, setCurrentTask] = useState(null);
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Show loading spinner while authentication is loading
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show authentication modal if user is not logged in
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="mx-auto w-24 h-24 bg-gradient-to-br from-primary-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
            <span className="text-4xl">💡</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-3">Welcome to Idea Manager</h1>
          <p className="text-gray-600 mb-8">
            Transform your ideas into reality with our structured workflow. Sign in to get started.
          </p>
          <button
            onClick={() => setShowAuthModal(true)}
            className="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            Get Started
          </button>
        </div>

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          initialMode="signin"
        />
      </div>
    );
  }

  const stats = getTaskStats();

  // Show loading spinner while tasks are loading
  if (tasksLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header
          onNewTask={() => {}}
          filter={filter}
          setFilter={setFilter}
          stats={{ total: 0, completed: 0, inProgress: 0, ideas: 0 }}
          viewMode={viewMode}
          setViewMode={setViewMode}
        />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-16">
            <Loader2 className="w-8 h-8 animate-spin text-primary-600 mx-auto mb-4" />
            <p className="text-gray-600">Loading your ideas...</p>
          </div>
        </main>
      </div>
    );
  }

  // Show onboarding for first-time users
  useEffect(() => {
    const hasSeenOnboarding = localStorage.getItem('hasSeenOnboarding');
    if (!hasSeenOnboarding && tasks.length === 0 && !tasksLoading) {
      setShowOnboarding(true);
    }
  }, [tasks.length, tasksLoading]);

  const handleNewTask = () => {
    setEditingTask(null);
    setShowTaskForm(true);
  };

  const handleEditTask = (task) => {
    setEditingTask(task);
    setCurrentTask(task);
    setShowTaskForm(true);
  };

  const handleSaveTask = (taskData) => {
    if (editingTask) {
      updateTask(editingTask.id, taskData);
    } else {
      createTask(taskData);
    }
    setShowTaskForm(false);
    setEditingTask(null);
  };

  const handleCloseForm = () => {
    setShowTaskForm(false);
    setEditingTask(null);
  };

  const handleDeleteTask = (taskId) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      deleteTask(taskId);
    }
  };

  const handleCloseOnboarding = () => {
    setShowOnboarding(false);
    localStorage.setItem('hasSeenOnboarding', 'true');
  };

  const handleCreateFirstTask = () => {
    setShowOnboarding(false);
    localStorage.setItem('hasSeenOnboarding', 'true');
    handleNewTask();
  };

  const handleTaskSuggestions = (suggestions) => {
    // For now, just create the first suggestion as a new task
    if (suggestions.length > 0) {
      const suggestion = suggestions[0];
      createTask({
        title: suggestion.title,
        description: suggestion.description,
        priority: suggestion.priority,
        category: suggestion.category,
        estimatedHours: suggestion.estimatedHours || 0
      });
    }
  };

  const handleImproveDescription = (taskId, improvedDescription) => {
    updateTask(taskId, { description: improvedDescription });
  };

  const handleGenerateSteps = (taskId, steps) => {
    const newSteps = steps.map((step, index) => ({
      id: Date.now().toString() + index,
      title: step.title,
      description: step.description,
      completed: false,
      order: index
    }));

    updateTask(taskId, { processSteps: newSteps });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header
        onNewTask={handleNewTask}
        filter={filter}
        setFilter={setFilter}
        stats={stats}
        viewMode={viewMode}
        setViewMode={setViewMode}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Empty State with Better UX */}
        {tasks.length === 0 && !Object.values(filter).some(Boolean) ? (
          <div className="text-center py-16">
            <div className="mx-auto w-32 h-32 bg-gradient-to-br from-primary-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-5xl">💡</span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">Ready to innovate?</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Start your innovation journey by capturing your first idea. Every great project begins with a single thought.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleNewTask}
                className="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-4 rounded-xl font-medium text-lg transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Create Your First Idea
              </button>
              <button
                onClick={() => setShowOnboarding(true)}
                className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 rounded-xl font-medium text-lg transition-all duration-200"
              >
                Take a Tour
              </button>
            </div>
          </div>
        ) : (
          <>
            {/* Main Content */}
            {viewMode === 'dashboard' ? (
              <Dashboard
                tasks={tasks}
                onNewTask={handleNewTask}
                onEditTask={handleEditTask}
                filter={filter}
                setFilter={setFilter}
                stats={stats}
                onViewModeChange={setViewMode}
              />
            ) : (
              <TaskList
                tasks={tasks}
                onEdit={handleEditTask}
                onDelete={handleDeleteTask}
                onToggleFollowUp={toggleFollowUp}
                onToggleProcessStep={toggleProcessStep}
                onNewTask={handleNewTask}
                filter={filter}
              />
            )}
          </>
        )}
      </main>

      {/* Modals */}
      {showTaskForm && (
        <TaskForm
          task={editingTask}
          onSave={handleSaveTask}
          onClose={handleCloseForm}
        />
      )}

      {showOnboarding && (
        <OnboardingGuide
          onClose={handleCloseOnboarding}
          onCreateFirstTask={handleCreateFirstTask}
        />
      )}

      {/* AI Assistant */}
      <AIAssistant
        tasks={tasks}
        currentTask={currentTask}
        onTaskSuggestions={handleTaskSuggestions}
        onImproveDescription={handleImproveDescription}
        onGenerateSteps={handleGenerateSteps}
      />
    </div>
  );
}

export default App;
