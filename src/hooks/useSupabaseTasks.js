import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { TaskStatus, TaskPriority } from '../types/task'
import { useAuth } from '../contexts/AuthContext'
import { migrateLocalStorageData } from '../lib/migrations'

export const useSupabaseTasks = () => {
  const { user } = useAuth()
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState({
    status: '',
    priority: '',
    category: '',
    search: ''
  })

  // Load tasks from Supabase
  const loadTasks = async () => {
    if (!user) {
      setTasks([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)

      // Fetch tasks with related data
      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select(`
          *,
          follow_ups (*),
          process_steps (*),
          attachments (*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (tasksError) throw tasksError

      // Transform data to match existing structure
      const transformedTasks = tasksData.map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        category: task.category,
        tags: task.tags || [],
        startDate: task.start_date,
        dueDate: task.due_date,
        estimatedHours: task.estimated_hours,
        actualHours: task.actual_hours,
        progress: task.progress,
        followUps: task.follow_ups?.map(fu => ({
          id: fu.id,
          description: fu.description,
          dueDate: fu.due_date,
          completed: fu.completed,
          createdAt: fu.created_at
        })) || [],
        processSteps: task.process_steps?.map(ps => ({
          id: ps.id,
          title: ps.title,
          description: ps.description,
          completed: ps.completed,
          order: ps.step_order,
          createdAt: ps.created_at
        })) || [],
        attachments: task.attachments || [],
        createdAt: task.created_at,
        updatedAt: task.updated_at
      }))

      setTasks(transformedTasks)
    } catch (error) {
      console.error('Error loading tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load tasks when user changes and handle migration
  useEffect(() => {
    const initializeUserData = async () => {
      if (!user) return

      // Check if user has any tasks in Supabase
      const { data: existingTasks } = await supabase
        .from('tasks')
        .select('id')
        .eq('user_id', user.id)
        .limit(1)

      // If no tasks in Supabase but localStorage has data, migrate it
      if ((!existingTasks || existingTasks.length === 0)) {
        try {
          await migrateLocalStorageData(user.id)
        } catch (error) {
          console.error('Migration failed:', error)
        }
      }

      // Load tasks after potential migration
      await loadTasks()
    }

    initializeUserData()
  }, [user])

  const createTask = async (taskData) => {
    if (!user) return null

    try {
      const newTask = {
        user_id: user.id,
        title: taskData.title,
        description: taskData.description || '',
        status: taskData.status || TaskStatus.IDEA,
        priority: taskData.priority || TaskPriority.MEDIUM,
        category: taskData.category || 'other',
        tags: taskData.tags || [],
        start_date: taskData.startDate || null,
        due_date: taskData.dueDate || null,
        estimated_hours: taskData.estimatedHours || 0,
        actual_hours: taskData.actualHours || 0,
        progress: taskData.progress || 0
      }

      const { data, error } = await supabase
        .from('tasks')
        .insert([newTask])
        .select()
        .single()

      if (error) throw error

      // Transform and add to local state
      const transformedTask = {
        id: data.id,
        title: data.title,
        description: data.description,
        status: data.status,
        priority: data.priority,
        category: data.category,
        tags: data.tags || [],
        startDate: data.start_date,
        dueDate: data.due_date,
        estimatedHours: data.estimated_hours,
        actualHours: data.actual_hours,
        progress: data.progress,
        followUps: [],
        processSteps: [],
        attachments: [],
        createdAt: data.created_at,
        updatedAt: data.updated_at
      }

      setTasks(prev => [transformedTask, ...prev])
      return transformedTask
    } catch (error) {
      console.error('Error creating task:', error)
      return null
    }
  }

  const updateTask = async (taskId, updates) => {
    if (!user) return

    try {
      // Transform updates to match database schema
      const dbUpdates = {}
      if (updates.title !== undefined) dbUpdates.title = updates.title
      if (updates.description !== undefined) dbUpdates.description = updates.description
      if (updates.status !== undefined) dbUpdates.status = updates.status
      if (updates.priority !== undefined) dbUpdates.priority = updates.priority
      if (updates.category !== undefined) dbUpdates.category = updates.category
      if (updates.tags !== undefined) dbUpdates.tags = updates.tags
      if (updates.startDate !== undefined) dbUpdates.start_date = updates.startDate
      if (updates.dueDate !== undefined) dbUpdates.due_date = updates.dueDate
      if (updates.estimatedHours !== undefined) dbUpdates.estimated_hours = updates.estimatedHours
      if (updates.actualHours !== undefined) dbUpdates.actual_hours = updates.actualHours
      if (updates.progress !== undefined) dbUpdates.progress = updates.progress

      const { error } = await supabase
        .from('tasks')
        .update(dbUpdates)
        .eq('id', taskId)
        .eq('user_id', user.id)

      if (error) throw error

      // Update local state
      setTasks(prev => prev.map(task =>
        task.id === taskId
          ? { ...task, ...updates, updatedAt: new Date().toISOString() }
          : task
      ))
    } catch (error) {
      console.error('Error updating task:', error)
    }
  }

  const deleteTask = async (taskId) => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', user.id)

      if (error) throw error

      setTasks(prev => prev.filter(task => task.id !== taskId))
    } catch (error) {
      console.error('Error deleting task:', error)
    }
  }

  const addFollowUp = async (taskId, followUp) => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('follow_ups')
        .insert([{
          task_id: taskId,
          description: followUp.description,
          due_date: followUp.dueDate,
          completed: false
        }])
        .select()
        .single()

      if (error) throw error

      // Update local state
      const newFollowUp = {
        id: data.id,
        description: data.description,
        dueDate: data.due_date,
        completed: data.completed,
        createdAt: data.created_at
      }

      setTasks(prev => prev.map(task =>
        task.id === taskId
          ? { ...task, followUps: [...task.followUps, newFollowUp] }
          : task
      ))
    } catch (error) {
      console.error('Error adding follow-up:', error)
    }
  }

  const toggleFollowUp = async (taskId, followUpId) => {
    if (!user) return

    try {
      const task = tasks.find(t => t.id === taskId)
      const followUp = task?.followUps.find(fu => fu.id === followUpId)

      if (!followUp) return

      const { error } = await supabase
        .from('follow_ups')
        .update({ completed: !followUp.completed })
        .eq('id', followUpId)

      if (error) throw error

      // Update local state
      setTasks(prev => prev.map(task =>
        task.id === taskId
          ? {
              ...task,
              followUps: task.followUps.map(fu =>
                fu.id === followUpId ? { ...fu, completed: !fu.completed } : fu
              )
            }
          : task
      ))
    } catch (error) {
      console.error('Error toggling follow-up:', error)
    }
  }

  const addProcessStep = async (taskId, step) => {
    if (!user) return

    try {
      const task = tasks.find(t => t.id === taskId)
      const stepOrder = task?.processSteps.length || 0

      const { data, error } = await supabase
        .from('process_steps')
        .insert([{
          task_id: taskId,
          title: step.title,
          description: step.description,
          completed: false,
          step_order: stepOrder
        }])
        .select()
        .single()

      if (error) throw error

      // Update local state
      const newStep = {
        id: data.id,
        title: data.title,
        description: data.description,
        completed: data.completed,
        order: data.step_order,
        createdAt: data.created_at
      }

      setTasks(prev => prev.map(task =>
        task.id === taskId
          ? { ...task, processSteps: [...task.processSteps, newStep] }
          : task
      ))
    } catch (error) {
      console.error('Error adding process step:', error)
    }
  }

  const toggleProcessStep = async (taskId, stepId) => {
    if (!user) return

    try {
      const task = tasks.find(t => t.id === taskId)
      const step = task?.processSteps.find(ps => ps.id === stepId)

      if (!step) return

      const { error } = await supabase
        .from('process_steps')
        .update({ completed: !step.completed })
        .eq('id', stepId)

      if (error) throw error

      // Update local state
      setTasks(prev => prev.map(task =>
        task.id === taskId
          ? {
              ...task,
              processSteps: task.processSteps.map(ps =>
                ps.id === stepId ? { ...ps, completed: !ps.completed } : ps
              )
            }
          : task
      ))
    } catch (error) {
      console.error('Error toggling process step:', error)
    }
  }

  const getFilteredTasks = () => {
    return tasks.filter(task => {
      const matchesStatus = !filter.status || task.status === filter.status
      const matchesPriority = !filter.priority || task.priority === filter.priority
      const matchesCategory = !filter.category || task.category === filter.category
      const matchesSearch = !filter.search ||
        task.title.toLowerCase().includes(filter.search.toLowerCase()) ||
        task.description.toLowerCase().includes(filter.search.toLowerCase()) ||
        task.tags.some(tag => tag.toLowerCase().includes(filter.search.toLowerCase()))

      return matchesStatus && matchesPriority && matchesCategory && matchesSearch
    })
  }

  const getTaskStats = () => {
    const total = tasks.length
    const completed = tasks.filter(t => t.status === TaskStatus.COMPLETED).length
    const inProgress = tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length
    const ideas = tasks.filter(t => t.status === TaskStatus.IDEA).length

    return { total, completed, inProgress, ideas }
  }

  return {
    tasks: getFilteredTasks(),
    allTasks: tasks,
    loading,
    filter,
    setFilter,
    createTask,
    updateTask,
    deleteTask,
    addFollowUp,
    toggleFollowUp,
    addProcessStep,
    toggleProcessStep,
    getTaskStats,
    refreshTasks: loadTasks
  }
}
