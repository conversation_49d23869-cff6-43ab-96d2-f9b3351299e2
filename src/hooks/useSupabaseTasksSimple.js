import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { TaskStatus, TaskPriority } from '../types/task'
import { useAuth } from '../contexts/AuthContext'

// Simplified version without migration for debugging
export const useSupabaseTasksSimple = () => {
  const { user } = useAuth()
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState({
    status: '',
    priority: '',
    category: '',
    search: ''
  })

  // Load tasks from Supabase
  const loadTasks = async () => {
    if (!user) {
      console.log('No user, clearing tasks')
      setTasks([])
      setLoading(false)
      return
    }

    try {
      console.log('Loading tasks for user:', user.id)
      setLoading(true)
      
      // Just fetch tasks without related data first
      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (tasksError) {
        console.error('Error loading tasks:', tasksError)
        throw tasksError
      }

      console.log('Loaded tasks:', tasksData?.length || 0)

      // Transform data to match existing structure (simplified)
      const transformedTasks = tasksData.map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        category: task.category,
        tags: task.tags || [],
        startDate: task.start_date,
        dueDate: task.due_date,
        estimatedHours: task.estimated_hours,
        actualHours: task.actual_hours,
        progress: task.progress,
        followUps: [], // Simplified - no related data for now
        processSteps: [], // Simplified - no related data for now
        attachments: [],
        createdAt: task.created_at,
        updatedAt: task.updated_at
      }))

      setTasks(transformedTasks)
    } catch (error) {
      console.error('Error loading tasks:', error)
      setTasks([]) // Set empty array on error
    } finally {
      setLoading(false)
    }
  }

  // Simple effect - just load tasks when user changes
  useEffect(() => {
    console.log('User changed, loading tasks:', user?.id || 'No user')
    loadTasks()
  }, [user])

  const createTask = async (taskData) => {
    if (!user) return null

    try {
      const newTask = {
        user_id: user.id,
        title: taskData.title,
        description: taskData.description || '',
        status: taskData.status || TaskStatus.IDEA,
        priority: taskData.priority || TaskPriority.MEDIUM,
        category: taskData.category || 'other',
        tags: taskData.tags || [],
        start_date: taskData.startDate || null,
        due_date: taskData.dueDate || null,
        estimated_hours: taskData.estimatedHours || 0,
        actual_hours: taskData.actualHours || 0,
        progress: taskData.progress || 0
      }

      const { data, error } = await supabase
        .from('tasks')
        .insert([newTask])
        .select()
        .single()

      if (error) throw error

      // Transform and add to local state
      const transformedTask = {
        id: data.id,
        title: data.title,
        description: data.description,
        status: data.status,
        priority: data.priority,
        category: data.category,
        tags: data.tags || [],
        startDate: data.start_date,
        dueDate: data.due_date,
        estimatedHours: data.estimated_hours,
        actualHours: data.actual_hours,
        progress: data.progress,
        followUps: [],
        processSteps: [],
        attachments: [],
        createdAt: data.created_at,
        updatedAt: data.updated_at
      }

      setTasks(prev => [transformedTask, ...prev])
      return transformedTask
    } catch (error) {
      console.error('Error creating task:', error)
      return null
    }
  }

  const updateTask = async (taskId, updates) => {
    if (!user) return

    try {
      // Transform updates to match database schema
      const dbUpdates = {}
      if (updates.title !== undefined) dbUpdates.title = updates.title
      if (updates.description !== undefined) dbUpdates.description = updates.description
      if (updates.status !== undefined) dbUpdates.status = updates.status
      if (updates.priority !== undefined) dbUpdates.priority = updates.priority
      if (updates.category !== undefined) dbUpdates.category = updates.category
      if (updates.tags !== undefined) dbUpdates.tags = updates.tags
      if (updates.startDate !== undefined) dbUpdates.start_date = updates.startDate
      if (updates.dueDate !== undefined) dbUpdates.due_date = updates.dueDate
      if (updates.estimatedHours !== undefined) dbUpdates.estimated_hours = updates.estimatedHours
      if (updates.actualHours !== undefined) dbUpdates.actual_hours = updates.actualHours
      if (updates.progress !== undefined) dbUpdates.progress = updates.progress

      const { error } = await supabase
        .from('tasks')
        .update(dbUpdates)
        .eq('id', taskId)
        .eq('user_id', user.id)

      if (error) throw error

      // Update local state
      setTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, ...updates, updatedAt: new Date().toISOString() }
          : task
      ))
    } catch (error) {
      console.error('Error updating task:', error)
    }
  }

  const deleteTask = async (taskId) => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', user.id)

      if (error) throw error

      setTasks(prev => prev.filter(task => task.id !== taskId))
    } catch (error) {
      console.error('Error deleting task:', error)
    }
  }

  // Simplified versions of these functions for now
  const addFollowUp = async (taskId, followUp) => {
    console.log('addFollowUp not implemented in simple version')
  }

  const toggleFollowUp = async (taskId, followUpId) => {
    console.log('toggleFollowUp not implemented in simple version')
  }

  const addProcessStep = async (taskId, step) => {
    console.log('addProcessStep not implemented in simple version')
  }

  const toggleProcessStep = async (taskId, stepId) => {
    console.log('toggleProcessStep not implemented in simple version')
  }

  const getFilteredTasks = () => {
    return tasks.filter(task => {
      const matchesStatus = !filter.status || task.status === filter.status
      const matchesPriority = !filter.priority || task.priority === filter.priority
      const matchesCategory = !filter.category || task.category === filter.category
      const matchesSearch = !filter.search || 
        task.title.toLowerCase().includes(filter.search.toLowerCase()) ||
        task.description.toLowerCase().includes(filter.search.toLowerCase()) ||
        task.tags.some(tag => tag.toLowerCase().includes(filter.search.toLowerCase()))

      return matchesStatus && matchesPriority && matchesCategory && matchesSearch
    })
  }

  const getTaskStats = () => {
    const total = tasks.length
    const completed = tasks.filter(t => t.status === TaskStatus.COMPLETED).length
    const inProgress = tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length
    const ideas = tasks.filter(t => t.status === TaskStatus.IDEA).length
    
    return { total, completed, inProgress, ideas }
  }

  return {
    tasks: getFilteredTasks(),
    allTasks: tasks,
    loading,
    filter,
    setFilter,
    createTask,
    updateTask,
    deleteTask,
    addFollowUp,
    toggleFollowUp,
    addProcessStep,
    toggleProcessStep,
    getTaskStats,
    refreshTasks: loadTasks
  }
}
