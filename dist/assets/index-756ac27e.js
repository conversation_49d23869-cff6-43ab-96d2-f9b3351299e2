(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function r(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function n(s){if(s.ep)return;s.ep=!0;const i=r(s);fetch(s.href,i)}})();var We=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Uf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Ff(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function n(){return this instanceof n?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var s=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,s.get?s:{enumerable:!0,get:function(){return e[n]}})}),r}var Lc={exports:{}},ji={},Mc={exports:{}},L={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ls=Symbol.for("react.element"),zf=Symbol.for("react.portal"),Hf=Symbol.for("react.fragment"),Bf=Symbol.for("react.strict_mode"),Wf=Symbol.for("react.profiler"),Gf=Symbol.for("react.provider"),Vf=Symbol.for("react.context"),qf=Symbol.for("react.forward_ref"),Yf=Symbol.for("react.suspense"),Kf=Symbol.for("react.memo"),Jf=Symbol.for("react.lazy"),Ml=Symbol.iterator;function Qf(e){return e===null||typeof e!="object"?null:(e=Ml&&e[Ml]||e["@@iterator"],typeof e=="function"?e:null)}var $c={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Uc=Object.assign,Fc={};function ln(e,t,r){this.props=e,this.context=t,this.refs=Fc,this.updater=r||$c}ln.prototype.isReactComponent={};ln.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ln.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function zc(){}zc.prototype=ln.prototype;function Da(e,t,r){this.props=e,this.context=t,this.refs=Fc,this.updater=r||$c}var La=Da.prototype=new zc;La.constructor=Da;Uc(La,ln.prototype);La.isPureReactComponent=!0;var $l=Array.isArray,Hc=Object.prototype.hasOwnProperty,Ma={current:null},Bc={key:!0,ref:!0,__self:!0,__source:!0};function Wc(e,t,r){var n,s={},i=null,o=null;if(t!=null)for(n in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Hc.call(t,n)&&!Bc.hasOwnProperty(n)&&(s[n]=t[n]);var a=arguments.length-2;if(a===1)s.children=r;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(n in a=e.defaultProps,a)s[n]===void 0&&(s[n]=a[n]);return{$$typeof:ls,type:e,key:i,ref:o,props:s,_owner:Ma.current}}function Xf(e,t){return{$$typeof:ls,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function $a(e){return typeof e=="object"&&e!==null&&e.$$typeof===ls}function Zf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Ul=/\/+/g;function Xi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Zf(""+e.key):t.toString(36)}function $s(e,t,r,n,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ls:case zf:o=!0}}if(o)return o=e,s=s(o),e=n===""?"."+Xi(o,0):n,$l(s)?(r="",e!=null&&(r=e.replace(Ul,"$&/")+"/"),$s(s,t,r,"",function(c){return c})):s!=null&&($a(s)&&(s=Xf(s,r+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Ul,"$&/")+"/")+e)),t.push(s)),1;if(o=0,n=n===""?".":n+":",$l(e))for(var a=0;a<e.length;a++){i=e[a];var u=n+Xi(i,a);o+=$s(i,t,r,u,s)}else if(u=Qf(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=n+Xi(i,a++),o+=$s(i,t,r,u,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function ys(e,t,r){if(e==null)return e;var n=[],s=0;return $s(e,n,"","",function(i){return t.call(r,i,s++)}),n}function ep(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var _e={current:null},Us={transition:null},tp={ReactCurrentDispatcher:_e,ReactCurrentBatchConfig:Us,ReactCurrentOwner:Ma};function Gc(){throw Error("act(...) is not supported in production builds of React.")}L.Children={map:ys,forEach:function(e,t,r){ys(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return ys(e,function(){t++}),t},toArray:function(e){return ys(e,function(t){return t})||[]},only:function(e){if(!$a(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};L.Component=ln;L.Fragment=Hf;L.Profiler=Wf;L.PureComponent=Da;L.StrictMode=Bf;L.Suspense=Yf;L.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tp;L.act=Gc;L.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=Uc({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Ma.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)Hc.call(t,u)&&!Bc.hasOwnProperty(u)&&(n[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)n.children=r;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];n.children=a}return{$$typeof:ls,type:e.type,key:s,ref:i,props:n,_owner:o}};L.createContext=function(e){return e={$$typeof:Vf,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Gf,_context:e},e.Consumer=e};L.createElement=Wc;L.createFactory=function(e){var t=Wc.bind(null,e);return t.type=e,t};L.createRef=function(){return{current:null}};L.forwardRef=function(e){return{$$typeof:qf,render:e}};L.isValidElement=$a;L.lazy=function(e){return{$$typeof:Jf,_payload:{_status:-1,_result:e},_init:ep}};L.memo=function(e,t){return{$$typeof:Kf,type:e,compare:t===void 0?null:t}};L.startTransition=function(e){var t=Us.transition;Us.transition={};try{e()}finally{Us.transition=t}};L.unstable_act=Gc;L.useCallback=function(e,t){return _e.current.useCallback(e,t)};L.useContext=function(e){return _e.current.useContext(e)};L.useDebugValue=function(){};L.useDeferredValue=function(e){return _e.current.useDeferredValue(e)};L.useEffect=function(e,t){return _e.current.useEffect(e,t)};L.useId=function(){return _e.current.useId()};L.useImperativeHandle=function(e,t,r){return _e.current.useImperativeHandle(e,t,r)};L.useInsertionEffect=function(e,t){return _e.current.useInsertionEffect(e,t)};L.useLayoutEffect=function(e,t){return _e.current.useLayoutEffect(e,t)};L.useMemo=function(e,t){return _e.current.useMemo(e,t)};L.useReducer=function(e,t,r){return _e.current.useReducer(e,t,r)};L.useRef=function(e){return _e.current.useRef(e)};L.useState=function(e){return _e.current.useState(e)};L.useSyncExternalStore=function(e,t,r){return _e.current.useSyncExternalStore(e,t,r)};L.useTransition=function(){return _e.current.useTransition()};L.version="18.3.1";Mc.exports=L;var I=Mc.exports;const Vc=Uf(I);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rp=I,np=Symbol.for("react.element"),sp=Symbol.for("react.fragment"),ip=Object.prototype.hasOwnProperty,op=rp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ap={key:!0,ref:!0,__self:!0,__source:!0};function qc(e,t,r){var n,s={},i=null,o=null;r!==void 0&&(i=""+r),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(n in t)ip.call(t,n)&&!ap.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)s[n]===void 0&&(s[n]=t[n]);return{$$typeof:np,type:e,key:i,ref:o,props:s,_owner:op.current}}ji.Fragment=sp;ji.jsx=qc;ji.jsxs=qc;Lc.exports=ji;var l=Lc.exports,Io={},Yc={exports:{}},Me={},Kc={exports:{}},Jc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,R){var A=T.length;T.push(R);e:for(;0<A;){var q=A-1>>>1,ee=T[q];if(0<s(ee,R))T[q]=R,T[A]=ee,A=q;else break e}}function r(T){return T.length===0?null:T[0]}function n(T){if(T.length===0)return null;var R=T[0],A=T.pop();if(A!==R){T[0]=A;e:for(var q=0,ee=T.length,Xt=ee>>>1;q<Xt;){var Zt=2*(q+1)-1,Qi=T[Zt],er=Zt+1,gs=T[er];if(0>s(Qi,A))er<ee&&0>s(gs,Qi)?(T[q]=gs,T[er]=A,q=er):(T[q]=Qi,T[Zt]=A,q=Zt);else if(er<ee&&0>s(gs,A))T[q]=gs,T[er]=A,q=er;else break e}}return R}function s(T,R){var A=T.sortIndex-R.sortIndex;return A!==0?A:T.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var u=[],c=[],d=1,p=null,m=3,y=!1,w=!1,x=!1,k=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(T){for(var R=r(c);R!==null;){if(R.callback===null)n(c);else if(R.startTime<=T)n(c),R.sortIndex=R.expirationTime,t(u,R);else break;R=r(c)}}function _(T){if(x=!1,g(T),!w)if(r(u)!==null)w=!0,Se(S);else{var R=r(c);R!==null&&tt(_,R.startTime-T)}}function S(T,R){w=!1,x&&(x=!1,f(v),v=-1),y=!0;var A=m;try{for(g(R),p=r(u);p!==null&&(!(p.expirationTime>R)||T&&!X());){var q=p.callback;if(typeof q=="function"){p.callback=null,m=p.priorityLevel;var ee=q(p.expirationTime<=R);R=e.unstable_now(),typeof ee=="function"?p.callback=ee:p===r(u)&&n(u),g(R)}else n(u);p=r(u)}if(p!==null)var Xt=!0;else{var Zt=r(c);Zt!==null&&tt(_,Zt.startTime-R),Xt=!1}return Xt}finally{p=null,m=A,y=!1}}var b=!1,E=null,v=-1,j=5,C=-1;function X(){return!(e.unstable_now()-C<j)}function lt(){if(E!==null){var T=e.unstable_now();C=T;var R=!0;try{R=E(!0,T)}finally{R?ut():(b=!1,E=null)}}else b=!1}var ut;if(typeof h=="function")ut=function(){h(lt)};else if(typeof MessageChannel<"u"){var Sr=new MessageChannel,ue=Sr.port2;Sr.port1.onmessage=lt,ut=function(){ue.postMessage(null)}}else ut=function(){k(lt,0)};function Se(T){E=T,b||(b=!0,ut())}function tt(T,R){v=k(function(){T(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){w||y||(w=!0,Se(S))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return r(u)},e.unstable_next=function(T){switch(m){case 1:case 2:case 3:var R=3;break;default:R=m}var A=m;m=R;try{return T()}finally{m=A}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,R){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var A=m;m=T;try{return R()}finally{m=A}},e.unstable_scheduleCallback=function(T,R,A){var q=e.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?q+A:q):A=q,T){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=A+ee,T={id:d++,callback:R,priorityLevel:T,startTime:A,expirationTime:ee,sortIndex:-1},A>q?(T.sortIndex=A,t(c,T),r(u)===null&&T===r(c)&&(x?(f(v),v=-1):x=!0,tt(_,A-q))):(T.sortIndex=ee,t(u,T),w||y||(w=!0,Se(S))),T},e.unstable_shouldYield=X,e.unstable_wrapCallback=function(T){var R=m;return function(){var A=m;m=R;try{return T.apply(this,arguments)}finally{m=A}}}})(Jc);Kc.exports=Jc;var lp=Kc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var up=I,Le=lp;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Qc=new Set,Hn={};function xr(e,t){Qr(e,t),Qr(e+"Capture",t)}function Qr(e,t){for(Hn[e]=t,e=0;e<t.length;e++)Qc.add(t[e])}var gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ao=Object.prototype.hasOwnProperty,cp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Fl={},zl={};function dp(e){return Ao.call(zl,e)?!0:Ao.call(Fl,e)?!1:cp.test(e)?zl[e]=!0:(Fl[e]=!0,!1)}function hp(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function fp(e,t,r,n){if(t===null||typeof t>"u"||hp(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ke(e,t,r,n,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=s,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var he={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){he[e]=new ke(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];he[t]=new ke(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){he[e]=new ke(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){he[e]=new ke(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){he[e]=new ke(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){he[e]=new ke(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){he[e]=new ke(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){he[e]=new ke(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){he[e]=new ke(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ua=/[\-:]([a-z])/g;function Fa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ua,Fa);he[t]=new ke(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ua,Fa);he[t]=new ke(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ua,Fa);he[t]=new ke(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){he[e]=new ke(e,1,!1,e.toLowerCase(),null,!1,!1)});he.xlinkHref=new ke("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){he[e]=new ke(e,1,!1,e.toLowerCase(),null,!0,!0)});function za(e,t,r,n){var s=he.hasOwnProperty(t)?he[t]:null;(s!==null?s.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(fp(t,r,s,n)&&(r=null),n||s===null?dp(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):s.mustUseProperty?e[s.propertyName]=r===null?s.type===3?!1:"":r:(t=s.attributeName,n=s.attributeNamespace,r===null?e.removeAttribute(t):(s=s.type,r=s===3||s===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var _t=up.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,vs=Symbol.for("react.element"),Pr=Symbol.for("react.portal"),Rr=Symbol.for("react.fragment"),Ha=Symbol.for("react.strict_mode"),Do=Symbol.for("react.profiler"),Xc=Symbol.for("react.provider"),Zc=Symbol.for("react.context"),Ba=Symbol.for("react.forward_ref"),Lo=Symbol.for("react.suspense"),Mo=Symbol.for("react.suspense_list"),Wa=Symbol.for("react.memo"),jt=Symbol.for("react.lazy"),ed=Symbol.for("react.offscreen"),Hl=Symbol.iterator;function fn(e){return e===null||typeof e!="object"?null:(e=Hl&&e[Hl]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,Zi;function En(e){if(Zi===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);Zi=t&&t[1]||""}return`
`+Zi+e}var eo=!1;function to(e,t){if(!e||eo)return"";eo=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var n=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){n=c}e.call(t.prototype)}else{try{throw Error()}catch(c){n=c}e()}}catch(c){if(c&&n&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),i=n.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var u=`
`+s[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=a);break}}}finally{eo=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?En(e):""}function pp(e){switch(e.tag){case 5:return En(e.type);case 16:return En("Lazy");case 13:return En("Suspense");case 19:return En("SuspenseList");case 0:case 2:case 15:return e=to(e.type,!1),e;case 11:return e=to(e.type.render,!1),e;case 1:return e=to(e.type,!0),e;default:return""}}function $o(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Rr:return"Fragment";case Pr:return"Portal";case Do:return"Profiler";case Ha:return"StrictMode";case Lo:return"Suspense";case Mo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Zc:return(e.displayName||"Context")+".Consumer";case Xc:return(e._context.displayName||"Context")+".Provider";case Ba:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Wa:return t=e.displayName||null,t!==null?t:$o(e.type)||"Memo";case jt:t=e._payload,e=e._init;try{return $o(e(t))}catch{}}return null}function mp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $o(t);case 8:return t===Ha?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Vt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function td(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function gp(e){var t=td(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var s=r.get,i=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){n=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(o){n=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ws(e){e._valueTracker||(e._valueTracker=gp(e))}function rd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=td(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function Qs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Uo(e,t){var r=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Bl(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=Vt(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function nd(e,t){t=t.checked,t!=null&&za(e,"checked",t,!1)}function Fo(e,t){nd(e,t);var r=Vt(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?zo(e,t.type,r):t.hasOwnProperty("defaultValue")&&zo(e,t.type,Vt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Wl(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function zo(e,t,r){(t!=="number"||Qs(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var bn=Array.isArray;function Wr(e,t,r,n){if(e=e.options,t){t={};for(var s=0;s<r.length;s++)t["$"+r[s]]=!0;for(r=0;r<e.length;r++)s=t.hasOwnProperty("$"+e[r].value),e[r].selected!==s&&(e[r].selected=s),s&&n&&(e[r].defaultSelected=!0)}else{for(r=""+Vt(r),t=null,s=0;s<e.length;s++){if(e[s].value===r){e[s].selected=!0,n&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Ho(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Gl(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(N(92));if(bn(r)){if(1<r.length)throw Error(N(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Vt(r)}}function sd(e,t){var r=Vt(t.value),n=Vt(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function Vl(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function id(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Bo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?id(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var xs,od=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,s){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(xs=xs||document.createElement("div"),xs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=xs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Bn(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Tn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},yp=["Webkit","ms","Moz","O"];Object.keys(Tn).forEach(function(e){yp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Tn[t]=Tn[e]})});function ad(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Tn.hasOwnProperty(e)&&Tn[e]?(""+t).trim():t+"px"}function ld(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,s=ad(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,s):e[r]=s}}var vp=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wo(e,t){if(t){if(vp[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function Go(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Vo=null;function Ga(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var qo=null,Gr=null,Vr=null;function ql(e){if(e=ds(e)){if(typeof qo!="function")throw Error(N(280));var t=e.stateNode;t&&(t=Pi(t),qo(e.stateNode,e.type,t))}}function ud(e){Gr?Vr?Vr.push(e):Vr=[e]:Gr=e}function cd(){if(Gr){var e=Gr,t=Vr;if(Vr=Gr=null,ql(e),t)for(e=0;e<t.length;e++)ql(t[e])}}function dd(e,t){return e(t)}function hd(){}var ro=!1;function fd(e,t,r){if(ro)return e(t,r);ro=!0;try{return dd(e,t,r)}finally{ro=!1,(Gr!==null||Vr!==null)&&(hd(),cd())}}function Wn(e,t){var r=e.stateNode;if(r===null)return null;var n=Pi(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(N(231,t,typeof r));return r}var Yo=!1;if(gt)try{var pn={};Object.defineProperty(pn,"passive",{get:function(){Yo=!0}}),window.addEventListener("test",pn,pn),window.removeEventListener("test",pn,pn)}catch{Yo=!1}function wp(e,t,r,n,s,i,o,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(r,c)}catch(d){this.onError(d)}}var On=!1,Xs=null,Zs=!1,Ko=null,xp={onError:function(e){On=!0,Xs=e}};function _p(e,t,r,n,s,i,o,a,u){On=!1,Xs=null,wp.apply(xp,arguments)}function kp(e,t,r,n,s,i,o,a,u){if(_p.apply(this,arguments),On){if(On){var c=Xs;On=!1,Xs=null}else throw Error(N(198));Zs||(Zs=!0,Ko=c)}}function _r(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function pd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Yl(e){if(_r(e)!==e)throw Error(N(188))}function Sp(e){var t=e.alternate;if(!t){if(t=_r(e),t===null)throw Error(N(188));return t!==e?null:e}for(var r=e,n=t;;){var s=r.return;if(s===null)break;var i=s.alternate;if(i===null){if(n=s.return,n!==null){r=n;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===r)return Yl(s),e;if(i===n)return Yl(s),t;i=i.sibling}throw Error(N(188))}if(r.return!==n.return)r=s,n=i;else{for(var o=!1,a=s.child;a;){if(a===r){o=!0,r=s,n=i;break}if(a===n){o=!0,n=s,r=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===r){o=!0,r=i,n=s;break}if(a===n){o=!0,n=i,r=s;break}a=a.sibling}if(!o)throw Error(N(189))}}if(r.alternate!==n)throw Error(N(190))}if(r.tag!==3)throw Error(N(188));return r.stateNode.current===r?e:t}function md(e){return e=Sp(e),e!==null?gd(e):null}function gd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=gd(e);if(t!==null)return t;e=e.sibling}return null}var yd=Le.unstable_scheduleCallback,Kl=Le.unstable_cancelCallback,Ep=Le.unstable_shouldYield,bp=Le.unstable_requestPaint,te=Le.unstable_now,jp=Le.unstable_getCurrentPriorityLevel,Va=Le.unstable_ImmediatePriority,vd=Le.unstable_UserBlockingPriority,ei=Le.unstable_NormalPriority,Np=Le.unstable_LowPriority,wd=Le.unstable_IdlePriority,Ni=null,ot=null;function Cp(e){if(ot&&typeof ot.onCommitFiberRoot=="function")try{ot.onCommitFiberRoot(Ni,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:Pp,Tp=Math.log,Op=Math.LN2;function Pp(e){return e>>>=0,e===0?32:31-(Tp(e)/Op|0)|0}var _s=64,ks=4194304;function jn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ti(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,s=e.suspendedLanes,i=e.pingedLanes,o=r&268435455;if(o!==0){var a=o&~s;a!==0?n=jn(a):(i&=o,i!==0&&(n=jn(i)))}else o=r&~s,o!==0?n=jn(o):i!==0&&(n=jn(i));if(n===0)return 0;if(t!==0&&t!==n&&!(t&s)&&(s=n&-n,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(n&4&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-Xe(t),s=1<<r,n|=e[r],t&=~s;return n}function Rp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ip(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Xe(i),a=1<<o,u=s[o];u===-1?(!(a&r)||a&n)&&(s[o]=Rp(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function Jo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function xd(){var e=_s;return _s<<=1,!(_s&4194240)&&(_s=64),e}function no(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function us(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=r}function Ap(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var s=31-Xe(r),i=1<<s;t[s]=0,n[s]=-1,e[s]=-1,r&=~i}}function qa(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-Xe(r),s=1<<n;s&t|e[n]&t&&(e[n]|=t),r&=~s}}var z=0;function _d(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var kd,Ya,Sd,Ed,bd,Qo=!1,Ss=[],Dt=null,Lt=null,Mt=null,Gn=new Map,Vn=new Map,Ot=[],Dp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Jl(e,t){switch(e){case"focusin":case"focusout":Dt=null;break;case"dragenter":case"dragleave":Lt=null;break;case"mouseover":case"mouseout":Mt=null;break;case"pointerover":case"pointerout":Gn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vn.delete(t.pointerId)}}function mn(e,t,r,n,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:i,targetContainers:[s]},t!==null&&(t=ds(t),t!==null&&Ya(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Lp(e,t,r,n,s){switch(t){case"focusin":return Dt=mn(Dt,e,t,r,n,s),!0;case"dragenter":return Lt=mn(Lt,e,t,r,n,s),!0;case"mouseover":return Mt=mn(Mt,e,t,r,n,s),!0;case"pointerover":var i=s.pointerId;return Gn.set(i,mn(Gn.get(i)||null,e,t,r,n,s)),!0;case"gotpointercapture":return i=s.pointerId,Vn.set(i,mn(Vn.get(i)||null,e,t,r,n,s)),!0}return!1}function jd(e){var t=ar(e.target);if(t!==null){var r=_r(t);if(r!==null){if(t=r.tag,t===13){if(t=pd(r),t!==null){e.blockedOn=t,bd(e.priority,function(){Sd(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Fs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Xo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);Vo=n,r.target.dispatchEvent(n),Vo=null}else return t=ds(r),t!==null&&Ya(t),e.blockedOn=r,!1;t.shift()}return!0}function Ql(e,t,r){Fs(e)&&r.delete(t)}function Mp(){Qo=!1,Dt!==null&&Fs(Dt)&&(Dt=null),Lt!==null&&Fs(Lt)&&(Lt=null),Mt!==null&&Fs(Mt)&&(Mt=null),Gn.forEach(Ql),Vn.forEach(Ql)}function gn(e,t){e.blockedOn===t&&(e.blockedOn=null,Qo||(Qo=!0,Le.unstable_scheduleCallback(Le.unstable_NormalPriority,Mp)))}function qn(e){function t(s){return gn(s,e)}if(0<Ss.length){gn(Ss[0],e);for(var r=1;r<Ss.length;r++){var n=Ss[r];n.blockedOn===e&&(n.blockedOn=null)}}for(Dt!==null&&gn(Dt,e),Lt!==null&&gn(Lt,e),Mt!==null&&gn(Mt,e),Gn.forEach(t),Vn.forEach(t),r=0;r<Ot.length;r++)n=Ot[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Ot.length&&(r=Ot[0],r.blockedOn===null);)jd(r),r.blockedOn===null&&Ot.shift()}var qr=_t.ReactCurrentBatchConfig,ri=!0;function $p(e,t,r,n){var s=z,i=qr.transition;qr.transition=null;try{z=1,Ka(e,t,r,n)}finally{z=s,qr.transition=i}}function Up(e,t,r,n){var s=z,i=qr.transition;qr.transition=null;try{z=4,Ka(e,t,r,n)}finally{z=s,qr.transition=i}}function Ka(e,t,r,n){if(ri){var s=Xo(e,t,r,n);if(s===null)po(e,t,n,ni,r),Jl(e,n);else if(Lp(s,e,t,r,n))n.stopPropagation();else if(Jl(e,n),t&4&&-1<Dp.indexOf(e)){for(;s!==null;){var i=ds(s);if(i!==null&&kd(i),i=Xo(e,t,r,n),i===null&&po(e,t,n,ni,r),i===s)break;s=i}s!==null&&n.stopPropagation()}else po(e,t,n,null,r)}}var ni=null;function Xo(e,t,r,n){if(ni=null,e=Ga(n),e=ar(e),e!==null)if(t=_r(e),t===null)e=null;else if(r=t.tag,r===13){if(e=pd(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ni=e,null}function Nd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(jp()){case Va:return 1;case vd:return 4;case ei:case Np:return 16;case wd:return 536870912;default:return 16}default:return 16}}var It=null,Ja=null,zs=null;function Cd(){if(zs)return zs;var e,t=Ja,r=t.length,n,s="value"in It?It.value:It.textContent,i=s.length;for(e=0;e<r&&t[e]===s[e];e++);var o=r-e;for(n=1;n<=o&&t[r-n]===s[i-n];n++);return zs=s.slice(e,1<n?1-n:void 0)}function Hs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Es(){return!0}function Xl(){return!1}function $e(e){function t(r,n,s,i,o){this._reactName=r,this._targetInst=s,this.type=n,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(r=e[a],this[a]=r?r(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Es:Xl,this.isPropagationStopped=Xl,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Es)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Es)},persist:function(){},isPersistent:Es}),t}var un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Qa=$e(un),cs=J({},un,{view:0,detail:0}),Fp=$e(cs),so,io,yn,Ci=J({},cs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Xa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==yn&&(yn&&e.type==="mousemove"?(so=e.screenX-yn.screenX,io=e.screenY-yn.screenY):io=so=0,yn=e),so)},movementY:function(e){return"movementY"in e?e.movementY:io}}),Zl=$e(Ci),zp=J({},Ci,{dataTransfer:0}),Hp=$e(zp),Bp=J({},cs,{relatedTarget:0}),oo=$e(Bp),Wp=J({},un,{animationName:0,elapsedTime:0,pseudoElement:0}),Gp=$e(Wp),Vp=J({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),qp=$e(Vp),Yp=J({},un,{data:0}),eu=$e(Yp),Kp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Qp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Xp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Qp[e])?!!t[e]:!1}function Xa(){return Xp}var Zp=J({},cs,{key:function(e){if(e.key){var t=Kp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Hs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Xa,charCode:function(e){return e.type==="keypress"?Hs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Hs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),em=$e(Zp),tm=J({},Ci,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),tu=$e(tm),rm=J({},cs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Xa}),nm=$e(rm),sm=J({},un,{propertyName:0,elapsedTime:0,pseudoElement:0}),im=$e(sm),om=J({},Ci,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),am=$e(om),lm=[9,13,27,32],Za=gt&&"CompositionEvent"in window,Pn=null;gt&&"documentMode"in document&&(Pn=document.documentMode);var um=gt&&"TextEvent"in window&&!Pn,Td=gt&&(!Za||Pn&&8<Pn&&11>=Pn),ru=String.fromCharCode(32),nu=!1;function Od(e,t){switch(e){case"keyup":return lm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ir=!1;function cm(e,t){switch(e){case"compositionend":return Pd(t);case"keypress":return t.which!==32?null:(nu=!0,ru);case"textInput":return e=t.data,e===ru&&nu?null:e;default:return null}}function dm(e,t){if(Ir)return e==="compositionend"||!Za&&Od(e,t)?(e=Cd(),zs=Ja=It=null,Ir=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Td&&t.locale!=="ko"?null:t.data;default:return null}}var hm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function su(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hm[e.type]:t==="textarea"}function Rd(e,t,r,n){ud(n),t=si(t,"onChange"),0<t.length&&(r=new Qa("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var Rn=null,Yn=null;function fm(e){Bd(e,0)}function Ti(e){var t=Lr(e);if(rd(t))return e}function pm(e,t){if(e==="change")return t}var Id=!1;if(gt){var ao;if(gt){var lo="oninput"in document;if(!lo){var iu=document.createElement("div");iu.setAttribute("oninput","return;"),lo=typeof iu.oninput=="function"}ao=lo}else ao=!1;Id=ao&&(!document.documentMode||9<document.documentMode)}function ou(){Rn&&(Rn.detachEvent("onpropertychange",Ad),Yn=Rn=null)}function Ad(e){if(e.propertyName==="value"&&Ti(Yn)){var t=[];Rd(t,Yn,e,Ga(e)),fd(fm,t)}}function mm(e,t,r){e==="focusin"?(ou(),Rn=t,Yn=r,Rn.attachEvent("onpropertychange",Ad)):e==="focusout"&&ou()}function gm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ti(Yn)}function ym(e,t){if(e==="click")return Ti(t)}function vm(e,t){if(e==="input"||e==="change")return Ti(t)}function wm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var et=typeof Object.is=="function"?Object.is:wm;function Kn(e,t){if(et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var s=r[n];if(!Ao.call(t,s)||!et(e[s],t[s]))return!1}return!0}function au(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function lu(e,t){var r=au(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=au(r)}}function Dd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Dd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ld(){for(var e=window,t=Qs();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=Qs(e.document)}return t}function el(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function xm(e){var t=Ld(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&Dd(r.ownerDocument.documentElement,r)){if(n!==null&&el(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=r.textContent.length,i=Math.min(n.start,s);n=n.end===void 0?i:Math.min(n.end,s),!e.extend&&i>n&&(s=n,n=i,i=s),s=lu(r,i);var o=lu(r,n);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>n?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var _m=gt&&"documentMode"in document&&11>=document.documentMode,Ar=null,Zo=null,In=null,ea=!1;function uu(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;ea||Ar==null||Ar!==Qs(n)||(n=Ar,"selectionStart"in n&&el(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),In&&Kn(In,n)||(In=n,n=si(Zo,"onSelect"),0<n.length&&(t=new Qa("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=Ar)))}function bs(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Dr={animationend:bs("Animation","AnimationEnd"),animationiteration:bs("Animation","AnimationIteration"),animationstart:bs("Animation","AnimationStart"),transitionend:bs("Transition","TransitionEnd")},uo={},Md={};gt&&(Md=document.createElement("div").style,"AnimationEvent"in window||(delete Dr.animationend.animation,delete Dr.animationiteration.animation,delete Dr.animationstart.animation),"TransitionEvent"in window||delete Dr.transitionend.transition);function Oi(e){if(uo[e])return uo[e];if(!Dr[e])return e;var t=Dr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in Md)return uo[e]=t[r];return e}var $d=Oi("animationend"),Ud=Oi("animationiteration"),Fd=Oi("animationstart"),zd=Oi("transitionend"),Hd=new Map,cu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yt(e,t){Hd.set(e,t),xr(t,[e])}for(var co=0;co<cu.length;co++){var ho=cu[co],km=ho.toLowerCase(),Sm=ho[0].toUpperCase()+ho.slice(1);Yt(km,"on"+Sm)}Yt($d,"onAnimationEnd");Yt(Ud,"onAnimationIteration");Yt(Fd,"onAnimationStart");Yt("dblclick","onDoubleClick");Yt("focusin","onFocus");Yt("focusout","onBlur");Yt(zd,"onTransitionEnd");Qr("onMouseEnter",["mouseout","mouseover"]);Qr("onMouseLeave",["mouseout","mouseover"]);Qr("onPointerEnter",["pointerout","pointerover"]);Qr("onPointerLeave",["pointerout","pointerover"]);xr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));xr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));xr("onBeforeInput",["compositionend","keypress","textInput","paste"]);xr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));xr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));xr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Nn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Em=new Set("cancel close invalid load scroll toggle".split(" ").concat(Nn));function du(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,kp(n,t,void 0,e),e.currentTarget=null}function Bd(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],s=n.event;n=n.listeners;e:{var i=void 0;if(t)for(var o=n.length-1;0<=o;o--){var a=n[o],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==i&&s.isPropagationStopped())break e;du(s,a,c),i=u}else for(o=0;o<n.length;o++){if(a=n[o],u=a.instance,c=a.currentTarget,a=a.listener,u!==i&&s.isPropagationStopped())break e;du(s,a,c),i=u}}}if(Zs)throw e=Ko,Zs=!1,Ko=null,e}function W(e,t){var r=t[ia];r===void 0&&(r=t[ia]=new Set);var n=e+"__bubble";r.has(n)||(Wd(t,e,2,!1),r.add(n))}function fo(e,t,r){var n=0;t&&(n|=4),Wd(r,e,n,t)}var js="_reactListening"+Math.random().toString(36).slice(2);function Jn(e){if(!e[js]){e[js]=!0,Qc.forEach(function(r){r!=="selectionchange"&&(Em.has(r)||fo(r,!1,e),fo(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[js]||(t[js]=!0,fo("selectionchange",!1,t))}}function Wd(e,t,r,n){switch(Nd(t)){case 1:var s=$p;break;case 4:s=Up;break;default:s=Ka}r=s.bind(null,t,r,e),s=void 0,!Yo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),n?s!==void 0?e.addEventListener(t,r,{capture:!0,passive:s}):e.addEventListener(t,r,!0):s!==void 0?e.addEventListener(t,r,{passive:s}):e.addEventListener(t,r,!1)}function po(e,t,r,n,s){var i=n;if(!(t&1)&&!(t&2)&&n!==null)e:for(;;){if(n===null)return;var o=n.tag;if(o===3||o===4){var a=n.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=n.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;o=o.return}for(;a!==null;){if(o=ar(a),o===null)return;if(u=o.tag,u===5||u===6){n=i=o;continue e}a=a.parentNode}}n=n.return}fd(function(){var c=i,d=Ga(r),p=[];e:{var m=Hd.get(e);if(m!==void 0){var y=Qa,w=e;switch(e){case"keypress":if(Hs(r)===0)break e;case"keydown":case"keyup":y=em;break;case"focusin":w="focus",y=oo;break;case"focusout":w="blur",y=oo;break;case"beforeblur":case"afterblur":y=oo;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Zl;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Hp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=nm;break;case $d:case Ud:case Fd:y=Gp;break;case zd:y=im;break;case"scroll":y=Fp;break;case"wheel":y=am;break;case"copy":case"cut":case"paste":y=qp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=tu}var x=(t&4)!==0,k=!x&&e==="scroll",f=x?m!==null?m+"Capture":null:m;x=[];for(var h=c,g;h!==null;){g=h;var _=g.stateNode;if(g.tag===5&&_!==null&&(g=_,f!==null&&(_=Wn(h,f),_!=null&&x.push(Qn(h,_,g)))),k)break;h=h.return}0<x.length&&(m=new y(m,w,null,r,d),p.push({event:m,listeners:x}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",m&&r!==Vo&&(w=r.relatedTarget||r.fromElement)&&(ar(w)||w[yt]))break e;if((y||m)&&(m=d.window===d?d:(m=d.ownerDocument)?m.defaultView||m.parentWindow:window,y?(w=r.relatedTarget||r.toElement,y=c,w=w?ar(w):null,w!==null&&(k=_r(w),w!==k||w.tag!==5&&w.tag!==6)&&(w=null)):(y=null,w=c),y!==w)){if(x=Zl,_="onMouseLeave",f="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=tu,_="onPointerLeave",f="onPointerEnter",h="pointer"),k=y==null?m:Lr(y),g=w==null?m:Lr(w),m=new x(_,h+"leave",y,r,d),m.target=k,m.relatedTarget=g,_=null,ar(d)===c&&(x=new x(f,h+"enter",w,r,d),x.target=g,x.relatedTarget=k,_=x),k=_,y&&w)t:{for(x=y,f=w,h=0,g=x;g;g=Er(g))h++;for(g=0,_=f;_;_=Er(_))g++;for(;0<h-g;)x=Er(x),h--;for(;0<g-h;)f=Er(f),g--;for(;h--;){if(x===f||f!==null&&x===f.alternate)break t;x=Er(x),f=Er(f)}x=null}else x=null;y!==null&&hu(p,m,y,x,!1),w!==null&&k!==null&&hu(p,k,w,x,!0)}}e:{if(m=c?Lr(c):window,y=m.nodeName&&m.nodeName.toLowerCase(),y==="select"||y==="input"&&m.type==="file")var S=pm;else if(su(m))if(Id)S=vm;else{S=gm;var b=mm}else(y=m.nodeName)&&y.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(S=ym);if(S&&(S=S(e,c))){Rd(p,S,r,d);break e}b&&b(e,m,c),e==="focusout"&&(b=m._wrapperState)&&b.controlled&&m.type==="number"&&zo(m,"number",m.value)}switch(b=c?Lr(c):window,e){case"focusin":(su(b)||b.contentEditable==="true")&&(Ar=b,Zo=c,In=null);break;case"focusout":In=Zo=Ar=null;break;case"mousedown":ea=!0;break;case"contextmenu":case"mouseup":case"dragend":ea=!1,uu(p,r,d);break;case"selectionchange":if(_m)break;case"keydown":case"keyup":uu(p,r,d)}var E;if(Za)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else Ir?Od(e,r)&&(v="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(v="onCompositionStart");v&&(Td&&r.locale!=="ko"&&(Ir||v!=="onCompositionStart"?v==="onCompositionEnd"&&Ir&&(E=Cd()):(It=d,Ja="value"in It?It.value:It.textContent,Ir=!0)),b=si(c,v),0<b.length&&(v=new eu(v,e,null,r,d),p.push({event:v,listeners:b}),E?v.data=E:(E=Pd(r),E!==null&&(v.data=E)))),(E=um?cm(e,r):dm(e,r))&&(c=si(c,"onBeforeInput"),0<c.length&&(d=new eu("onBeforeInput","beforeinput",null,r,d),p.push({event:d,listeners:c}),d.data=E))}Bd(p,t)})}function Qn(e,t,r){return{instance:e,listener:t,currentTarget:r}}function si(e,t){for(var r=t+"Capture",n=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Wn(e,r),i!=null&&n.unshift(Qn(e,i,s)),i=Wn(e,t),i!=null&&n.push(Qn(e,i,s))),e=e.return}return n}function Er(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function hu(e,t,r,n,s){for(var i=t._reactName,o=[];r!==null&&r!==n;){var a=r,u=a.alternate,c=a.stateNode;if(u!==null&&u===n)break;a.tag===5&&c!==null&&(a=c,s?(u=Wn(r,i),u!=null&&o.unshift(Qn(r,u,a))):s||(u=Wn(r,i),u!=null&&o.push(Qn(r,u,a)))),r=r.return}o.length!==0&&e.push({event:t,listeners:o})}var bm=/\r\n?/g,jm=/\u0000|\uFFFD/g;function fu(e){return(typeof e=="string"?e:""+e).replace(bm,`
`).replace(jm,"")}function Ns(e,t,r){if(t=fu(t),fu(e)!==t&&r)throw Error(N(425))}function ii(){}var ta=null,ra=null;function na(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var sa=typeof setTimeout=="function"?setTimeout:void 0,Nm=typeof clearTimeout=="function"?clearTimeout:void 0,pu=typeof Promise=="function"?Promise:void 0,Cm=typeof queueMicrotask=="function"?queueMicrotask:typeof pu<"u"?function(e){return pu.resolve(null).then(e).catch(Tm)}:sa;function Tm(e){setTimeout(function(){throw e})}function mo(e,t){var r=t,n=0;do{var s=r.nextSibling;if(e.removeChild(r),s&&s.nodeType===8)if(r=s.data,r==="/$"){if(n===0){e.removeChild(s),qn(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=s}while(r);qn(t)}function $t(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function mu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var cn=Math.random().toString(36).slice(2),it="__reactFiber$"+cn,Xn="__reactProps$"+cn,yt="__reactContainer$"+cn,ia="__reactEvents$"+cn,Om="__reactListeners$"+cn,Pm="__reactHandles$"+cn;function ar(e){var t=e[it];if(t)return t;for(var r=e.parentNode;r;){if(t=r[yt]||r[it]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=mu(e);e!==null;){if(r=e[it])return r;e=mu(e)}return t}e=r,r=e.parentNode}return null}function ds(e){return e=e[it]||e[yt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Lr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function Pi(e){return e[Xn]||null}var oa=[],Mr=-1;function Kt(e){return{current:e}}function G(e){0>Mr||(e.current=oa[Mr],oa[Mr]=null,Mr--)}function B(e,t){Mr++,oa[Mr]=e.current,e.current=t}var qt={},ve=Kt(qt),Ce=Kt(!1),fr=qt;function Xr(e,t){var r=e.type.contextTypes;if(!r)return qt;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in r)s[i]=t[i];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Te(e){return e=e.childContextTypes,e!=null}function oi(){G(Ce),G(ve)}function gu(e,t,r){if(ve.current!==qt)throw Error(N(168));B(ve,t),B(Ce,r)}function Gd(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var s in n)if(!(s in t))throw Error(N(108,mp(e)||"Unknown",s));return J({},r,n)}function ai(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||qt,fr=ve.current,B(ve,e),B(Ce,Ce.current),!0}function yu(e,t,r){var n=e.stateNode;if(!n)throw Error(N(169));r?(e=Gd(e,t,fr),n.__reactInternalMemoizedMergedChildContext=e,G(Ce),G(ve),B(ve,e)):G(Ce),B(Ce,r)}var ht=null,Ri=!1,go=!1;function Vd(e){ht===null?ht=[e]:ht.push(e)}function Rm(e){Ri=!0,Vd(e)}function Jt(){if(!go&&ht!==null){go=!0;var e=0,t=z;try{var r=ht;for(z=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}ht=null,Ri=!1}catch(s){throw ht!==null&&(ht=ht.slice(e+1)),yd(Va,Jt),s}finally{z=t,go=!1}}return null}var $r=[],Ur=0,li=null,ui=0,Fe=[],ze=0,pr=null,ft=1,pt="";function rr(e,t){$r[Ur++]=ui,$r[Ur++]=li,li=e,ui=t}function qd(e,t,r){Fe[ze++]=ft,Fe[ze++]=pt,Fe[ze++]=pr,pr=e;var n=ft;e=pt;var s=32-Xe(n)-1;n&=~(1<<s),r+=1;var i=32-Xe(t)+s;if(30<i){var o=s-s%5;i=(n&(1<<o)-1).toString(32),n>>=o,s-=o,ft=1<<32-Xe(t)+s|r<<s|n,pt=i+e}else ft=1<<i|r<<s|n,pt=e}function tl(e){e.return!==null&&(rr(e,1),qd(e,1,0))}function rl(e){for(;e===li;)li=$r[--Ur],$r[Ur]=null,ui=$r[--Ur],$r[Ur]=null;for(;e===pr;)pr=Fe[--ze],Fe[ze]=null,pt=Fe[--ze],Fe[ze]=null,ft=Fe[--ze],Fe[ze]=null}var De=null,Ae=null,V=!1,Qe=null;function Yd(e,t){var r=He(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function vu(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Ae=$t(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=pr!==null?{id:ft,overflow:pt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=He(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,De=e,Ae=null,!0):!1;default:return!1}}function aa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function la(e){if(V){var t=Ae;if(t){var r=t;if(!vu(e,t)){if(aa(e))throw Error(N(418));t=$t(r.nextSibling);var n=De;t&&vu(e,t)?Yd(n,r):(e.flags=e.flags&-4097|2,V=!1,De=e)}}else{if(aa(e))throw Error(N(418));e.flags=e.flags&-4097|2,V=!1,De=e}}}function wu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function Cs(e){if(e!==De)return!1;if(!V)return wu(e),V=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!na(e.type,e.memoizedProps)),t&&(t=Ae)){if(aa(e))throw Kd(),Error(N(418));for(;t;)Yd(e,t),t=$t(t.nextSibling)}if(wu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){Ae=$t(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=De?$t(e.stateNode.nextSibling):null;return!0}function Kd(){for(var e=Ae;e;)e=$t(e.nextSibling)}function Zr(){Ae=De=null,V=!1}function nl(e){Qe===null?Qe=[e]:Qe.push(e)}var Im=_t.ReactCurrentBatchConfig;function vn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(N(309));var n=r.stateNode}if(!n)throw Error(N(147,e));var s=n,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(N(284));if(!r._owner)throw Error(N(290,e))}return e}function Ts(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function xu(e){var t=e._init;return t(e._payload)}function Jd(e){function t(f,h){if(e){var g=f.deletions;g===null?(f.deletions=[h],f.flags|=16):g.push(h)}}function r(f,h){if(!e)return null;for(;h!==null;)t(f,h),h=h.sibling;return null}function n(f,h){for(f=new Map;h!==null;)h.key!==null?f.set(h.key,h):f.set(h.index,h),h=h.sibling;return f}function s(f,h){return f=Ht(f,h),f.index=0,f.sibling=null,f}function i(f,h,g){return f.index=g,e?(g=f.alternate,g!==null?(g=g.index,g<h?(f.flags|=2,h):g):(f.flags|=2,h)):(f.flags|=1048576,h)}function o(f){return e&&f.alternate===null&&(f.flags|=2),f}function a(f,h,g,_){return h===null||h.tag!==6?(h=So(g,f.mode,_),h.return=f,h):(h=s(h,g),h.return=f,h)}function u(f,h,g,_){var S=g.type;return S===Rr?d(f,h,g.props.children,_,g.key):h!==null&&(h.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===jt&&xu(S)===h.type)?(_=s(h,g.props),_.ref=vn(f,h,g),_.return=f,_):(_=Ks(g.type,g.key,g.props,null,f.mode,_),_.ref=vn(f,h,g),_.return=f,_)}function c(f,h,g,_){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=Eo(g,f.mode,_),h.return=f,h):(h=s(h,g.children||[]),h.return=f,h)}function d(f,h,g,_,S){return h===null||h.tag!==7?(h=dr(g,f.mode,_,S),h.return=f,h):(h=s(h,g),h.return=f,h)}function p(f,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=So(""+h,f.mode,g),h.return=f,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case vs:return g=Ks(h.type,h.key,h.props,null,f.mode,g),g.ref=vn(f,null,h),g.return=f,g;case Pr:return h=Eo(h,f.mode,g),h.return=f,h;case jt:var _=h._init;return p(f,_(h._payload),g)}if(bn(h)||fn(h))return h=dr(h,f.mode,g,null),h.return=f,h;Ts(f,h)}return null}function m(f,h,g,_){var S=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return S!==null?null:a(f,h,""+g,_);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case vs:return g.key===S?u(f,h,g,_):null;case Pr:return g.key===S?c(f,h,g,_):null;case jt:return S=g._init,m(f,h,S(g._payload),_)}if(bn(g)||fn(g))return S!==null?null:d(f,h,g,_,null);Ts(f,g)}return null}function y(f,h,g,_,S){if(typeof _=="string"&&_!==""||typeof _=="number")return f=f.get(g)||null,a(h,f,""+_,S);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case vs:return f=f.get(_.key===null?g:_.key)||null,u(h,f,_,S);case Pr:return f=f.get(_.key===null?g:_.key)||null,c(h,f,_,S);case jt:var b=_._init;return y(f,h,g,b(_._payload),S)}if(bn(_)||fn(_))return f=f.get(g)||null,d(h,f,_,S,null);Ts(h,_)}return null}function w(f,h,g,_){for(var S=null,b=null,E=h,v=h=0,j=null;E!==null&&v<g.length;v++){E.index>v?(j=E,E=null):j=E.sibling;var C=m(f,E,g[v],_);if(C===null){E===null&&(E=j);break}e&&E&&C.alternate===null&&t(f,E),h=i(C,h,v),b===null?S=C:b.sibling=C,b=C,E=j}if(v===g.length)return r(f,E),V&&rr(f,v),S;if(E===null){for(;v<g.length;v++)E=p(f,g[v],_),E!==null&&(h=i(E,h,v),b===null?S=E:b.sibling=E,b=E);return V&&rr(f,v),S}for(E=n(f,E);v<g.length;v++)j=y(E,f,v,g[v],_),j!==null&&(e&&j.alternate!==null&&E.delete(j.key===null?v:j.key),h=i(j,h,v),b===null?S=j:b.sibling=j,b=j);return e&&E.forEach(function(X){return t(f,X)}),V&&rr(f,v),S}function x(f,h,g,_){var S=fn(g);if(typeof S!="function")throw Error(N(150));if(g=S.call(g),g==null)throw Error(N(151));for(var b=S=null,E=h,v=h=0,j=null,C=g.next();E!==null&&!C.done;v++,C=g.next()){E.index>v?(j=E,E=null):j=E.sibling;var X=m(f,E,C.value,_);if(X===null){E===null&&(E=j);break}e&&E&&X.alternate===null&&t(f,E),h=i(X,h,v),b===null?S=X:b.sibling=X,b=X,E=j}if(C.done)return r(f,E),V&&rr(f,v),S;if(E===null){for(;!C.done;v++,C=g.next())C=p(f,C.value,_),C!==null&&(h=i(C,h,v),b===null?S=C:b.sibling=C,b=C);return V&&rr(f,v),S}for(E=n(f,E);!C.done;v++,C=g.next())C=y(E,f,v,C.value,_),C!==null&&(e&&C.alternate!==null&&E.delete(C.key===null?v:C.key),h=i(C,h,v),b===null?S=C:b.sibling=C,b=C);return e&&E.forEach(function(lt){return t(f,lt)}),V&&rr(f,v),S}function k(f,h,g,_){if(typeof g=="object"&&g!==null&&g.type===Rr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case vs:e:{for(var S=g.key,b=h;b!==null;){if(b.key===S){if(S=g.type,S===Rr){if(b.tag===7){r(f,b.sibling),h=s(b,g.props.children),h.return=f,f=h;break e}}else if(b.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===jt&&xu(S)===b.type){r(f,b.sibling),h=s(b,g.props),h.ref=vn(f,b,g),h.return=f,f=h;break e}r(f,b);break}else t(f,b);b=b.sibling}g.type===Rr?(h=dr(g.props.children,f.mode,_,g.key),h.return=f,f=h):(_=Ks(g.type,g.key,g.props,null,f.mode,_),_.ref=vn(f,h,g),_.return=f,f=_)}return o(f);case Pr:e:{for(b=g.key;h!==null;){if(h.key===b)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){r(f,h.sibling),h=s(h,g.children||[]),h.return=f,f=h;break e}else{r(f,h);break}else t(f,h);h=h.sibling}h=Eo(g,f.mode,_),h.return=f,f=h}return o(f);case jt:return b=g._init,k(f,h,b(g._payload),_)}if(bn(g))return w(f,h,g,_);if(fn(g))return x(f,h,g,_);Ts(f,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(r(f,h.sibling),h=s(h,g),h.return=f,f=h):(r(f,h),h=So(g,f.mode,_),h.return=f,f=h),o(f)):r(f,h)}return k}var en=Jd(!0),Qd=Jd(!1),ci=Kt(null),di=null,Fr=null,sl=null;function il(){sl=Fr=di=null}function ol(e){var t=ci.current;G(ci),e._currentValue=t}function ua(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function Yr(e,t){di=e,sl=Fr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ne=!0),e.firstContext=null)}function Ge(e){var t=e._currentValue;if(sl!==e)if(e={context:e,memoizedValue:t,next:null},Fr===null){if(di===null)throw Error(N(308));Fr=e,di.dependencies={lanes:0,firstContext:e}}else Fr=Fr.next=e;return t}var lr=null;function al(e){lr===null?lr=[e]:lr.push(e)}function Xd(e,t,r,n){var s=t.interleaved;return s===null?(r.next=r,al(t)):(r.next=s.next,s.next=r),t.interleaved=r,vt(e,n)}function vt(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Nt=!1;function ll(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Zd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function mt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ut(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,U&2){var s=n.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),n.pending=t,vt(e,r)}return s=n.interleaved,s===null?(t.next=t,al(n)):(t.next=s.next,s.next=t),n.interleaved=t,vt(e,r)}function Bs(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,qa(e,r)}}function _u(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var s=null,i=null;if(r=r.firstBaseUpdate,r!==null){do{var o={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};i===null?s=i=o:i=i.next=o,r=r.next}while(r!==null);i===null?s=i=t:i=i.next=t}else s=i=t;r={baseState:n.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function hi(e,t,r,n){var s=e.updateQueue;Nt=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,c=u.next;u.next=null,o===null?i=c:o.next=c,o=u;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=c:a.next=c,d.lastBaseUpdate=u))}if(i!==null){var p=s.baseState;o=0,d=c=u=null,a=i;do{var m=a.lane,y=a.eventTime;if((n&m)===m){d!==null&&(d=d.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,x=a;switch(m=t,y=r,x.tag){case 1:if(w=x.payload,typeof w=="function"){p=w.call(y,p,m);break e}p=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=x.payload,m=typeof w=="function"?w.call(y,p,m):w,m==null)break e;p=J({},p,m);break e;case 2:Nt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,m=s.effects,m===null?s.effects=[a]:m.push(a))}else y={eventTime:y,lane:m,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(c=d=y,u=p):d=d.next=y,o|=m;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;m=a,a=m.next,m.next=null,s.lastBaseUpdate=m,s.shared.pending=null}}while(1);if(d===null&&(u=p),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);gr|=o,e.lanes=o,e.memoizedState=p}}function ku(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],s=n.callback;if(s!==null){if(n.callback=null,n=r,typeof s!="function")throw Error(N(191,s));s.call(n)}}}var hs={},at=Kt(hs),Zn=Kt(hs),es=Kt(hs);function ur(e){if(e===hs)throw Error(N(174));return e}function ul(e,t){switch(B(es,t),B(Zn,e),B(at,hs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Bo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Bo(t,e)}G(at),B(at,t)}function tn(){G(at),G(Zn),G(es)}function eh(e){ur(es.current);var t=ur(at.current),r=Bo(t,e.type);t!==r&&(B(Zn,e),B(at,r))}function cl(e){Zn.current===e&&(G(at),G(Zn))}var Y=Kt(0);function fi(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var yo=[];function dl(){for(var e=0;e<yo.length;e++)yo[e]._workInProgressVersionPrimary=null;yo.length=0}var Ws=_t.ReactCurrentDispatcher,vo=_t.ReactCurrentBatchConfig,mr=0,K=null,se=null,ae=null,pi=!1,An=!1,ts=0,Am=0;function fe(){throw Error(N(321))}function hl(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!et(e[r],t[r]))return!1;return!0}function fl(e,t,r,n,s,i){if(mr=i,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ws.current=e===null||e.memoizedState===null?$m:Um,e=r(n,s),An){i=0;do{if(An=!1,ts=0,25<=i)throw Error(N(301));i+=1,ae=se=null,t.updateQueue=null,Ws.current=Fm,e=r(n,s)}while(An)}if(Ws.current=mi,t=se!==null&&se.next!==null,mr=0,ae=se=K=null,pi=!1,t)throw Error(N(300));return e}function pl(){var e=ts!==0;return ts=0,e}function st(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ae===null?K.memoizedState=ae=e:ae=ae.next=e,ae}function Ve(){if(se===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=se.next;var t=ae===null?K.memoizedState:ae.next;if(t!==null)ae=t,se=e;else{if(e===null)throw Error(N(310));se=e,e={memoizedState:se.memoizedState,baseState:se.baseState,baseQueue:se.baseQueue,queue:se.queue,next:null},ae===null?K.memoizedState=ae=e:ae=ae.next=e}return ae}function rs(e,t){return typeof t=="function"?t(e):t}function wo(e){var t=Ve(),r=t.queue;if(r===null)throw Error(N(311));r.lastRenderedReducer=e;var n=se,s=n.baseQueue,i=r.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}n.baseQueue=s=i,r.pending=null}if(s!==null){i=s.next,n=n.baseState;var a=o=null,u=null,c=i;do{var d=c.lane;if((mr&d)===d)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),n=c.hasEagerState?c.eagerState:e(n,c.action);else{var p={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=p,o=n):u=u.next=p,K.lanes|=d,gr|=d}c=c.next}while(c!==null&&c!==i);u===null?o=n:u.next=a,et(n,t.memoizedState)||(Ne=!0),t.memoizedState=n,t.baseState=o,t.baseQueue=u,r.lastRenderedState=n}if(e=r.interleaved,e!==null){s=e;do i=s.lane,K.lanes|=i,gr|=i,s=s.next;while(s!==e)}else s===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function xo(e){var t=Ve(),r=t.queue;if(r===null)throw Error(N(311));r.lastRenderedReducer=e;var n=r.dispatch,s=r.pending,i=t.memoizedState;if(s!==null){r.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);et(i,t.memoizedState)||(Ne=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),r.lastRenderedState=i}return[i,n]}function th(){}function rh(e,t){var r=K,n=Ve(),s=t(),i=!et(n.memoizedState,s);if(i&&(n.memoizedState=s,Ne=!0),n=n.queue,ml(ih.bind(null,r,n,e),[e]),n.getSnapshot!==t||i||ae!==null&&ae.memoizedState.tag&1){if(r.flags|=2048,ns(9,sh.bind(null,r,n,s,t),void 0,null),le===null)throw Error(N(349));mr&30||nh(r,t,s)}return s}function nh(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function sh(e,t,r,n){t.value=r,t.getSnapshot=n,oh(t)&&ah(e)}function ih(e,t,r){return r(function(){oh(t)&&ah(e)})}function oh(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!et(e,r)}catch{return!0}}function ah(e){var t=vt(e,1);t!==null&&Ze(t,e,1,-1)}function Su(e){var t=st();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:rs,lastRenderedState:e},t.queue=e,e=e.dispatch=Mm.bind(null,K,e),[t.memoizedState,e]}function ns(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function lh(){return Ve().memoizedState}function Gs(e,t,r,n){var s=st();K.flags|=e,s.memoizedState=ns(1|t,r,void 0,n===void 0?null:n)}function Ii(e,t,r,n){var s=Ve();n=n===void 0?null:n;var i=void 0;if(se!==null){var o=se.memoizedState;if(i=o.destroy,n!==null&&hl(n,o.deps)){s.memoizedState=ns(t,r,i,n);return}}K.flags|=e,s.memoizedState=ns(1|t,r,i,n)}function Eu(e,t){return Gs(8390656,8,e,t)}function ml(e,t){return Ii(2048,8,e,t)}function uh(e,t){return Ii(4,2,e,t)}function ch(e,t){return Ii(4,4,e,t)}function dh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hh(e,t,r){return r=r!=null?r.concat([e]):null,Ii(4,4,dh.bind(null,t,e),r)}function gl(){}function fh(e,t){var r=Ve();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&hl(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function ph(e,t){var r=Ve();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&hl(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function mh(e,t,r){return mr&21?(et(r,t)||(r=xd(),K.lanes|=r,gr|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ne=!0),e.memoizedState=r)}function Dm(e,t){var r=z;z=r!==0&&4>r?r:4,e(!0);var n=vo.transition;vo.transition={};try{e(!1),t()}finally{z=r,vo.transition=n}}function gh(){return Ve().memoizedState}function Lm(e,t,r){var n=zt(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},yh(e))vh(t,r);else if(r=Xd(e,t,r,n),r!==null){var s=xe();Ze(r,e,n,s),wh(r,t,n)}}function Mm(e,t,r){var n=zt(e),s={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(yh(e))vh(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,r);if(s.hasEagerState=!0,s.eagerState=a,et(a,o)){var u=t.interleaved;u===null?(s.next=s,al(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}r=Xd(e,t,s,n),r!==null&&(s=xe(),Ze(r,e,n,s),wh(r,t,n))}}function yh(e){var t=e.alternate;return e===K||t!==null&&t===K}function vh(e,t){An=pi=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function wh(e,t,r){if(r&4194240){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,qa(e,r)}}var mi={readContext:Ge,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},$m={readContext:Ge,useCallback:function(e,t){return st().memoizedState=[e,t===void 0?null:t],e},useContext:Ge,useEffect:Eu,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,Gs(4194308,4,dh.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Gs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Gs(4,2,e,t)},useMemo:function(e,t){var r=st();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=st();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=Lm.bind(null,K,e),[n.memoizedState,e]},useRef:function(e){var t=st();return e={current:e},t.memoizedState=e},useState:Su,useDebugValue:gl,useDeferredValue:function(e){return st().memoizedState=e},useTransition:function(){var e=Su(!1),t=e[0];return e=Dm.bind(null,e[1]),st().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=K,s=st();if(V){if(r===void 0)throw Error(N(407));r=r()}else{if(r=t(),le===null)throw Error(N(349));mr&30||nh(n,t,r)}s.memoizedState=r;var i={value:r,getSnapshot:t};return s.queue=i,Eu(ih.bind(null,n,i,e),[e]),n.flags|=2048,ns(9,sh.bind(null,n,i,r,t),void 0,null),r},useId:function(){var e=st(),t=le.identifierPrefix;if(V){var r=pt,n=ft;r=(n&~(1<<32-Xe(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=ts++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Am++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Um={readContext:Ge,useCallback:fh,useContext:Ge,useEffect:ml,useImperativeHandle:hh,useInsertionEffect:uh,useLayoutEffect:ch,useMemo:ph,useReducer:wo,useRef:lh,useState:function(){return wo(rs)},useDebugValue:gl,useDeferredValue:function(e){var t=Ve();return mh(t,se.memoizedState,e)},useTransition:function(){var e=wo(rs)[0],t=Ve().memoizedState;return[e,t]},useMutableSource:th,useSyncExternalStore:rh,useId:gh,unstable_isNewReconciler:!1},Fm={readContext:Ge,useCallback:fh,useContext:Ge,useEffect:ml,useImperativeHandle:hh,useInsertionEffect:uh,useLayoutEffect:ch,useMemo:ph,useReducer:xo,useRef:lh,useState:function(){return xo(rs)},useDebugValue:gl,useDeferredValue:function(e){var t=Ve();return se===null?t.memoizedState=e:mh(t,se.memoizedState,e)},useTransition:function(){var e=xo(rs)[0],t=Ve().memoizedState;return[e,t]},useMutableSource:th,useSyncExternalStore:rh,useId:gh,unstable_isNewReconciler:!1};function Ye(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function ca(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:J({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var Ai={isMounted:function(e){return(e=e._reactInternals)?_r(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=xe(),s=zt(e),i=mt(n,s);i.payload=t,r!=null&&(i.callback=r),t=Ut(e,i,s),t!==null&&(Ze(t,e,s,n),Bs(t,e,s))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=xe(),s=zt(e),i=mt(n,s);i.tag=1,i.payload=t,r!=null&&(i.callback=r),t=Ut(e,i,s),t!==null&&(Ze(t,e,s,n),Bs(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=xe(),n=zt(e),s=mt(r,n);s.tag=2,t!=null&&(s.callback=t),t=Ut(e,s,n),t!==null&&(Ze(t,e,n,r),Bs(t,e,n))}};function bu(e,t,r,n,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,i,o):t.prototype&&t.prototype.isPureReactComponent?!Kn(r,n)||!Kn(s,i):!0}function xh(e,t,r){var n=!1,s=qt,i=t.contextType;return typeof i=="object"&&i!==null?i=Ge(i):(s=Te(t)?fr:ve.current,n=t.contextTypes,i=(n=n!=null)?Xr(e,s):qt),t=new t(r,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ai,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function ju(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&Ai.enqueueReplaceState(t,t.state,null)}function da(e,t,r,n){var s=e.stateNode;s.props=r,s.state=e.memoizedState,s.refs={},ll(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=Ge(i):(i=Te(t)?fr:ve.current,s.context=Xr(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ca(e,t,i,r),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Ai.enqueueReplaceState(s,s.state,null),hi(e,r,s,n),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function rn(e,t){try{var r="",n=t;do r+=pp(n),n=n.return;while(n);var s=r}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function _o(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function ha(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var zm=typeof WeakMap=="function"?WeakMap:Map;function _h(e,t,r){r=mt(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){yi||(yi=!0,ka=n),ha(e,t)},r}function kh(e,t,r){r=mt(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var s=t.value;r.payload=function(){return n(s)},r.callback=function(){ha(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(r.callback=function(){ha(e,t),typeof n!="function"&&(Ft===null?Ft=new Set([this]):Ft.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),r}function Nu(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new zm;var s=new Set;n.set(t,s)}else s=n.get(t),s===void 0&&(s=new Set,n.set(t,s));s.has(r)||(s.add(r),e=tg.bind(null,e,t,r),t.then(e,e))}function Cu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Tu(e,t,r,n,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=mt(-1,1),t.tag=2,Ut(r,t,1))),r.lanes|=1),e)}var Hm=_t.ReactCurrentOwner,Ne=!1;function we(e,t,r,n){t.child=e===null?Qd(t,null,r,n):en(t,e.child,r,n)}function Ou(e,t,r,n,s){r=r.render;var i=t.ref;return Yr(t,s),n=fl(e,t,r,n,i,s),r=pl(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,wt(e,t,s)):(V&&r&&tl(t),t.flags|=1,we(e,t,n,s),t.child)}function Pu(e,t,r,n,s){if(e===null){var i=r.type;return typeof i=="function"&&!El(i)&&i.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=i,Sh(e,t,i,n,s)):(e=Ks(r.type,null,n,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(r=r.compare,r=r!==null?r:Kn,r(o,n)&&e.ref===t.ref)return wt(e,t,s)}return t.flags|=1,e=Ht(i,n),e.ref=t.ref,e.return=t,t.child=e}function Sh(e,t,r,n,s){if(e!==null){var i=e.memoizedProps;if(Kn(i,n)&&e.ref===t.ref)if(Ne=!1,t.pendingProps=n=i,(e.lanes&s)!==0)e.flags&131072&&(Ne=!0);else return t.lanes=e.lanes,wt(e,t,s)}return fa(e,t,r,n,s)}function Eh(e,t,r){var n=t.pendingProps,s=n.children,i=e!==null?e.memoizedState:null;if(n.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Hr,Re),Re|=r;else{if(!(r&1073741824))return e=i!==null?i.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Hr,Re),Re|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=i!==null?i.baseLanes:r,B(Hr,Re),Re|=n}else i!==null?(n=i.baseLanes|r,t.memoizedState=null):n=r,B(Hr,Re),Re|=n;return we(e,t,s,r),t.child}function bh(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function fa(e,t,r,n,s){var i=Te(r)?fr:ve.current;return i=Xr(t,i),Yr(t,s),r=fl(e,t,r,n,i,s),n=pl(),e!==null&&!Ne?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,wt(e,t,s)):(V&&n&&tl(t),t.flags|=1,we(e,t,r,s),t.child)}function Ru(e,t,r,n,s){if(Te(r)){var i=!0;ai(t)}else i=!1;if(Yr(t,s),t.stateNode===null)Vs(e,t),xh(t,r,n),da(t,r,n,s),n=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var u=o.context,c=r.contextType;typeof c=="object"&&c!==null?c=Ge(c):(c=Te(r)?fr:ve.current,c=Xr(t,c));var d=r.getDerivedStateFromProps,p=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";p||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==n||u!==c)&&ju(t,o,n,c),Nt=!1;var m=t.memoizedState;o.state=m,hi(t,n,o,s),u=t.memoizedState,a!==n||m!==u||Ce.current||Nt?(typeof d=="function"&&(ca(t,r,d,n),u=t.memoizedState),(a=Nt||bu(t,r,a,n,m,u,c))?(p||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=u),o.props=n,o.state=u,o.context=c,n=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{o=t.stateNode,Zd(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Ye(t.type,a),o.props=c,p=t.pendingProps,m=o.context,u=r.contextType,typeof u=="object"&&u!==null?u=Ge(u):(u=Te(r)?fr:ve.current,u=Xr(t,u));var y=r.getDerivedStateFromProps;(d=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==p||m!==u)&&ju(t,o,n,u),Nt=!1,m=t.memoizedState,o.state=m,hi(t,n,o,s);var w=t.memoizedState;a!==p||m!==w||Ce.current||Nt?(typeof y=="function"&&(ca(t,r,y,n),w=t.memoizedState),(c=Nt||bu(t,r,c,n,m,w,u)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(n,w,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(n,w,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=w),o.props=n,o.state=w,o.context=u,n=c):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),n=!1)}return pa(e,t,r,n,i,s)}function pa(e,t,r,n,s,i){bh(e,t);var o=(t.flags&128)!==0;if(!n&&!o)return s&&yu(t,r,!1),wt(e,t,i);n=t.stateNode,Hm.current=t;var a=o&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&o?(t.child=en(t,e.child,null,i),t.child=en(t,null,a,i)):we(e,t,a,i),t.memoizedState=n.state,s&&yu(t,r,!0),t.child}function jh(e){var t=e.stateNode;t.pendingContext?gu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&gu(e,t.context,!1),ul(e,t.containerInfo)}function Iu(e,t,r,n,s){return Zr(),nl(s),t.flags|=256,we(e,t,r,n),t.child}var ma={dehydrated:null,treeContext:null,retryLane:0};function ga(e){return{baseLanes:e,cachePool:null,transitions:null}}function Nh(e,t,r){var n=t.pendingProps,s=Y.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),B(Y,s&1),e===null)return la(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=n.children,e=n.fallback,i?(n=t.mode,i=t.child,o={mode:"hidden",children:o},!(n&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Mi(o,n,0,null),e=dr(e,n,r,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ga(r),t.memoizedState=ma,e):yl(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return Bm(e,t,o,n,a,s,r);if(i){i=n.fallback,o=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:n.children};return!(o&1)&&t.child!==s?(n=t.child,n.childLanes=0,n.pendingProps=u,t.deletions=null):(n=Ht(s,u),n.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=Ht(a,i):(i=dr(i,o,r,null),i.flags|=2),i.return=t,n.return=t,n.sibling=i,t.child=n,n=i,i=t.child,o=e.child.memoizedState,o=o===null?ga(r):{baseLanes:o.baseLanes|r,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~r,t.memoizedState=ma,n}return i=e.child,e=i.sibling,n=Ht(i,{mode:"visible",children:n.children}),!(t.mode&1)&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function yl(e,t){return t=Mi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Os(e,t,r,n){return n!==null&&nl(n),en(t,e.child,null,r),e=yl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Bm(e,t,r,n,s,i,o){if(r)return t.flags&256?(t.flags&=-257,n=_o(Error(N(422))),Os(e,t,o,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=n.fallback,s=t.mode,n=Mi({mode:"visible",children:n.children},s,0,null),i=dr(i,s,o,null),i.flags|=2,n.return=t,i.return=t,n.sibling=i,t.child=n,t.mode&1&&en(t,e.child,null,o),t.child.memoizedState=ga(o),t.memoizedState=ma,i);if(!(t.mode&1))return Os(e,t,o,null);if(s.data==="$!"){if(n=s.nextSibling&&s.nextSibling.dataset,n)var a=n.dgst;return n=a,i=Error(N(419)),n=_o(i,n,void 0),Os(e,t,o,n)}if(a=(o&e.childLanes)!==0,Ne||a){if(n=le,n!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(n.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,vt(e,s),Ze(n,e,s,-1))}return Sl(),n=_o(Error(N(421))),Os(e,t,o,n)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=rg.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,Ae=$t(s.nextSibling),De=t,V=!0,Qe=null,e!==null&&(Fe[ze++]=ft,Fe[ze++]=pt,Fe[ze++]=pr,ft=e.id,pt=e.overflow,pr=t),t=yl(t,n.children),t.flags|=4096,t)}function Au(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),ua(e.return,t,r)}function ko(e,t,r,n,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=n,i.tail=r,i.tailMode=s)}function Ch(e,t,r){var n=t.pendingProps,s=n.revealOrder,i=n.tail;if(we(e,t,n.children,r),n=Y.current,n&2)n=n&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Au(e,r,t);else if(e.tag===19)Au(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(B(Y,n),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(r=t.child,s=null;r!==null;)e=r.alternate,e!==null&&fi(e)===null&&(s=r),r=r.sibling;r=s,r===null?(s=t.child,t.child=null):(s=r.sibling,r.sibling=null),ko(t,!1,s,r,i);break;case"backwards":for(r=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&fi(e)===null){t.child=s;break}e=s.sibling,s.sibling=r,r=s,s=e}ko(t,!0,r,null,i);break;case"together":ko(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function wt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),gr|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,r=Ht(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Ht(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function Wm(e,t,r){switch(t.tag){case 3:jh(t),Zr();break;case 5:eh(t);break;case 1:Te(t.type)&&ai(t);break;case 4:ul(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,s=t.memoizedProps.value;B(ci,n._currentValue),n._currentValue=s;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(B(Y,Y.current&1),t.flags|=128,null):r&t.child.childLanes?Nh(e,t,r):(B(Y,Y.current&1),e=wt(e,t,r),e!==null?e.sibling:null);B(Y,Y.current&1);break;case 19:if(n=(r&t.childLanes)!==0,e.flags&128){if(n)return Ch(e,t,r);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),B(Y,Y.current),n)break;return null;case 22:case 23:return t.lanes=0,Eh(e,t,r)}return wt(e,t,r)}var Th,ya,Oh,Ph;Th=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};ya=function(){};Oh=function(e,t,r,n){var s=e.memoizedProps;if(s!==n){e=t.stateNode,ur(at.current);var i=null;switch(r){case"input":s=Uo(e,s),n=Uo(e,n),i=[];break;case"select":s=J({},s,{value:void 0}),n=J({},n,{value:void 0}),i=[];break;case"textarea":s=Ho(e,s),n=Ho(e,n),i=[];break;default:typeof s.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=ii)}Wo(r,n);var o;r=null;for(c in s)if(!n.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(o in a)a.hasOwnProperty(o)&&(r||(r={}),r[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Hn.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in n){var u=n[c];if(a=s!=null?s[c]:void 0,n.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(o in a)!a.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(r||(r={}),r[o]="");for(o in u)u.hasOwnProperty(o)&&a[o]!==u[o]&&(r||(r={}),r[o]=u[o])}else r||(i||(i=[]),i.push(c,r)),r=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Hn.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&W("scroll",e),i||a===u||(i=[])):(i=i||[]).push(c,u))}r&&(i=i||[]).push("style",r);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};Ph=function(e,t,r,n){r!==n&&(t.flags|=4)};function wn(e,t){if(!V)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var s=e.child;s!==null;)r|=s.lanes|s.childLanes,n|=s.subtreeFlags&14680064,n|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)r|=s.lanes|s.childLanes,n|=s.subtreeFlags,n|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function Gm(e,t,r){var n=t.pendingProps;switch(rl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Te(t.type)&&oi(),pe(t),null;case 3:return n=t.stateNode,tn(),G(Ce),G(ve),dl(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Cs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Qe!==null&&(ba(Qe),Qe=null))),ya(e,t),pe(t),null;case 5:cl(t);var s=ur(es.current);if(r=t.type,e!==null&&t.stateNode!=null)Oh(e,t,r,n,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(N(166));return pe(t),null}if(e=ur(at.current),Cs(t)){n=t.stateNode,r=t.type;var i=t.memoizedProps;switch(n[it]=t,n[Xn]=i,e=(t.mode&1)!==0,r){case"dialog":W("cancel",n),W("close",n);break;case"iframe":case"object":case"embed":W("load",n);break;case"video":case"audio":for(s=0;s<Nn.length;s++)W(Nn[s],n);break;case"source":W("error",n);break;case"img":case"image":case"link":W("error",n),W("load",n);break;case"details":W("toggle",n);break;case"input":Bl(n,i),W("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!i.multiple},W("invalid",n);break;case"textarea":Gl(n,i),W("invalid",n)}Wo(r,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?n.textContent!==a&&(i.suppressHydrationWarning!==!0&&Ns(n.textContent,a,e),s=["children",a]):typeof a=="number"&&n.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Ns(n.textContent,a,e),s=["children",""+a]):Hn.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&W("scroll",n)}switch(r){case"input":ws(n),Wl(n,i,!0);break;case"textarea":ws(n),Vl(n);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(n.onclick=ii)}n=s,t.updateQueue=n,n!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=id(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=o.createElement(r,{is:n.is}):(e=o.createElement(r),r==="select"&&(o=e,n.multiple?o.multiple=!0:n.size&&(o.size=n.size))):e=o.createElementNS(e,r),e[it]=t,e[Xn]=n,Th(e,t,!1,!1),t.stateNode=e;e:{switch(o=Go(r,n),r){case"dialog":W("cancel",e),W("close",e),s=n;break;case"iframe":case"object":case"embed":W("load",e),s=n;break;case"video":case"audio":for(s=0;s<Nn.length;s++)W(Nn[s],e);s=n;break;case"source":W("error",e),s=n;break;case"img":case"image":case"link":W("error",e),W("load",e),s=n;break;case"details":W("toggle",e),s=n;break;case"input":Bl(e,n),s=Uo(e,n),W("invalid",e);break;case"option":s=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},s=J({},n,{value:void 0}),W("invalid",e);break;case"textarea":Gl(e,n),s=Ho(e,n),W("invalid",e);break;default:s=n}Wo(r,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?ld(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&od(e,u)):i==="children"?typeof u=="string"?(r!=="textarea"||u!=="")&&Bn(e,u):typeof u=="number"&&Bn(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Hn.hasOwnProperty(i)?u!=null&&i==="onScroll"&&W("scroll",e):u!=null&&za(e,i,u,o))}switch(r){case"input":ws(e),Wl(e,n,!1);break;case"textarea":ws(e),Vl(e);break;case"option":n.value!=null&&e.setAttribute("value",""+Vt(n.value));break;case"select":e.multiple=!!n.multiple,i=n.value,i!=null?Wr(e,!!n.multiple,i,!1):n.defaultValue!=null&&Wr(e,!!n.multiple,n.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=ii)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)Ph(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(N(166));if(r=ur(es.current),ur(at.current),Cs(t)){if(n=t.stateNode,r=t.memoizedProps,n[it]=t,(i=n.nodeValue!==r)&&(e=De,e!==null))switch(e.tag){case 3:Ns(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ns(n.nodeValue,r,(e.mode&1)!==0)}i&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[it]=t,t.stateNode=n}return pe(t),null;case 13:if(G(Y),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(V&&Ae!==null&&t.mode&1&&!(t.flags&128))Kd(),Zr(),t.flags|=98560,i=!1;else if(i=Cs(t),n!==null&&n.dehydrated!==null){if(e===null){if(!i)throw Error(N(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(N(317));i[it]=t}else Zr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),i=!1}else Qe!==null&&(ba(Qe),Qe=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?ie===0&&(ie=3):Sl())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return tn(),ya(e,t),e===null&&Jn(t.stateNode.containerInfo),pe(t),null;case 10:return ol(t.type._context),pe(t),null;case 17:return Te(t.type)&&oi(),pe(t),null;case 19:if(G(Y),i=t.memoizedState,i===null)return pe(t),null;if(n=(t.flags&128)!==0,o=i.rendering,o===null)if(n)wn(i,!1);else{if(ie!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=fi(e),o!==null){for(t.flags|=128,wn(i,!1),n=o.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)i=r,e=n,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return B(Y,Y.current&1|2),t.child}e=e.sibling}i.tail!==null&&te()>nn&&(t.flags|=128,n=!0,wn(i,!1),t.lanes=4194304)}else{if(!n)if(e=fi(o),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),wn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!V)return pe(t),null}else 2*te()-i.renderingStartTime>nn&&r!==1073741824&&(t.flags|=128,n=!0,wn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(r=i.last,r!==null?r.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=te(),t.sibling=null,r=Y.current,B(Y,n?r&1|2:r&1),t):(pe(t),null);case 22:case 23:return kl(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&t.mode&1?Re&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function Vm(e,t){switch(rl(t),t.tag){case 1:return Te(t.type)&&oi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return tn(),G(Ce),G(ve),dl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return cl(t),null;case 13:if(G(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));Zr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(Y),null;case 4:return tn(),null;case 10:return ol(t.type._context),null;case 22:case 23:return kl(),null;case 24:return null;default:return null}}var Ps=!1,ge=!1,qm=typeof WeakSet=="function"?WeakSet:Set,O=null;function zr(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){Z(e,t,n)}else r.current=null}function va(e,t,r){try{r()}catch(n){Z(e,t,n)}}var Du=!1;function Ym(e,t){if(ta=ri,e=Ld(),el(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var s=n.anchorOffset,i=n.focusNode;n=n.focusOffset;try{r.nodeType,i.nodeType}catch{r=null;break e}var o=0,a=-1,u=-1,c=0,d=0,p=e,m=null;t:for(;;){for(var y;p!==r||s!==0&&p.nodeType!==3||(a=o+s),p!==i||n!==0&&p.nodeType!==3||(u=o+n),p.nodeType===3&&(o+=p.nodeValue.length),(y=p.firstChild)!==null;)m=p,p=y;for(;;){if(p===e)break t;if(m===r&&++c===s&&(a=o),m===i&&++d===n&&(u=o),(y=p.nextSibling)!==null)break;p=m,m=p.parentNode}p=y}r=a===-1||u===-1?null:{start:a,end:u}}else r=null}r=r||{start:0,end:0}}else r=null;for(ra={focusedElem:e,selectionRange:r},ri=!1,O=t;O!==null;)if(t=O,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,O=e;else for(;O!==null;){t=O;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var x=w.memoizedProps,k=w.memoizedState,f=t.stateNode,h=f.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ye(t.type,x),k);f.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(_){Z(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,O=e;break}O=t.return}return w=Du,Du=!1,w}function Dn(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var s=n=n.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&va(t,r,i)}s=s.next}while(s!==n)}}function Di(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function wa(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Rh(e){var t=e.alternate;t!==null&&(e.alternate=null,Rh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[it],delete t[Xn],delete t[ia],delete t[Om],delete t[Pm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ih(e){return e.tag===5||e.tag===3||e.tag===4}function Lu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ih(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function xa(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=ii));else if(n!==4&&(e=e.child,e!==null))for(xa(e,t,r),e=e.sibling;e!==null;)xa(e,t,r),e=e.sibling}function _a(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(_a(e,t,r),e=e.sibling;e!==null;)_a(e,t,r),e=e.sibling}var ce=null,Ke=!1;function kt(e,t,r){for(r=r.child;r!==null;)Ah(e,t,r),r=r.sibling}function Ah(e,t,r){if(ot&&typeof ot.onCommitFiberUnmount=="function")try{ot.onCommitFiberUnmount(Ni,r)}catch{}switch(r.tag){case 5:ge||zr(r,t);case 6:var n=ce,s=Ke;ce=null,kt(e,t,r),ce=n,Ke=s,ce!==null&&(Ke?(e=ce,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):ce.removeChild(r.stateNode));break;case 18:ce!==null&&(Ke?(e=ce,r=r.stateNode,e.nodeType===8?mo(e.parentNode,r):e.nodeType===1&&mo(e,r),qn(e)):mo(ce,r.stateNode));break;case 4:n=ce,s=Ke,ce=r.stateNode.containerInfo,Ke=!0,kt(e,t,r),ce=n,Ke=s;break;case 0:case 11:case 14:case 15:if(!ge&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){s=n=n.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&va(r,t,o),s=s.next}while(s!==n)}kt(e,t,r);break;case 1:if(!ge&&(zr(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(a){Z(r,t,a)}kt(e,t,r);break;case 21:kt(e,t,r);break;case 22:r.mode&1?(ge=(n=ge)||r.memoizedState!==null,kt(e,t,r),ge=n):kt(e,t,r);break;default:kt(e,t,r)}}function Mu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new qm),t.forEach(function(n){var s=ng.bind(null,e,n);r.has(n)||(r.add(n),n.then(s,s))})}}function qe(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var s=r[n];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ce=a.stateNode,Ke=!1;break e;case 3:ce=a.stateNode.containerInfo,Ke=!0;break e;case 4:ce=a.stateNode.containerInfo,Ke=!0;break e}a=a.return}if(ce===null)throw Error(N(160));Ah(i,o,s),ce=null,Ke=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){Z(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Dh(t,e),t=t.sibling}function Dh(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(qe(t,e),rt(e),n&4){try{Dn(3,e,e.return),Di(3,e)}catch(x){Z(e,e.return,x)}try{Dn(5,e,e.return)}catch(x){Z(e,e.return,x)}}break;case 1:qe(t,e),rt(e),n&512&&r!==null&&zr(r,r.return);break;case 5:if(qe(t,e),rt(e),n&512&&r!==null&&zr(r,r.return),e.flags&32){var s=e.stateNode;try{Bn(s,"")}catch(x){Z(e,e.return,x)}}if(n&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=r!==null?r.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&nd(s,i),Go(a,o);var c=Go(a,i);for(o=0;o<u.length;o+=2){var d=u[o],p=u[o+1];d==="style"?ld(s,p):d==="dangerouslySetInnerHTML"?od(s,p):d==="children"?Bn(s,p):za(s,d,p,c)}switch(a){case"input":Fo(s,i);break;case"textarea":sd(s,i);break;case"select":var m=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?Wr(s,!!i.multiple,y,!1):m!==!!i.multiple&&(i.defaultValue!=null?Wr(s,!!i.multiple,i.defaultValue,!0):Wr(s,!!i.multiple,i.multiple?[]:"",!1))}s[Xn]=i}catch(x){Z(e,e.return,x)}}break;case 6:if(qe(t,e),rt(e),n&4){if(e.stateNode===null)throw Error(N(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(x){Z(e,e.return,x)}}break;case 3:if(qe(t,e),rt(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{qn(t.containerInfo)}catch(x){Z(e,e.return,x)}break;case 4:qe(t,e),rt(e);break;case 13:qe(t,e),rt(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(xl=te())),n&4&&Mu(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(ge=(c=ge)||d,qe(t,e),ge=c):qe(t,e),rt(e),n&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(O=e,d=e.child;d!==null;){for(p=O=d;O!==null;){switch(m=O,y=m.child,m.tag){case 0:case 11:case 14:case 15:Dn(4,m,m.return);break;case 1:zr(m,m.return);var w=m.stateNode;if(typeof w.componentWillUnmount=="function"){n=m,r=m.return;try{t=n,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(x){Z(n,r,x)}}break;case 5:zr(m,m.return);break;case 22:if(m.memoizedState!==null){Uu(p);continue}}y!==null?(y.return=m,O=y):Uu(p)}d=d.sibling}e:for(d=null,p=e;;){if(p.tag===5){if(d===null){d=p;try{s=p.stateNode,c?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=p.stateNode,u=p.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=ad("display",o))}catch(x){Z(e,e.return,x)}}}else if(p.tag===6){if(d===null)try{p.stateNode.nodeValue=c?"":p.memoizedProps}catch(x){Z(e,e.return,x)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;d===p&&(d=null),p=p.return}d===p&&(d=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:qe(t,e),rt(e),n&4&&Mu(e);break;case 21:break;default:qe(t,e),rt(e)}}function rt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Ih(r)){var n=r;break e}r=r.return}throw Error(N(160))}switch(n.tag){case 5:var s=n.stateNode;n.flags&32&&(Bn(s,""),n.flags&=-33);var i=Lu(e);_a(e,i,s);break;case 3:case 4:var o=n.stateNode.containerInfo,a=Lu(e);xa(e,a,o);break;default:throw Error(N(161))}}catch(u){Z(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Km(e,t,r){O=e,Lh(e)}function Lh(e,t,r){for(var n=(e.mode&1)!==0;O!==null;){var s=O,i=s.child;if(s.tag===22&&n){var o=s.memoizedState!==null||Ps;if(!o){var a=s.alternate,u=a!==null&&a.memoizedState!==null||ge;a=Ps;var c=ge;if(Ps=o,(ge=u)&&!c)for(O=s;O!==null;)o=O,u=o.child,o.tag===22&&o.memoizedState!==null?Fu(s):u!==null?(u.return=o,O=u):Fu(s);for(;i!==null;)O=i,Lh(i),i=i.sibling;O=s,Ps=a,ge=c}$u(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,O=i):$u(e)}}function $u(e){for(;O!==null;){var t=O;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ge||Di(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!ge)if(r===null)n.componentDidMount();else{var s=t.elementType===t.type?r.memoizedProps:Ye(t.type,r.memoizedProps);n.componentDidUpdate(s,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ku(t,i,n);break;case 3:var o=t.updateQueue;if(o!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}ku(t,o,r)}break;case 5:var a=t.stateNode;if(r===null&&t.flags&4){r=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&r.focus();break;case"img":u.src&&(r.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var p=d.dehydrated;p!==null&&qn(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}ge||t.flags&512&&wa(t)}catch(m){Z(t,t.return,m)}}if(t===e){O=null;break}if(r=t.sibling,r!==null){r.return=t.return,O=r;break}O=t.return}}function Uu(e){for(;O!==null;){var t=O;if(t===e){O=null;break}var r=t.sibling;if(r!==null){r.return=t.return,O=r;break}O=t.return}}function Fu(e){for(;O!==null;){var t=O;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{Di(4,t)}catch(u){Z(t,r,u)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var s=t.return;try{n.componentDidMount()}catch(u){Z(t,s,u)}}var i=t.return;try{wa(t)}catch(u){Z(t,i,u)}break;case 5:var o=t.return;try{wa(t)}catch(u){Z(t,o,u)}}}catch(u){Z(t,t.return,u)}if(t===e){O=null;break}var a=t.sibling;if(a!==null){a.return=t.return,O=a;break}O=t.return}}var Jm=Math.ceil,gi=_t.ReactCurrentDispatcher,vl=_t.ReactCurrentOwner,Be=_t.ReactCurrentBatchConfig,U=0,le=null,ne=null,de=0,Re=0,Hr=Kt(0),ie=0,ss=null,gr=0,Li=0,wl=0,Ln=null,be=null,xl=0,nn=1/0,ct=null,yi=!1,ka=null,Ft=null,Rs=!1,At=null,vi=0,Mn=0,Sa=null,qs=-1,Ys=0;function xe(){return U&6?te():qs!==-1?qs:qs=te()}function zt(e){return e.mode&1?U&2&&de!==0?de&-de:Im.transition!==null?(Ys===0&&(Ys=xd()),Ys):(e=z,e!==0||(e=window.event,e=e===void 0?16:Nd(e.type)),e):1}function Ze(e,t,r,n){if(50<Mn)throw Mn=0,Sa=null,Error(N(185));us(e,r,n),(!(U&2)||e!==le)&&(e===le&&(!(U&2)&&(Li|=r),ie===4&&Pt(e,de)),Oe(e,n),r===1&&U===0&&!(t.mode&1)&&(nn=te()+500,Ri&&Jt()))}function Oe(e,t){var r=e.callbackNode;Ip(e,t);var n=ti(e,e===le?de:0);if(n===0)r!==null&&Kl(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&Kl(r),t===1)e.tag===0?Rm(zu.bind(null,e)):Vd(zu.bind(null,e)),Cm(function(){!(U&6)&&Jt()}),r=null;else{switch(_d(n)){case 1:r=Va;break;case 4:r=vd;break;case 16:r=ei;break;case 536870912:r=wd;break;default:r=ei}r=Wh(r,Mh.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function Mh(e,t){if(qs=-1,Ys=0,U&6)throw Error(N(327));var r=e.callbackNode;if(Kr()&&e.callbackNode!==r)return null;var n=ti(e,e===le?de:0);if(n===0)return null;if(n&30||n&e.expiredLanes||t)t=wi(e,n);else{t=n;var s=U;U|=2;var i=Uh();(le!==e||de!==t)&&(ct=null,nn=te()+500,cr(e,t));do try{Zm();break}catch(a){$h(e,a)}while(1);il(),gi.current=i,U=s,ne!==null?t=0:(le=null,de=0,t=ie)}if(t!==0){if(t===2&&(s=Jo(e),s!==0&&(n=s,t=Ea(e,s))),t===1)throw r=ss,cr(e,0),Pt(e,n),Oe(e,te()),r;if(t===6)Pt(e,n);else{if(s=e.current.alternate,!(n&30)&&!Qm(s)&&(t=wi(e,n),t===2&&(i=Jo(e),i!==0&&(n=i,t=Ea(e,i))),t===1))throw r=ss,cr(e,0),Pt(e,n),Oe(e,te()),r;switch(e.finishedWork=s,e.finishedLanes=n,t){case 0:case 1:throw Error(N(345));case 2:nr(e,be,ct);break;case 3:if(Pt(e,n),(n&130023424)===n&&(t=xl+500-te(),10<t)){if(ti(e,0)!==0)break;if(s=e.suspendedLanes,(s&n)!==n){xe(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=sa(nr.bind(null,e,be,ct),t);break}nr(e,be,ct);break;case 4:if(Pt(e,n),(n&4194240)===n)break;for(t=e.eventTimes,s=-1;0<n;){var o=31-Xe(n);i=1<<o,o=t[o],o>s&&(s=o),n&=~i}if(n=s,n=te()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Jm(n/1960))-n,10<n){e.timeoutHandle=sa(nr.bind(null,e,be,ct),n);break}nr(e,be,ct);break;case 5:nr(e,be,ct);break;default:throw Error(N(329))}}}return Oe(e,te()),e.callbackNode===r?Mh.bind(null,e):null}function Ea(e,t){var r=Ln;return e.current.memoizedState.isDehydrated&&(cr(e,t).flags|=256),e=wi(e,t),e!==2&&(t=be,be=r,t!==null&&ba(t)),e}function ba(e){be===null?be=e:be.push.apply(be,e)}function Qm(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var s=r[n],i=s.getSnapshot;s=s.value;try{if(!et(i(),s))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Pt(e,t){for(t&=~wl,t&=~Li,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-Xe(t),n=1<<r;e[r]=-1,t&=~n}}function zu(e){if(U&6)throw Error(N(327));Kr();var t=ti(e,0);if(!(t&1))return Oe(e,te()),null;var r=wi(e,t);if(e.tag!==0&&r===2){var n=Jo(e);n!==0&&(t=n,r=Ea(e,n))}if(r===1)throw r=ss,cr(e,0),Pt(e,t),Oe(e,te()),r;if(r===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,nr(e,be,ct),Oe(e,te()),null}function _l(e,t){var r=U;U|=1;try{return e(t)}finally{U=r,U===0&&(nn=te()+500,Ri&&Jt())}}function yr(e){At!==null&&At.tag===0&&!(U&6)&&Kr();var t=U;U|=1;var r=Be.transition,n=z;try{if(Be.transition=null,z=1,e)return e()}finally{z=n,Be.transition=r,U=t,!(U&6)&&Jt()}}function kl(){Re=Hr.current,G(Hr)}function cr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Nm(r)),ne!==null)for(r=ne.return;r!==null;){var n=r;switch(rl(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&oi();break;case 3:tn(),G(Ce),G(ve),dl();break;case 5:cl(n);break;case 4:tn();break;case 13:G(Y);break;case 19:G(Y);break;case 10:ol(n.type._context);break;case 22:case 23:kl()}r=r.return}if(le=e,ne=e=Ht(e.current,null),de=Re=t,ie=0,ss=null,wl=Li=gr=0,be=Ln=null,lr!==null){for(t=0;t<lr.length;t++)if(r=lr[t],n=r.interleaved,n!==null){r.interleaved=null;var s=n.next,i=r.pending;if(i!==null){var o=i.next;i.next=s,n.next=o}r.pending=n}lr=null}return e}function $h(e,t){do{var r=ne;try{if(il(),Ws.current=mi,pi){for(var n=K.memoizedState;n!==null;){var s=n.queue;s!==null&&(s.pending=null),n=n.next}pi=!1}if(mr=0,ae=se=K=null,An=!1,ts=0,vl.current=null,r===null||r.return===null){ie=1,ss=t,ne=null;break}e:{var i=e,o=r.return,a=r,u=t;if(t=de,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,d=a,p=d.tag;if(!(d.mode&1)&&(p===0||p===11||p===15)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=Cu(o);if(y!==null){y.flags&=-257,Tu(y,o,a,i,t),y.mode&1&&Nu(i,c,t),t=y,u=c;var w=t.updateQueue;if(w===null){var x=new Set;x.add(u),t.updateQueue=x}else w.add(u);break e}else{if(!(t&1)){Nu(i,c,t),Sl();break e}u=Error(N(426))}}else if(V&&a.mode&1){var k=Cu(o);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Tu(k,o,a,i,t),nl(rn(u,a));break e}}i=u=rn(u,a),ie!==4&&(ie=2),Ln===null?Ln=[i]:Ln.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var f=_h(i,u,t);_u(i,f);break e;case 1:a=u;var h=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Ft===null||!Ft.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var _=kh(i,a,t);_u(i,_);break e}}i=i.return}while(i!==null)}zh(r)}catch(S){t=S,ne===r&&r!==null&&(ne=r=r.return);continue}break}while(1)}function Uh(){var e=gi.current;return gi.current=mi,e===null?mi:e}function Sl(){(ie===0||ie===3||ie===2)&&(ie=4),le===null||!(gr&268435455)&&!(Li&268435455)||Pt(le,de)}function wi(e,t){var r=U;U|=2;var n=Uh();(le!==e||de!==t)&&(ct=null,cr(e,t));do try{Xm();break}catch(s){$h(e,s)}while(1);if(il(),U=r,gi.current=n,ne!==null)throw Error(N(261));return le=null,de=0,ie}function Xm(){for(;ne!==null;)Fh(ne)}function Zm(){for(;ne!==null&&!Ep();)Fh(ne)}function Fh(e){var t=Bh(e.alternate,e,Re);e.memoizedProps=e.pendingProps,t===null?zh(e):ne=t,vl.current=null}function zh(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=Vm(r,t),r!==null){r.flags&=32767,ne=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ie=6,ne=null;return}}else if(r=Gm(r,t,Re),r!==null){ne=r;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);ie===0&&(ie=5)}function nr(e,t,r){var n=z,s=Be.transition;try{Be.transition=null,z=1,eg(e,t,r,n)}finally{Be.transition=s,z=n}return null}function eg(e,t,r,n){do Kr();while(At!==null);if(U&6)throw Error(N(327));r=e.finishedWork;var s=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var i=r.lanes|r.childLanes;if(Ap(e,i),e===le&&(ne=le=null,de=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||Rs||(Rs=!0,Wh(ei,function(){return Kr(),null})),i=(r.flags&15990)!==0,r.subtreeFlags&15990||i){i=Be.transition,Be.transition=null;var o=z;z=1;var a=U;U|=4,vl.current=null,Ym(e,r),Dh(r,e),xm(ra),ri=!!ta,ra=ta=null,e.current=r,Km(r),bp(),U=a,z=o,Be.transition=i}else e.current=r;if(Rs&&(Rs=!1,At=e,vi=s),i=e.pendingLanes,i===0&&(Ft=null),Cp(r.stateNode),Oe(e,te()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)s=t[r],n(s.value,{componentStack:s.stack,digest:s.digest});if(yi)throw yi=!1,e=ka,ka=null,e;return vi&1&&e.tag!==0&&Kr(),i=e.pendingLanes,i&1?e===Sa?Mn++:(Mn=0,Sa=e):Mn=0,Jt(),null}function Kr(){if(At!==null){var e=_d(vi),t=Be.transition,r=z;try{if(Be.transition=null,z=16>e?16:e,At===null)var n=!1;else{if(e=At,At=null,vi=0,U&6)throw Error(N(331));var s=U;for(U|=4,O=e.current;O!==null;){var i=O,o=i.child;if(O.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(O=c;O!==null;){var d=O;switch(d.tag){case 0:case 11:case 15:Dn(8,d,i)}var p=d.child;if(p!==null)p.return=d,O=p;else for(;O!==null;){d=O;var m=d.sibling,y=d.return;if(Rh(d),d===c){O=null;break}if(m!==null){m.return=y,O=m;break}O=y}}}var w=i.alternate;if(w!==null){var x=w.child;if(x!==null){w.child=null;do{var k=x.sibling;x.sibling=null,x=k}while(x!==null)}}O=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,O=o;else e:for(;O!==null;){if(i=O,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Dn(9,i,i.return)}var f=i.sibling;if(f!==null){f.return=i.return,O=f;break e}O=i.return}}var h=e.current;for(O=h;O!==null;){o=O;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,O=g;else e:for(o=h;O!==null;){if(a=O,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Di(9,a)}}catch(S){Z(a,a.return,S)}if(a===o){O=null;break e}var _=a.sibling;if(_!==null){_.return=a.return,O=_;break e}O=a.return}}if(U=s,Jt(),ot&&typeof ot.onPostCommitFiberRoot=="function")try{ot.onPostCommitFiberRoot(Ni,e)}catch{}n=!0}return n}finally{z=r,Be.transition=t}}return!1}function Hu(e,t,r){t=rn(r,t),t=_h(e,t,1),e=Ut(e,t,1),t=xe(),e!==null&&(us(e,1,t),Oe(e,t))}function Z(e,t,r){if(e.tag===3)Hu(e,e,r);else for(;t!==null;){if(t.tag===3){Hu(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Ft===null||!Ft.has(n))){e=rn(r,e),e=kh(t,e,1),t=Ut(t,e,1),e=xe(),t!==null&&(us(t,1,e),Oe(t,e));break}}t=t.return}}function tg(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&r,le===e&&(de&r)===r&&(ie===4||ie===3&&(de&130023424)===de&&500>te()-xl?cr(e,0):wl|=r),Oe(e,t)}function Hh(e,t){t===0&&(e.mode&1?(t=ks,ks<<=1,!(ks&130023424)&&(ks=4194304)):t=1);var r=xe();e=vt(e,t),e!==null&&(us(e,t,r),Oe(e,r))}function rg(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),Hh(e,r)}function ng(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,s=e.memoizedState;s!==null&&(r=s.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(N(314))}n!==null&&n.delete(t),Hh(e,r)}var Bh;Bh=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ce.current)Ne=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return Ne=!1,Wm(e,t,r);Ne=!!(e.flags&131072)}else Ne=!1,V&&t.flags&1048576&&qd(t,ui,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;Vs(e,t),e=t.pendingProps;var s=Xr(t,ve.current);Yr(t,r),s=fl(null,t,n,e,s,r);var i=pl();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(n)?(i=!0,ai(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,ll(t),s.updater=Ai,t.stateNode=s,s._reactInternals=t,da(t,n,e,r),t=pa(null,t,n,!0,i,r)):(t.tag=0,V&&i&&tl(t),we(null,t,s,r),t=t.child),t;case 16:n=t.elementType;e:{switch(Vs(e,t),e=t.pendingProps,s=n._init,n=s(n._payload),t.type=n,s=t.tag=ig(n),e=Ye(n,e),s){case 0:t=fa(null,t,n,e,r);break e;case 1:t=Ru(null,t,n,e,r);break e;case 11:t=Ou(null,t,n,e,r);break e;case 14:t=Pu(null,t,n,Ye(n.type,e),r);break e}throw Error(N(306,n,""))}return t;case 0:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Ye(n,s),fa(e,t,n,s,r);case 1:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Ye(n,s),Ru(e,t,n,s,r);case 3:e:{if(jh(t),e===null)throw Error(N(387));n=t.pendingProps,i=t.memoizedState,s=i.element,Zd(e,t),hi(t,n,null,r);var o=t.memoizedState;if(n=o.element,i.isDehydrated)if(i={element:n,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=rn(Error(N(423)),t),t=Iu(e,t,n,r,s);break e}else if(n!==s){s=rn(Error(N(424)),t),t=Iu(e,t,n,r,s);break e}else for(Ae=$t(t.stateNode.containerInfo.firstChild),De=t,V=!0,Qe=null,r=Qd(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Zr(),n===s){t=wt(e,t,r);break e}we(e,t,n,r)}t=t.child}return t;case 5:return eh(t),e===null&&la(t),n=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,na(n,s)?o=null:i!==null&&na(n,i)&&(t.flags|=32),bh(e,t),we(e,t,o,r),t.child;case 6:return e===null&&la(t),null;case 13:return Nh(e,t,r);case 4:return ul(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=en(t,null,n,r):we(e,t,n,r),t.child;case 11:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Ye(n,s),Ou(e,t,n,s,r);case 7:return we(e,t,t.pendingProps,r),t.child;case 8:return we(e,t,t.pendingProps.children,r),t.child;case 12:return we(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,B(ci,n._currentValue),n._currentValue=o,i!==null)if(et(i.value,o)){if(i.children===s.children&&!Ce.current){t=wt(e,t,r);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var u=a.firstContext;u!==null;){if(u.context===n){if(i.tag===1){u=mt(-1,r&-r),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=r,u=i.alternate,u!==null&&(u.lanes|=r),ua(i.return,r,t),a.lanes|=r;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(N(341));o.lanes|=r,a=o.alternate,a!==null&&(a.lanes|=r),ua(o,r,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}we(e,t,s.children,r),t=t.child}return t;case 9:return s=t.type,n=t.pendingProps.children,Yr(t,r),s=Ge(s),n=n(s),t.flags|=1,we(e,t,n,r),t.child;case 14:return n=t.type,s=Ye(n,t.pendingProps),s=Ye(n.type,s),Pu(e,t,n,s,r);case 15:return Sh(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Ye(n,s),Vs(e,t),t.tag=1,Te(n)?(e=!0,ai(t)):e=!1,Yr(t,r),xh(t,n,s),da(t,n,s,r),pa(null,t,n,!0,e,r);case 19:return Ch(e,t,r);case 22:return Eh(e,t,r)}throw Error(N(156,t.tag))};function Wh(e,t){return yd(e,t)}function sg(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function He(e,t,r,n){return new sg(e,t,r,n)}function El(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ig(e){if(typeof e=="function")return El(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ba)return 11;if(e===Wa)return 14}return 2}function Ht(e,t){var r=e.alternate;return r===null?(r=He(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function Ks(e,t,r,n,s,i){var o=2;if(n=e,typeof e=="function")El(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Rr:return dr(r.children,s,i,t);case Ha:o=8,s|=8;break;case Do:return e=He(12,r,t,s|2),e.elementType=Do,e.lanes=i,e;case Lo:return e=He(13,r,t,s),e.elementType=Lo,e.lanes=i,e;case Mo:return e=He(19,r,t,s),e.elementType=Mo,e.lanes=i,e;case ed:return Mi(r,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Xc:o=10;break e;case Zc:o=9;break e;case Ba:o=11;break e;case Wa:o=14;break e;case jt:o=16,n=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=He(o,r,t,s),t.elementType=e,t.type=n,t.lanes=i,t}function dr(e,t,r,n){return e=He(7,e,n,t),e.lanes=r,e}function Mi(e,t,r,n){return e=He(22,e,n,t),e.elementType=ed,e.lanes=r,e.stateNode={isHidden:!1},e}function So(e,t,r){return e=He(6,e,null,t),e.lanes=r,e}function Eo(e,t,r){return t=He(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function og(e,t,r,n,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=no(0),this.expirationTimes=no(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=no(0),this.identifierPrefix=n,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function bl(e,t,r,n,s,i,o,a,u){return e=new og(e,t,r,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=He(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},ll(i),e}function ag(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Pr,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function Gh(e){if(!e)return qt;e=e._reactInternals;e:{if(_r(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var r=e.type;if(Te(r))return Gd(e,r,t)}return t}function Vh(e,t,r,n,s,i,o,a,u){return e=bl(r,n,!0,e,s,i,o,a,u),e.context=Gh(null),r=e.current,n=xe(),s=zt(r),i=mt(n,s),i.callback=t??null,Ut(r,i,s),e.current.lanes=s,us(e,s,n),Oe(e,n),e}function $i(e,t,r,n){var s=t.current,i=xe(),o=zt(s);return r=Gh(r),t.context===null?t.context=r:t.pendingContext=r,t=mt(i,o),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=Ut(s,t,o),e!==null&&(Ze(e,s,o,i),Bs(e,s,o)),o}function xi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Bu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function jl(e,t){Bu(e,t),(e=e.alternate)&&Bu(e,t)}function lg(){return null}var qh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Nl(e){this._internalRoot=e}Ui.prototype.render=Nl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));$i(e,t,null,null)};Ui.prototype.unmount=Nl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;yr(function(){$i(null,e,null,null)}),t[yt]=null}};function Ui(e){this._internalRoot=e}Ui.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ed();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Ot.length&&t!==0&&t<Ot[r].priority;r++);Ot.splice(r,0,e),r===0&&jd(e)}};function Cl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Fi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Wu(){}function ug(e,t,r,n,s){if(s){if(typeof n=="function"){var i=n;n=function(){var c=xi(o);i.call(c)}}var o=Vh(t,n,e,0,null,!1,!1,"",Wu);return e._reactRootContainer=o,e[yt]=o.current,Jn(e.nodeType===8?e.parentNode:e),yr(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof n=="function"){var a=n;n=function(){var c=xi(u);a.call(c)}}var u=bl(e,0,!1,null,null,!1,!1,"",Wu);return e._reactRootContainer=u,e[yt]=u.current,Jn(e.nodeType===8?e.parentNode:e),yr(function(){$i(t,u,r,n)}),u}function zi(e,t,r,n,s){var i=r._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var u=xi(o);a.call(u)}}$i(t,o,e,s)}else o=ug(r,t,e,s,n);return xi(o)}kd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=jn(t.pendingLanes);r!==0&&(qa(t,r|1),Oe(t,te()),!(U&6)&&(nn=te()+500,Jt()))}break;case 13:yr(function(){var n=vt(e,1);if(n!==null){var s=xe();Ze(n,e,1,s)}}),jl(e,1)}};Ya=function(e){if(e.tag===13){var t=vt(e,134217728);if(t!==null){var r=xe();Ze(t,e,134217728,r)}jl(e,134217728)}};Sd=function(e){if(e.tag===13){var t=zt(e),r=vt(e,t);if(r!==null){var n=xe();Ze(r,e,t,n)}jl(e,t)}};Ed=function(){return z};bd=function(e,t){var r=z;try{return z=e,t()}finally{z=r}};qo=function(e,t,r){switch(t){case"input":if(Fo(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var s=Pi(n);if(!s)throw Error(N(90));rd(n),Fo(n,s)}}}break;case"textarea":sd(e,r);break;case"select":t=r.value,t!=null&&Wr(e,!!r.multiple,t,!1)}};dd=_l;hd=yr;var cg={usingClientEntryPoint:!1,Events:[ds,Lr,Pi,ud,cd,_l]},xn={findFiberByHostInstance:ar,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},dg={bundleType:xn.bundleType,version:xn.version,rendererPackageName:xn.rendererPackageName,rendererConfig:xn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:_t.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=md(e),e===null?null:e.stateNode},findFiberByHostInstance:xn.findFiberByHostInstance||lg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Is=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Is.isDisabled&&Is.supportsFiber)try{Ni=Is.inject(dg),ot=Is}catch{}}Me.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=cg;Me.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Cl(t))throw Error(N(200));return ag(e,t,null,r)};Me.createRoot=function(e,t){if(!Cl(e))throw Error(N(299));var r=!1,n="",s=qh;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=bl(e,1,!1,null,null,r,!1,n,s),e[yt]=t.current,Jn(e.nodeType===8?e.parentNode:e),new Nl(t)};Me.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=md(t),e=e===null?null:e.stateNode,e};Me.flushSync=function(e){return yr(e)};Me.hydrate=function(e,t,r){if(!Fi(t))throw Error(N(200));return zi(null,e,t,!0,r)};Me.hydrateRoot=function(e,t,r){if(!Cl(e))throw Error(N(405));var n=r!=null&&r.hydratedSources||null,s=!1,i="",o=qh;if(r!=null&&(r.unstable_strictMode===!0&&(s=!0),r.identifierPrefix!==void 0&&(i=r.identifierPrefix),r.onRecoverableError!==void 0&&(o=r.onRecoverableError)),t=Vh(t,null,e,1,r??null,s,!1,i,o),e[yt]=t.current,Jn(e),n)for(e=0;e<n.length;e++)r=n[e],s=r._getVersion,s=s(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,s]:t.mutableSourceEagerHydrationData.push(r,s);return new Ui(t)};Me.render=function(e,t,r){if(!Fi(t))throw Error(N(200));return zi(null,e,t,!1,r)};Me.unmountComponentAtNode=function(e){if(!Fi(e))throw Error(N(40));return e._reactRootContainer?(yr(function(){zi(null,null,e,!1,function(){e._reactRootContainer=null,e[yt]=null})}),!0):!1};Me.unstable_batchedUpdates=_l;Me.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!Fi(r))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return zi(e,t,r,!1,n)};Me.version="18.3.1-next-f1338f8080-20240426";function Yh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Yh)}catch(e){console.error(e)}}Yh(),Yc.exports=Me;var hg=Yc.exports,Gu=hg;Io.createRoot=Gu.createRoot,Io.hydrateRoot=Gu.hydrateRoot;var fg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const pg=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mg=(e,t)=>{const r=I.forwardRef(({color:n="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:a,...u},c)=>I.createElement("svg",{ref:c,...fg,width:s,height:s,stroke:n,strokeWidth:o?Number(i)*24/Number(s):i,className:`lucide lucide-${pg(e)}`,...u},[...t.map(([d,p])=>I.createElement(d,p)),...(Array.isArray(a)?a:[a])||[]]));return r.displayName=`${e}`,r};var M=mg;const _i=M("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),gg=M("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),Vu=M("Bot",[["rect",{width:"18",height:"10",x:"3",y:"11",rx:"2",key:"1ofdy3"}],["circle",{cx:"12",cy:"5",r:"2",key:"f1ur92"}],["path",{d:"M12 7v4",key:"xawao1"}],["line",{x1:"8",x2:"8",y1:"16",y2:"16",key:"h6x27f"}],["line",{x1:"16",x2:"16",y1:"16",y2:"16",key:"5lty7f"}]]),yg=M("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),hr=M("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Tl=M("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),vg=M("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),wg=M("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Kh=M("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),xg=M("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),Jh=M("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),_g=M("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),Qh=M("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]),kg=M("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]),Sg=M("LayoutList",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}],["path",{d:"M14 4h7",key:"3xa0d5"}],["path",{d:"M14 9h7",key:"1icrd9"}],["path",{d:"M14 15h7",key:"1mj8o2"}],["path",{d:"M14 20h7",key:"11slyb"}]]),Bt=M("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),sn=M("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),qu=M("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),Eg=M("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),bg=M("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),bo=M("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),jg=M("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),Ng=M("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]]),Xh=M("PenTool",[["path",{d:"m12 19 7-7 3 3-7 7-3-3z",key:"rklqx2"}],["path",{d:"m18 13-1.5-7.5L2 2l3.5 14.5L13 18l5-5z",key:"1et58u"}],["path",{d:"m2 2 7.586 7.586",key:"etlp93"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]),Cg=M("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),Wt=M("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Yu=M("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Tg=M("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),Og=M("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Pg=M("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]),Rg=M("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),ja=M("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Zh=M("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),Ku=M("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),ef=M("Wand2",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z",key:"1bcowg"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]),vr=M("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),tf=M("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),P={IDEA:"idea",PLANNING:"planning",IN_PROGRESS:"in_progress",REVIEW:"review",COMPLETED:"completed",ON_HOLD:"on_hold",CANCELLED:"cancelled"},Ie={LOW:"low",MEDIUM:"medium",HIGH:"high",URGENT:"urgent"},me={FEATURE:"feature",BUG_FIX:"bug_fix",IMPROVEMENT:"improvement",RESEARCH:"research",DOCUMENTATION:"documentation",TESTING:"testing",OTHER:"other"},Ig="modulepreload",Ag=function(e){return"/"+e},Ju={},on=function(t,r,n){if(!r||r.length===0)return t();const s=document.getElementsByTagName("link");return Promise.all(r.map(i=>{if(i=Ag(i),i in Ju)return;Ju[i]=!0;const o=i.endsWith(".css"),a=o?'[rel="stylesheet"]':"";if(!!n)for(let d=s.length-1;d>=0;d--){const p=s[d];if(p.href===i&&(!o||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${a}`))return;const c=document.createElement("link");if(c.rel=o?"stylesheet":Ig,o||(c.as="script",c.crossOrigin=""),c.href=i,document.head.appendChild(c),o)return new Promise((d,p)=>{c.addEventListener("load",d),c.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=i,window.dispatchEvent(o),!o.defaultPrevented)throw i})},Dg=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...r)=>on(()=>Promise.resolve().then(()=>dn),void 0).then(({default:n})=>n(...r)):t=fetch,(...r)=>t(...r)};class Ol extends Error{constructor(t,r="FunctionsError",n){super(t),this.name=r,this.context=n}}class Lg extends Ol{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class Mg extends Ol{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class $g extends Ol{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var Na;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(Na||(Na={}));var Ug=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};class Fg{constructor(t,{headers:r={},customFetch:n,region:s=Na.Any}={}){this.url=t,this.headers=r,this.region=s,this.fetch=Dg(n)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,r={}){var n;return Ug(this,void 0,void 0,function*(){try{const{headers:s,method:i,body:o}=r;let a={},{region:u}=r;u||(u=this.region),u&&u!=="any"&&(a["x-region"]=u);let c;o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",c=o):typeof o=="string"?(a["Content-Type"]="text/plain",c=o):typeof FormData<"u"&&o instanceof FormData?c=o:(a["Content-Type"]="application/json",c=JSON.stringify(o)));const d=yield this.fetch(`${this.url}/${t}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),s),body:c}).catch(w=>{throw new Lg(w)}),p=d.headers.get("x-relay-error");if(p&&p==="true")throw new Mg(d);if(!d.ok)throw new $g(d);let m=((n=d.headers.get("Content-Type"))!==null&&n!==void 0?n:"text/plain").split(";")[0].trim(),y;return m==="application/json"?y=yield d.json():m==="application/octet-stream"?y=yield d.blob():m==="text/event-stream"?y=d:m==="multipart/form-data"?y=yield d.formData():y=yield d.text(),{data:y,error:null}}catch(s){return{data:null,error:s}}})}}var je={},Pl={},Hi={},fs={},Bi={},Wi={},zg=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},an=zg();const Hg=an.fetch,rf=an.fetch.bind(an),nf=an.Headers,Bg=an.Request,Wg=an.Response,dn=Object.freeze(Object.defineProperty({__proto__:null,Headers:nf,Request:Bg,Response:Wg,default:rf,fetch:Hg},Symbol.toStringTag,{value:"Module"})),Gg=Ff(dn);var Gi={};Object.defineProperty(Gi,"__esModule",{value:!0});let Vg=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};Gi.default=Vg;var sf=We&&We.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Wi,"__esModule",{value:!0});const qg=sf(Gg),Yg=sf(Gi);let Kg=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=qg.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,r){return this.headers=Object.assign({},this.headers),this.headers[t]=r,this}then(t,r){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const n=this.fetch;let s=n(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async i=>{var o,a,u;let c=null,d=null,p=null,m=i.status,y=i.statusText;if(i.ok){if(this.method!=="HEAD"){const f=await i.text();f===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?d=f:d=JSON.parse(f))}const x=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),k=(a=i.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");x&&k&&k.length>1&&(p=parseInt(k[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(d)&&(d.length>1?(c={code:"PGRST116",details:`Results contain ${d.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},d=null,p=null,m=406,y="Not Acceptable"):d.length===1?d=d[0]:d=null)}else{const x=await i.text();try{c=JSON.parse(x),Array.isArray(c)&&i.status===404&&(d=[],c=null,m=200,y="OK")}catch{i.status===404&&x===""?(m=204,y="No Content"):c={message:x}}if(c&&this.isMaybeSingle&&(!((u=c==null?void 0:c.details)===null||u===void 0)&&u.includes("0 rows"))&&(c=null,m=200,y="OK"),c&&this.shouldThrowOnError)throw new Yg.default(c)}return{error:c,data:d,count:p,status:m,statusText:y}});return this.shouldThrowOnError||(s=s.catch(i=>{var o,a,u;return{error:{message:`${(o=i==null?void 0:i.name)!==null&&o!==void 0?o:"FetchError"}: ${i==null?void 0:i.message}`,details:`${(a=i==null?void 0:i.stack)!==null&&a!==void 0?a:""}`,hint:"",code:`${(u=i==null?void 0:i.code)!==null&&u!==void 0?u:""}`},data:null,count:null,status:0,statusText:""}})),s.then(t,r)}returns(){return this}overrideTypes(){return this}};Wi.default=Kg;var Jg=We&&We.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Bi,"__esModule",{value:!0});const Qg=Jg(Wi);let Xg=class extends Qg.default{select(t){let r=!1;const n=(t??"*").split("").map(s=>/\s/.test(s)&&!r?"":(s==='"'&&(r=!r),s)).join("");return this.url.searchParams.set("select",n),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:r=!0,nullsFirst:n,foreignTable:s,referencedTable:i=s}={}){const o=i?`${i}.order`:"order",a=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${a?`${a},`:""}${t}.${r?"asc":"desc"}${n===void 0?"":n?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:r,referencedTable:n=r}={}){const s=typeof n>"u"?"limit":`${n}.limit`;return this.url.searchParams.set(s,`${t}`),this}range(t,r,{foreignTable:n,referencedTable:s=n}={}){const i=typeof s>"u"?"offset":`${s}.offset`,o=typeof s>"u"?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${t}`),this.url.searchParams.set(o,`${r-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:r=!1,settings:n=!1,buffers:s=!1,wal:i=!1,format:o="text"}={}){var a;const u=[t?"analyze":null,r?"verbose":null,n?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),c=(a=this.headers.Accept)!==null&&a!==void 0?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${c}"; options=${u};`,o==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Bi.default=Xg;var Zg=We&&We.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(fs,"__esModule",{value:!0});const e0=Zg(Bi);let t0=class extends e0.default{eq(t,r){return this.url.searchParams.append(t,`eq.${r}`),this}neq(t,r){return this.url.searchParams.append(t,`neq.${r}`),this}gt(t,r){return this.url.searchParams.append(t,`gt.${r}`),this}gte(t,r){return this.url.searchParams.append(t,`gte.${r}`),this}lt(t,r){return this.url.searchParams.append(t,`lt.${r}`),this}lte(t,r){return this.url.searchParams.append(t,`lte.${r}`),this}like(t,r){return this.url.searchParams.append(t,`like.${r}`),this}likeAllOf(t,r){return this.url.searchParams.append(t,`like(all).{${r.join(",")}}`),this}likeAnyOf(t,r){return this.url.searchParams.append(t,`like(any).{${r.join(",")}}`),this}ilike(t,r){return this.url.searchParams.append(t,`ilike.${r}`),this}ilikeAllOf(t,r){return this.url.searchParams.append(t,`ilike(all).{${r.join(",")}}`),this}ilikeAnyOf(t,r){return this.url.searchParams.append(t,`ilike(any).{${r.join(",")}}`),this}is(t,r){return this.url.searchParams.append(t,`is.${r}`),this}in(t,r){const n=Array.from(new Set(r)).map(s=>typeof s=="string"&&new RegExp("[,()]").test(s)?`"${s}"`:`${s}`).join(",");return this.url.searchParams.append(t,`in.(${n})`),this}contains(t,r){return typeof r=="string"?this.url.searchParams.append(t,`cs.${r}`):Array.isArray(r)?this.url.searchParams.append(t,`cs.{${r.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(r)}`),this}containedBy(t,r){return typeof r=="string"?this.url.searchParams.append(t,`cd.${r}`):Array.isArray(r)?this.url.searchParams.append(t,`cd.{${r.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(r)}`),this}rangeGt(t,r){return this.url.searchParams.append(t,`sr.${r}`),this}rangeGte(t,r){return this.url.searchParams.append(t,`nxl.${r}`),this}rangeLt(t,r){return this.url.searchParams.append(t,`sl.${r}`),this}rangeLte(t,r){return this.url.searchParams.append(t,`nxr.${r}`),this}rangeAdjacent(t,r){return this.url.searchParams.append(t,`adj.${r}`),this}overlaps(t,r){return typeof r=="string"?this.url.searchParams.append(t,`ov.${r}`):this.url.searchParams.append(t,`ov.{${r.join(",")}}`),this}textSearch(t,r,{config:n,type:s}={}){let i="";s==="plain"?i="pl":s==="phrase"?i="ph":s==="websearch"&&(i="w");const o=n===void 0?"":`(${n})`;return this.url.searchParams.append(t,`${i}fts${o}.${r}`),this}match(t){return Object.entries(t).forEach(([r,n])=>{this.url.searchParams.append(r,`eq.${n}`)}),this}not(t,r,n){return this.url.searchParams.append(t,`not.${r}.${n}`),this}or(t,{foreignTable:r,referencedTable:n=r}={}){const s=n?`${n}.or`:"or";return this.url.searchParams.append(s,`(${t})`),this}filter(t,r,n){return this.url.searchParams.append(t,`${r}.${n}`),this}};fs.default=t0;var r0=We&&We.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Hi,"__esModule",{value:!0});const _n=r0(fs);let n0=class{constructor(t,{headers:r={},schema:n,fetch:s}){this.url=t,this.headers=r,this.schema=n,this.fetch=s}select(t,{head:r=!1,count:n}={}){const s=r?"HEAD":"GET";let i=!1;const o=(t??"*").split("").map(a=>/\s/.test(a)&&!i?"":(a==='"'&&(i=!i),a)).join("");return this.url.searchParams.set("select",o),n&&(this.headers.Prefer=`count=${n}`),new _n.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:r,defaultToNull:n=!0}={}){const s="POST",i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),r&&i.push(`count=${r}`),n||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(t)){const o=t.reduce((a,u)=>a.concat(Object.keys(u)),[]);if(o.length>0){const a=[...new Set(o)].map(u=>`"${u}"`);this.url.searchParams.set("columns",a.join(","))}}return new _n.default({method:s,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:r,ignoreDuplicates:n=!1,count:s,defaultToNull:i=!0}={}){const o="POST",a=[`resolution=${n?"ignore":"merge"}-duplicates`];if(r!==void 0&&this.url.searchParams.set("on_conflict",r),this.headers.Prefer&&a.push(this.headers.Prefer),s&&a.push(`count=${s}`),i||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(t)){const u=t.reduce((c,d)=>c.concat(Object.keys(d)),[]);if(u.length>0){const c=[...new Set(u)].map(d=>`"${d}"`);this.url.searchParams.set("columns",c.join(","))}}return new _n.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:r}={}){const n="PATCH",s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),r&&s.push(`count=${r}`),this.headers.Prefer=s.join(","),new _n.default({method:n,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const r="DELETE",n=[];return t&&n.push(`count=${t}`),this.headers.Prefer&&n.unshift(this.headers.Prefer),this.headers.Prefer=n.join(","),new _n.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};Hi.default=n0;var Vi={},qi={};Object.defineProperty(qi,"__esModule",{value:!0});qi.version=void 0;qi.version="0.0.0-automated";Object.defineProperty(Vi,"__esModule",{value:!0});Vi.DEFAULT_HEADERS=void 0;const s0=qi;Vi.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${s0.version}`};var of=We&&We.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Pl,"__esModule",{value:!0});const i0=of(Hi),o0=of(fs),a0=Vi;let l0=class af{constructor(t,{headers:r={},schema:n,fetch:s}={}){this.url=t,this.headers=Object.assign(Object.assign({},a0.DEFAULT_HEADERS),r),this.schemaName=n,this.fetch=s}from(t){const r=new URL(`${this.url}/${t}`);return new i0.default(r,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new af(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,r={},{head:n=!1,get:s=!1,count:i}={}){let o;const a=new URL(`${this.url}/rpc/${t}`);let u;n||s?(o=n?"HEAD":"GET",Object.entries(r).filter(([d,p])=>p!==void 0).map(([d,p])=>[d,Array.isArray(p)?`{${p.join(",")}}`:`${p}`]).forEach(([d,p])=>{a.searchParams.append(d,p)})):(o="POST",u=r);const c=Object.assign({},this.headers);return i&&(c.Prefer=`count=${i}`),new o0.default({method:o,url:a,headers:c,schema:this.schemaName,body:u,fetch:this.fetch,allowEmpty:!1})}};Pl.default=l0;var hn=We&&We.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(je,"__esModule",{value:!0});je.PostgrestError=je.PostgrestBuilder=je.PostgrestTransformBuilder=je.PostgrestFilterBuilder=je.PostgrestQueryBuilder=je.PostgrestClient=void 0;const lf=hn(Pl);je.PostgrestClient=lf.default;const uf=hn(Hi);je.PostgrestQueryBuilder=uf.default;const cf=hn(fs);je.PostgrestFilterBuilder=cf.default;const df=hn(Bi);je.PostgrestTransformBuilder=df.default;const hf=hn(Wi);je.PostgrestBuilder=hf.default;const ff=hn(Gi);je.PostgrestError=ff.default;var u0=je.default={PostgrestClient:lf.default,PostgrestQueryBuilder:uf.default,PostgrestFilterBuilder:cf.default,PostgrestTransformBuilder:df.default,PostgrestBuilder:hf.default,PostgrestError:ff.default};const{PostgrestClient:c0,PostgrestQueryBuilder:mx,PostgrestFilterBuilder:gx,PostgrestTransformBuilder:yx,PostgrestBuilder:vx,PostgrestError:wx}=u0,d0="2.11.2",h0={"X-Client-Info":`realtime-js/${d0}`},f0="1.0.0",pf=1e4,p0=1e3;var Jr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Jr||(Jr={}));var Pe;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(Pe||(Pe={}));var Je;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(Je||(Je={}));var Ca;(function(e){e.websocket="websocket"})(Ca||(Ca={}));var or;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(or||(or={}));class m0{constructor(){this.HEADER_LENGTH=1}decode(t,r){return t.constructor===ArrayBuffer?r(this._binaryDecode(t)):r(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const r=new DataView(t),n=new TextDecoder;return this._decodeBroadcast(t,r,n)}_decodeBroadcast(t,r,n){const s=r.getUint8(1),i=r.getUint8(2);let o=this.HEADER_LENGTH+2;const a=n.decode(t.slice(o,o+s));o=o+s;const u=n.decode(t.slice(o,o+i));o=o+i;const c=JSON.parse(n.decode(t.slice(o,t.byteLength)));return{ref:null,topic:a,event:u,payload:c}}}class mf{constructor(t,r){this.callback=t,this.timerCalc=r,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=r}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var H;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(H||(H={}));const Qu=(e,t,r={})=>{var n;const s=(n=r.skipTypes)!==null&&n!==void 0?n:[];return Object.keys(t).reduce((i,o)=>(i[o]=g0(o,e,t,s),i),{})},g0=(e,t,r,n)=>{const s=t.find(a=>a.name===e),i=s==null?void 0:s.type,o=r[e];return i&&!n.includes(i)?gf(i,o):Ta(o)},gf=(e,t)=>{if(e.charAt(0)==="_"){const r=e.slice(1,e.length);return x0(t,r)}switch(e){case H.bool:return y0(t);case H.float4:case H.float8:case H.int2:case H.int4:case H.int8:case H.numeric:case H.oid:return v0(t);case H.json:case H.jsonb:return w0(t);case H.timestamp:return _0(t);case H.abstime:case H.date:case H.daterange:case H.int4range:case H.int8range:case H.money:case H.reltime:case H.text:case H.time:case H.timestamptz:case H.timetz:case H.tsrange:case H.tstzrange:return Ta(t);default:return Ta(t)}},Ta=e=>e,y0=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},v0=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},w0=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},x0=(e,t)=>{if(typeof e!="string")return e;const r=e.length-1,n=e[r];if(e[0]==="{"&&n==="}"){let i;const o=e.slice(1,r);try{i=JSON.parse("["+o+"]")}catch{i=o?o.split(","):[]}return i.map(a=>gf(t,a))}return e},_0=e=>typeof e=="string"?e.replace(" ","T"):e,yf=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class jo{constructor(t,r,n={},s=pf){this.channel=t,this.event=r,this.payload=n,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,r){var n;return this._hasReceived(t)&&r((n=this.receivedResp)===null||n===void 0?void 0:n.response),this.recHooks.push({status:t,callback:r}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=r=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=r,this._matchReceive(r)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,r){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:r})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:r}){this.recHooks.filter(n=>n.status===t).forEach(n=>n.callback(r))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var Xu;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(Xu||(Xu={}));class $n{constructor(t,r){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const n=(r==null?void 0:r.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(n.state,{},s=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=$n.syncState(this.state,s,i,o),this.pendingDiffs.forEach(u=>{this.state=$n.syncDiff(this.state,u,i,o)}),this.pendingDiffs=[],a()}),this.channel._on(n.diff,{},s=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(s):(this.state=$n.syncDiff(this.state,s,i,o),a())}),this.onJoin((s,i,o)=>{this.channel._trigger("presence",{event:"join",key:s,currentPresences:i,newPresences:o})}),this.onLeave((s,i,o)=>{this.channel._trigger("presence",{event:"leave",key:s,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,r,n,s){const i=this.cloneDeep(t),o=this.transformState(r),a={},u={};return this.map(i,(c,d)=>{o[c]||(u[c]=d)}),this.map(o,(c,d)=>{const p=i[c];if(p){const m=d.map(k=>k.presence_ref),y=p.map(k=>k.presence_ref),w=d.filter(k=>y.indexOf(k.presence_ref)<0),x=p.filter(k=>m.indexOf(k.presence_ref)<0);w.length>0&&(a[c]=w),x.length>0&&(u[c]=x)}else a[c]=d}),this.syncDiff(i,{joins:a,leaves:u},n,s)}static syncDiff(t,r,n,s){const{joins:i,leaves:o}={joins:this.transformState(r.joins),leaves:this.transformState(r.leaves)};return n||(n=()=>{}),s||(s=()=>{}),this.map(i,(a,u)=>{var c;const d=(c=t[a])!==null&&c!==void 0?c:[];if(t[a]=this.cloneDeep(u),d.length>0){const p=t[a].map(y=>y.presence_ref),m=d.filter(y=>p.indexOf(y.presence_ref)<0);t[a].unshift(...m)}n(a,d,u)}),this.map(o,(a,u)=>{let c=t[a];if(!c)return;const d=u.map(p=>p.presence_ref);c=c.filter(p=>d.indexOf(p.presence_ref)<0),t[a]=c,s(a,c,u),c.length===0&&delete t[a]}),t}static map(t,r){return Object.getOwnPropertyNames(t).map(n=>r(n,t[n]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((r,n)=>{const s=t[n];return"metas"in s?r[n]=s.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):r[n]=s,r},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Zu;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(Zu||(Zu={}));var ec;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(ec||(ec={}));var dt;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(dt||(dt={}));class Rl{constructor(t,r={config:{}},n){this.topic=t,this.params=r,this.socket=n,this.bindings={},this.state=Pe.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},r.config),this.timeout=this.socket.timeout,this.joinPush=new jo(this,Je.join,this.params,this.timeout),this.rejoinTimer=new mf(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=Pe.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Pe.closed,this.socket._remove(this)}),this._onError(s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=Pe.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Pe.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Je.reply,{},(s,i)=>{this._trigger(this._replyEventName(i),s)}),this.presence=new $n(this),this.broadcastEndpointURL=yf(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,r=this.timeout){var n,s;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:i,presence:o,private:a}}=this.params;this._onError(d=>t==null?void 0:t(dt.CHANNEL_ERROR,d)),this._onClose(()=>t==null?void 0:t(dt.CLOSED));const u={},c={broadcast:i,presence:o,postgres_changes:(s=(n=this.bindings.postgres_changes)===null||n===void 0?void 0:n.map(d=>d.filter))!==null&&s!==void 0?s:[],private:a};this.socket.accessTokenValue&&(u.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},u)),this.joinedOnce=!0,this._rejoin(r),this.joinPush.receive("ok",async({postgres_changes:d})=>{var p;if(this.socket.setAuth(),d===void 0){t==null||t(dt.SUBSCRIBED);return}else{const m=this.bindings.postgres_changes,y=(p=m==null?void 0:m.length)!==null&&p!==void 0?p:0,w=[];for(let x=0;x<y;x++){const k=m[x],{filter:{event:f,schema:h,table:g,filter:_}}=k,S=d&&d[x];if(S&&S.event===f&&S.schema===h&&S.table===g&&S.filter===_)w.push(Object.assign(Object.assign({},k),{id:S.id}));else{this.unsubscribe(),t==null||t(dt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=w,t&&t(dt.SUBSCRIBED);return}}).receive("error",d=>{t==null||t(dt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(d).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(dt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,r={}){return await this.send({type:"presence",event:"track",payload:t},r.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,r,n){return this._on(t,r,n)}async send(t,r={}){var n,s;if(!this._canPush()&&t.type==="broadcast"){const{event:i,payload:o}=t,u={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const c=await this._fetchWithTimeout(this.broadcastEndpointURL,u,(n=r.timeout)!==null&&n!==void 0?n:this.timeout);return await((s=c.body)===null||s===void 0?void 0:s.cancel()),c.ok?"ok":"error"}catch(c){return c.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var o,a,u;const c=this._push(t.type,t,r.timeout||this.timeout);t.type==="broadcast"&&!(!((u=(a=(o=this.params)===null||o===void 0?void 0:o.config)===null||a===void 0?void 0:a.broadcast)===null||u===void 0)&&u.ack)&&i("ok"),c.receive("ok",()=>i("ok")),c.receive("error",()=>i("error")),c.receive("timeout",()=>i("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=Pe.leaving;const r=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Je.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(n=>{const s=new jo(this,Je.leave,{},t);s.receive("ok",()=>{r(),n("ok")}).receive("timeout",()=>{r(),n("timed out")}).receive("error",()=>{n("error")}),s.send(),this._canPush()||s.trigger("ok",{})})}async _fetchWithTimeout(t,r,n){const s=new AbortController,i=setTimeout(()=>s.abort(),n),o=await this.socket.fetch(t,Object.assign(Object.assign({},r),{signal:s.signal}));return clearTimeout(i),o}_push(t,r,n=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new jo(this,t,r,n);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(t,r,n){return r}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,r,n){var s,i;const o=t.toLocaleLowerCase(),{close:a,error:u,leave:c,join:d}=Je;if(n&&[a,u,c,d].indexOf(o)>=0&&n!==this._joinRef())return;let m=this._onMessage(o,r,n);if(r&&!m)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(s=this.bindings.postgres_changes)===null||s===void 0||s.filter(y=>{var w,x,k;return((w=y.filter)===null||w===void 0?void 0:w.event)==="*"||((k=(x=y.filter)===null||x===void 0?void 0:x.event)===null||k===void 0?void 0:k.toLocaleLowerCase())===o}).map(y=>y.callback(m,n)):(i=this.bindings[o])===null||i===void 0||i.filter(y=>{var w,x,k,f,h,g;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in y){const _=y.id,S=(w=y.filter)===null||w===void 0?void 0:w.event;return _&&((x=r.ids)===null||x===void 0?void 0:x.includes(_))&&(S==="*"||(S==null?void 0:S.toLocaleLowerCase())===((k=r.data)===null||k===void 0?void 0:k.type.toLocaleLowerCase()))}else{const _=(h=(f=y==null?void 0:y.filter)===null||f===void 0?void 0:f.event)===null||h===void 0?void 0:h.toLocaleLowerCase();return _==="*"||_===((g=r==null?void 0:r.event)===null||g===void 0?void 0:g.toLocaleLowerCase())}else return y.type.toLocaleLowerCase()===o}).map(y=>{if(typeof m=="object"&&"ids"in m){const w=m.data,{schema:x,table:k,commit_timestamp:f,type:h,errors:g}=w;m=Object.assign(Object.assign({},{schema:x,table:k,commit_timestamp:f,eventType:h,new:{},old:{},errors:g}),this._getPayloadRecords(w))}y.callback(m,n)})}_isClosed(){return this.state===Pe.closed}_isJoined(){return this.state===Pe.joined}_isJoining(){return this.state===Pe.joining}_isLeaving(){return this.state===Pe.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,r,n){const s=t.toLocaleLowerCase(),i={type:s,filter:r,callback:n};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(t,r){const n=t.toLocaleLowerCase();return this.bindings[n]=this.bindings[n].filter(s=>{var i;return!(((i=s.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===n&&Rl.isEqual(s.filter,r))}),this}static isEqual(t,r){if(Object.keys(t).length!==Object.keys(r).length)return!1;for(const n in t)if(t[n]!==r[n])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(Je.close,{},t)}_onError(t){this._on(Je.error,{},r=>t(r))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Pe.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const r={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(r.new=Qu(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(r.old=Qu(t.columns,t.old_record)),r}}const k0=()=>{},S0=typeof WebSocket<"u",E0=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class b0{constructor(t,r){var n;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=h0,this.params={},this.timeout=pf,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=k0,this.conn=null,this.sendBuffer=[],this.serializer=new m0,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let o;return i?o=i:typeof fetch>"u"?o=(...a)=>on(()=>Promise.resolve().then(()=>dn),void 0).then(({default:u})=>u(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${t}/${Ca.websocket}`,this.httpEndpoint=yf(t),r!=null&&r.transport?this.transport=r.transport:this.transport=null,r!=null&&r.params&&(this.params=r.params),r!=null&&r.headers&&(this.headers=Object.assign(Object.assign({},this.headers),r.headers)),r!=null&&r.timeout&&(this.timeout=r.timeout),r!=null&&r.logger&&(this.logger=r.logger),r!=null&&r.heartbeatIntervalMs&&(this.heartbeatIntervalMs=r.heartbeatIntervalMs);const s=(n=r==null?void 0:r.params)===null||n===void 0?void 0:n.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=r!=null&&r.reconnectAfterMs?r.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=r!=null&&r.encode?r.encode:(i,o)=>o(JSON.stringify(i)),this.decode=r!=null&&r.decode?r.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new mf(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(r==null?void 0:r.fetch),r!=null&&r.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(r==null?void 0:r.worker)||!1,this.workerUrl=r==null?void 0:r.workerUrl}this.accessToken=(r==null?void 0:r.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(S0){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new j0(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),on(()=>import("./browser-393879c9.js").then(t=>t.b),[]).then(({default:t})=>{this.conn=new t(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:f0}))}disconnect(t,r){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,r??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(t){const r=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),r}async removeAllChannels(){const t=await Promise.all(this.channels.map(r=>r.unsubscribe()));return this.disconnect(),t}log(t,r,n){this.logger(t,r,n)}connectionState(){switch(this.conn&&this.conn.readyState){case Jr.connecting:return or.Connecting;case Jr.open:return or.Open;case Jr.closing:return or.Closing;default:return or.Closed}}isConnected(){return this.connectionState()===or.Open}channel(t,r={config:{}}){const n=new Rl(`realtime:${t}`,r,this);return this.channels.push(n),n}push(t){const{topic:r,event:n,payload:s,ref:i}=t,o=()=>{this.encode(t,a=>{var u;(u=this.conn)===null||u===void 0||u.send(a)})};this.log("push",`${r} ${n} (${i})`,s),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let r=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(r){let n=null;try{n=JSON.parse(atob(r.split(".")[1]))}catch{}if(n&&n.exp&&!(Math.floor(Date.now()/1e3)-n.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${n.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${n.exp}`);this.accessTokenValue=r,this.channels.forEach(s=>{r&&s.updateJoinPayload({access_token:r}),s.joinedOnce&&s._isJoined()&&s._push(Je.access_token,{access_token:r})})}}async sendHeartbeat(){var t;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(t=this.conn)===null||t===void 0||t.close(p0,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let r=this.channels.find(n=>n.topic===t&&(n._isJoined()||n._isJoining()));r&&(this.log("transport",`leaving duplicate topic "${t}"`),r.unsubscribe())}_remove(t){this.channels=this.channels.filter(r=>r._joinRef()!==t._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,r=>{let{topic:n,event:s,payload:i,ref:o}=r;o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${n} ${s} ${o&&"("+o+")"||""}`,i),this.channels.filter(a=>a._isMember(n)).forEach(a=>a._trigger(s,i,o)),this.stateChangeCallbacks.message.forEach(a=>a(r))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=r=>{this.log("worker","worker error",r.message),this.workerRef.terminate()},this.workerRef.onmessage=r=>{r.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(t=>t())}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(r=>r(t))}_onConnError(t){this.log("transport",t.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(r=>r(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(Je.error))}_appendParams(t,r){if(Object.keys(r).length===0)return t;const n=t.match(/\?/)?"&":"?",s=new URLSearchParams(r);return`${t}${n}${s}`}_workerObjectUrl(t){let r;if(t)r=t;else{const n=new Blob([E0],{type:"application/javascript"});r=URL.createObjectURL(n)}return r}}class j0{constructor(t,r,n){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=Jr.connecting,this.send=()=>{},this.url=null,this.url=t,this.close=n.close}}class Il extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function oe(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class N0 extends Il{constructor(t,r){super(t),this.name="StorageApiError",this.status=r}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Oa extends Il{constructor(t,r){super(t),this.name="StorageUnknownError",this.originalError=r}}var C0=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};const vf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...r)=>on(()=>Promise.resolve().then(()=>dn),void 0).then(({default:n})=>n(...r)):t=fetch,(...r)=>t(...r)},T0=()=>C0(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield on(()=>Promise.resolve().then(()=>dn),void 0)).Response:Response}),Pa=e=>{if(Array.isArray(e))return e.map(r=>Pa(r));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([r,n])=>{const s=r.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));t[s]=Pa(n)}),t};var kr=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};const No=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),O0=(e,t,r)=>kr(void 0,void 0,void 0,function*(){const n=yield T0();e instanceof n&&!(r!=null&&r.noResolveJson)?e.json().then(s=>{t(new N0(No(s),e.status||500))}).catch(s=>{t(new Oa(No(s),s))}):t(new Oa(No(e),e))}),P0=(e,t,r,n)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),n&&(s.body=JSON.stringify(n)),Object.assign(Object.assign({},s),r))};function ps(e,t,r,n,s,i){return kr(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,P0(t,n,s,i)).then(u=>{if(!u.ok)throw u;return n!=null&&n.noResolveJson?u:u.json()}).then(u=>o(u)).catch(u=>O0(u,a,n))})})}function ki(e,t,r,n){return kr(this,void 0,void 0,function*(){return ps(e,"GET",t,r,n)})}function Ct(e,t,r,n,s){return kr(this,void 0,void 0,function*(){return ps(e,"POST",t,n,s,r)})}function R0(e,t,r,n,s){return kr(this,void 0,void 0,function*(){return ps(e,"PUT",t,n,s,r)})}function I0(e,t,r,n){return kr(this,void 0,void 0,function*(){return ps(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),n)})}function wf(e,t,r,n,s){return kr(this,void 0,void 0,function*(){return ps(e,"DELETE",t,n,s,r)})}var Ee=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};const A0={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},tc={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class D0{constructor(t,r={},n,s){this.url=t,this.headers=r,this.bucketId=n,this.fetch=vf(s)}uploadOrUpdate(t,r,n,s){return Ee(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},tc),s);let a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const u=o.metadata;typeof Blob<"u"&&n instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),u&&i.append("metadata",this.encodeMetadata(u)),i.append("",n)):typeof FormData<"u"&&n instanceof FormData?(i=n,i.append("cacheControl",o.cacheControl),u&&i.append("metadata",this.encodeMetadata(u))):(i=n,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,u&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(u)))),s!=null&&s.headers&&(a=Object.assign(Object.assign({},a),s.headers));const c=this._removeEmptyFolders(r),d=this._getFinalPath(c),p=yield this.fetch(`${this.url}/object/${d}`,Object.assign({method:t,body:i,headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{})),m=yield p.json();return p.ok?{data:{path:c,id:m.Id,fullPath:m.Key},error:null}:{data:null,error:m}}catch(i){if(oe(i))return{data:null,error:i};throw i}})}upload(t,r,n){return Ee(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,r,n)})}uploadToSignedUrl(t,r,n,s){return Ee(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(t),o=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",r);try{let u;const c=Object.assign({upsert:tc.upsert},s),d=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)});typeof Blob<"u"&&n instanceof Blob?(u=new FormData,u.append("cacheControl",c.cacheControl),u.append("",n)):typeof FormData<"u"&&n instanceof FormData?(u=n,u.append("cacheControl",c.cacheControl)):(u=n,d["cache-control"]=`max-age=${c.cacheControl}`,d["content-type"]=c.contentType);const p=yield this.fetch(a.toString(),{method:"PUT",body:u,headers:d}),m=yield p.json();return p.ok?{data:{path:i,fullPath:m.Key},error:null}:{data:null,error:m}}catch(u){if(oe(u))return{data:null,error:u};throw u}})}createSignedUploadUrl(t,r){return Ee(this,void 0,void 0,function*(){try{let n=this._getFinalPath(t);const s=Object.assign({},this.headers);r!=null&&r.upsert&&(s["x-upsert"]="true");const i=yield Ct(this.fetch,`${this.url}/object/upload/sign/${n}`,{},{headers:s}),o=new URL(this.url+i.url),a=o.searchParams.get("token");if(!a)throw new Il("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:a},error:null}}catch(n){if(oe(n))return{data:null,error:n};throw n}})}update(t,r,n){return Ee(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,r,n)})}move(t,r,n){return Ee(this,void 0,void 0,function*(){try{return{data:yield Ct(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:r,destinationBucket:n==null?void 0:n.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(oe(s))return{data:null,error:s};throw s}})}copy(t,r,n){return Ee(this,void 0,void 0,function*(){try{return{data:{path:(yield Ct(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:r,destinationBucket:n==null?void 0:n.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(oe(s))return{data:null,error:s};throw s}})}createSignedUrl(t,r,n){return Ee(this,void 0,void 0,function*(){try{let s=this._getFinalPath(t),i=yield Ct(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:r},n!=null&&n.transform?{transform:n.transform}:{}),{headers:this.headers});const o=n!=null&&n.download?`&download=${n.download===!0?"":n.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(s){if(oe(s))return{data:null,error:s};throw s}})}createSignedUrls(t,r,n){return Ee(this,void 0,void 0,function*(){try{const s=yield Ct(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:r,paths:t},{headers:this.headers}),i=n!=null&&n.download?`&download=${n.download===!0?"":n.download}`:"";return{data:s.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(s){if(oe(s))return{data:null,error:s};throw s}})}download(t,r){return Ee(this,void 0,void 0,function*(){const s=typeof(r==null?void 0:r.transform)<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((r==null?void 0:r.transform)||{}),o=i?`?${i}`:"";try{const a=this._getFinalPath(t);return{data:yield(yield ki(this.fetch,`${this.url}/${s}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(oe(a))return{data:null,error:a};throw a}})}info(t){return Ee(this,void 0,void 0,function*(){const r=this._getFinalPath(t);try{const n=yield ki(this.fetch,`${this.url}/object/info/${r}`,{headers:this.headers});return{data:Pa(n),error:null}}catch(n){if(oe(n))return{data:null,error:n};throw n}})}exists(t){return Ee(this,void 0,void 0,function*(){const r=this._getFinalPath(t);try{return yield I0(this.fetch,`${this.url}/object/${r}`,{headers:this.headers}),{data:!0,error:null}}catch(n){if(oe(n)&&n instanceof Oa){const s=n.originalError;if([400,404].includes(s==null?void 0:s.status))return{data:!1,error:n}}throw n}})}getPublicUrl(t,r){const n=this._getFinalPath(t),s=[],i=r!=null&&r.download?`download=${r.download===!0?"":r.download}`:"";i!==""&&s.push(i);const a=typeof(r==null?void 0:r.transform)<"u"?"render/image":"object",u=this.transformOptsToQueryString((r==null?void 0:r.transform)||{});u!==""&&s.push(u);let c=s.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${n}${c}`)}}}remove(t){return Ee(this,void 0,void 0,function*(){try{return{data:yield wf(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(r){if(oe(r))return{data:null,error:r};throw r}})}list(t,r,n){return Ee(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},A0),r),{prefix:t||""});return{data:yield Ct(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},n),error:null}}catch(s){if(oe(s))return{data:null,error:s};throw s}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const r=[];return t.width&&r.push(`width=${t.width}`),t.height&&r.push(`height=${t.height}`),t.resize&&r.push(`resize=${t.resize}`),t.format&&r.push(`format=${t.format}`),t.quality&&r.push(`quality=${t.quality}`),r.join("&")}}const L0="2.7.1",M0={"X-Client-Info":`storage-js/${L0}`};var br=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};class $0{constructor(t,r={},n){this.url=t,this.headers=Object.assign(Object.assign({},M0),r),this.fetch=vf(n)}listBuckets(){return br(this,void 0,void 0,function*(){try{return{data:yield ki(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(oe(t))return{data:null,error:t};throw t}})}getBucket(t){return br(this,void 0,void 0,function*(){try{return{data:yield ki(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(r){if(oe(r))return{data:null,error:r};throw r}})}createBucket(t,r={public:!1}){return br(this,void 0,void 0,function*(){try{return{data:yield Ct(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:r.public,file_size_limit:r.fileSizeLimit,allowed_mime_types:r.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(oe(n))return{data:null,error:n};throw n}})}updateBucket(t,r){return br(this,void 0,void 0,function*(){try{return{data:yield R0(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:r.public,file_size_limit:r.fileSizeLimit,allowed_mime_types:r.allowedMimeTypes},{headers:this.headers}),error:null}}catch(n){if(oe(n))return{data:null,error:n};throw n}})}emptyBucket(t){return br(this,void 0,void 0,function*(){try{return{data:yield Ct(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(r){if(oe(r))return{data:null,error:r};throw r}})}deleteBucket(t){return br(this,void 0,void 0,function*(){try{return{data:yield wf(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(r){if(oe(r))return{data:null,error:r};throw r}})}}class U0 extends $0{constructor(t,r={},n){super(t,r,n)}from(t){return new D0(this.url,this.headers,t,this.fetch)}}const F0="2.49.8";let Cn="";typeof Deno<"u"?Cn="deno":typeof document<"u"?Cn="web":typeof navigator<"u"&&navigator.product==="ReactNative"?Cn="react-native":Cn="node";const z0={"X-Client-Info":`supabase-js-${Cn}/${F0}`},H0={headers:z0},B0={schema:"public"},W0={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},G0={};var V0=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};const q0=e=>{let t;return e?t=e:typeof fetch>"u"?t=rf:t=fetch,(...r)=>t(...r)},Y0=()=>typeof Headers>"u"?nf:Headers,K0=(e,t,r)=>{const n=q0(r),s=Y0();return(i,o)=>V0(void 0,void 0,void 0,function*(){var a;const u=(a=yield t())!==null&&a!==void 0?a:e;let c=new s(o==null?void 0:o.headers);return c.has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization",`Bearer ${u}`),n(i,Object.assign(Object.assign({},o),{headers:c}))})};var J0=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};function Q0(e){return e.endsWith("/")?e:e+"/"}function X0(e,t){var r,n;const{db:s,auth:i,realtime:o,global:a}=e,{db:u,auth:c,realtime:d,global:p}=t,m={db:Object.assign(Object.assign({},u),s),auth:Object.assign(Object.assign({},c),i),realtime:Object.assign(Object.assign({},d),o),global:Object.assign(Object.assign(Object.assign({},p),a),{headers:Object.assign(Object.assign({},(r=p==null?void 0:p.headers)!==null&&r!==void 0?r:{}),(n=a==null?void 0:a.headers)!==null&&n!==void 0?n:{})}),accessToken:()=>J0(this,void 0,void 0,function*(){return""})};return e.accessToken?m.accessToken=e.accessToken:delete m.accessToken,m}const xf="2.69.1",Or=30*1e3,Ra=3,Co=Ra*Or,Z0="http://localhost:9999",ey="supabase.auth.token",ty={"X-Client-Info":`gotrue-js/${xf}`},Ia="X-Supabase-Api-Version",_f={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},ry=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,ny=6e5;class Al extends Error{constructor(t,r,n){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=r,this.code=n}}function D(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class sy extends Al{constructor(t,r,n){super(t,r,n),this.name="AuthApiError",this.status=r,this.code=n}}function iy(e){return D(e)&&e.name==="AuthApiError"}class kf extends Al{constructor(t,r){super(t),this.name="AuthUnknownError",this.originalError=r}}class Qt extends Al{constructor(t,r,n,s){super(t,n,s),this.name=r,this.status=n}}class Et extends Qt{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function oy(e){return D(e)&&e.name==="AuthSessionMissingError"}class To extends Qt{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class As extends Qt{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class Ds extends Qt{constructor(t,r=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=r}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function ay(e){return D(e)&&e.name==="AuthImplicitGrantRedirectError"}class rc extends Qt{constructor(t,r=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=r}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Aa extends Qt{constructor(t,r){super(t,"AuthRetryableFetchError",r,void 0)}}function Oo(e){return D(e)&&e.name==="AuthRetryableFetchError"}class nc extends Qt{constructor(t,r,n){super(t,"AuthWeakPasswordError",r,"weak_password"),this.reasons=n}}class Un extends Qt{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const sc="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),ic=` 	
\r=`.split(""),ly=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<ic.length;t+=1)e[ic[t].charCodeAt(0)]=-2;for(let t=0;t<sc.length;t+=1)e[sc[t].charCodeAt(0)]=t;return e})();function Sf(e,t,r){const n=ly[e];if(n>-1)for(t.queue=t.queue<<6|n,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(n===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function oc(e){const t=[],r=o=>{t.push(String.fromCodePoint(o))},n={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},i=o=>{dy(o,n,r)};for(let o=0;o<e.length;o+=1)Sf(e.charCodeAt(o),s,i);return t.join("")}function uy(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function cy(e,t){for(let r=0;r<e.length;r+=1){let n=e.charCodeAt(r);if(n>55295&&n<=56319){const s=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|s)+65536,r+=1}uy(n,t)}}function dy(e,t,r){if(t.utf8seq===0){if(e<=127){r(e);return}for(let n=1;n<6;n+=1)if(!(e>>7-n&1)){t.utf8seq=n;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&r(t.codepoint)}}function hy(e){const t=[],r={queue:0,queuedBits:0},n=s=>{t.push(s)};for(let s=0;s<e.length;s+=1)Sf(e.charCodeAt(s),r,n);return new Uint8Array(t)}function fy(e){const t=[];return cy(e,r=>t.push(r)),new Uint8Array(t)}function py(e){return Math.round(Date.now()/1e3)+e}function my(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const nt=()=>typeof window<"u"&&typeof document<"u",tr={tested:!1,writable:!1},Fn=()=>{if(!nt())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(tr.tested)return tr.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),tr.tested=!0,tr.writable=!0}catch{tr.tested=!0,tr.writable=!1}return tr.writable};function gy(e){const t={},r=new URL(e);if(r.hash&&r.hash[0]==="#")try{new URLSearchParams(r.hash.substring(1)).forEach((s,i)=>{t[i]=s})}catch{}return r.searchParams.forEach((n,s)=>{t[s]=n}),t}const Ef=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...r)=>on(()=>Promise.resolve().then(()=>dn),void 0).then(({default:n})=>n(...r)):t=fetch,(...r)=>t(...r)},yy=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",bf=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},Ls=async(e,t)=>{const r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch{return r}},Ms=async(e,t)=>{await e.removeItem(t)};class Yi{constructor(){this.promise=new Yi.promiseConstructor((t,r)=>{this.resolve=t,this.reject=r})}}Yi.promiseConstructor=Promise;function Po(e){const t=e.split(".");if(t.length!==3)throw new Un("Invalid JWT structure");for(let n=0;n<t.length;n++)if(!ry.test(t[n]))throw new Un("JWT not in base64url format");return{header:JSON.parse(oc(t[0])),payload:JSON.parse(oc(t[1])),signature:hy(t[2]),raw:{header:t[0],payload:t[1]}}}async function vy(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function wy(e,t){return new Promise((n,s)=>{(async()=>{for(let i=0;i<1/0;i++)try{const o=await e(i);if(!t(i,null,o)){n(o);return}}catch(o){if(!t(i,o)){s(o);return}}})()})}function xy(e){return("0"+e.toString(16)).substr(-2)}function _y(){const t=new Uint32Array(56);if(typeof crypto>"u"){const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",n=r.length;let s="";for(let i=0;i<56;i++)s+=r.charAt(Math.floor(Math.random()*n));return s}return crypto.getRandomValues(t),Array.from(t,xy).join("")}async function ky(e){const r=new TextEncoder().encode(e),n=await crypto.subtle.digest("SHA-256",r),s=new Uint8Array(n);return Array.from(s).map(i=>String.fromCharCode(i)).join("")}async function Sy(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const r=await ky(e);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function jr(e,t,r=!1){const n=_y();let s=n;r&&(s+="/PASSWORD_RECOVERY"),await bf(e,`${t}-code-verifier`,s);const i=await Sy(n);return[i,n===i?"plain":"s256"]}const Ey=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function by(e){const t=e.headers.get(Ia);if(!t||!t.match(Ey))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function jy(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function Ny(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}var Cy=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};const sr=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Ty=[502,503,504];async function ac(e){var t;if(!yy(e))throw new Aa(sr(e),0);if(Ty.includes(e.status))throw new Aa(sr(e),e.status);let r;try{r=await e.json()}catch(i){throw new kf(sr(i),i)}let n;const s=by(e);if(s&&s.getTime()>=_f["2024-01-01"].timestamp&&typeof r=="object"&&r&&typeof r.code=="string"?n=r.code:typeof r=="object"&&r&&typeof r.error_code=="string"&&(n=r.error_code),n){if(n==="weak_password")throw new nc(sr(r),e.status,((t=r.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(n==="session_not_found")throw new Et}else if(typeof r=="object"&&r&&typeof r.weak_password=="object"&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new nc(sr(r),e.status,r.weak_password.reasons);throw new sy(sr(r),e.status||500,n)}const Oy=(e,t,r,n)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),s.body=JSON.stringify(n),Object.assign(Object.assign({},s),r))};async function $(e,t,r,n){var s;const i=Object.assign({},n==null?void 0:n.headers);i[Ia]||(i[Ia]=_f["2024-01-01"].name),n!=null&&n.jwt&&(i.Authorization=`Bearer ${n.jwt}`);const o=(s=n==null?void 0:n.query)!==null&&s!==void 0?s:{};n!=null&&n.redirectTo&&(o.redirect_to=n.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",u=await Py(e,t,r+a,{headers:i,noResolveJson:n==null?void 0:n.noResolveJson},{},n==null?void 0:n.body);return n!=null&&n.xform?n==null?void 0:n.xform(u):{data:Object.assign({},u),error:null}}async function Py(e,t,r,n,s,i){const o=Oy(t,n,s,i);let a;try{a=await e(r,Object.assign({},o))}catch(u){throw console.error(u),new Aa(sr(u),0)}if(a.ok||await ac(a),n!=null&&n.noResolveJson)return a;try{return await a.json()}catch(u){await ac(u)}}function bt(e){var t;let r=null;Dy(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=py(e.expires_in)));const n=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:r,user:n},error:null}}function lc(e){const t=bt(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((r,n)=>r&&typeof n=="string",!0)&&(t.data.weak_password=e.weak_password),t}function Rt(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Ry(e){return{data:e,error:null}}function Iy(e){const{action_link:t,email_otp:r,hashed_token:n,redirect_to:s,verification_type:i}=e,o=Cy(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:r,hashed_token:n,redirect_to:s,verification_type:i},u=Object.assign({},o);return{data:{properties:a,user:u},error:null}}function Ay(e){return e}function Dy(e){return e.access_token&&e.refresh_token&&e.expires_in}var Ly=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};class My{constructor({url:t="",headers:r={},fetch:n}){this.url=t,this.headers=r,this.fetch=Ef(n),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,r="global"){try{return await $(this.fetch,"POST",`${this.url}/logout?scope=${r}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(n){if(D(n))return{data:null,error:n};throw n}}async inviteUserByEmail(t,r={}){try{return await $(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:r.data},headers:this.headers,redirectTo:r.redirectTo,xform:Rt})}catch(n){if(D(n))return{data:{user:null},error:n};throw n}}async generateLink(t){try{const{options:r}=t,n=Ly(t,["options"]),s=Object.assign(Object.assign({},n),r);return"newEmail"in n&&(s.new_email=n==null?void 0:n.newEmail,delete s.newEmail),await $(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:Iy,redirectTo:r==null?void 0:r.redirectTo})}catch(r){if(D(r))return{data:{properties:null,user:null},error:r};throw r}}async createUser(t){try{return await $(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Rt})}catch(r){if(D(r))return{data:{user:null},error:r};throw r}}async listUsers(t){var r,n,s,i,o,a,u;try{const c={nextPage:null,lastPage:0,total:0},d=await $(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(n=(r=t==null?void 0:t.page)===null||r===void 0?void 0:r.toString())!==null&&n!==void 0?n:"",per_page:(i=(s=t==null?void 0:t.perPage)===null||s===void 0?void 0:s.toString())!==null&&i!==void 0?i:""},xform:Ay});if(d.error)throw d.error;const p=await d.json(),m=(o=d.headers.get("x-total-count"))!==null&&o!==void 0?o:0,y=(u=(a=d.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&u!==void 0?u:[];return y.length>0&&(y.forEach(w=>{const x=parseInt(w.split(";")[0].split("=")[1].substring(0,1)),k=JSON.parse(w.split(";")[1].split("=")[1]);c[`${k}Page`]=x}),c.total=parseInt(m)),{data:Object.assign(Object.assign({},p),c),error:null}}catch(c){if(D(c))return{data:{users:[]},error:c};throw c}}async getUserById(t){try{return await $(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Rt})}catch(r){if(D(r))return{data:{user:null},error:r};throw r}}async updateUserById(t,r){try{return await $(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:r,headers:this.headers,xform:Rt})}catch(n){if(D(n))return{data:{user:null},error:n};throw n}}async deleteUser(t,r=!1){try{return await $(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:r},xform:Rt})}catch(n){if(D(n))return{data:{user:null},error:n};throw n}}async _listFactors(t){try{const{data:r,error:n}=await $(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:s=>({data:{factors:s},error:null})});return{data:r,error:n}}catch(r){if(D(r))return{data:null,error:r};throw r}}async _deleteFactor(t){try{return{data:await $(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(r){if(D(r))return{data:null,error:r};throw r}}}const $y={getItem:e=>Fn()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Fn()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Fn()&&globalThis.localStorage.removeItem(e)}};function uc(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}function Uy(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const Nr={debug:!!(globalThis&&Fn()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class jf extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Fy extends jf{}async function zy(e,t,r){Nr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const n=new globalThis.AbortController;return t>0&&setTimeout(()=>{n.abort(),Nr.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:n.signal},async s=>{if(s){Nr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await r()}finally{Nr.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}else{if(t===0)throw Nr.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Fy(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Nr.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}Uy();const Hy={url:Z0,storageKey:ey,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:ty,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function cc(e,t,r){return await r()}class is{constructor(t){var r,n;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=is.nextInstanceID,is.nextInstanceID+=1,this.instanceID>0&&nt()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},Hy),t);if(this.logDebugMessages=!!s.debug,typeof s.debug=="function"&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new My({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=Ef(s.fetch),this.lock=s.lock||cc,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:nt()&&(!((r=globalThis==null?void 0:globalThis.navigator)===null||r===void 0)&&r.locks)?this.lock=zy:this.lock=cc,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:Fn()?this.storage=$y:(this.memoryStorage={},this.storage=uc(this.memoryStorage)):(this.memoryStorage={},this.storage=uc(this.memoryStorage)),nt()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(n=this.broadcastChannel)===null||n===void 0||n.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${xf}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const r=gy(window.location.href);let n="none";if(this._isImplicitGrantCallback(r)?n="implicit":await this._isPKCECallback(r)&&(n="pkce"),nt()&&this.detectSessionInUrl&&n!=="none"){const{data:s,error:i}=await this._getSessionFromURL(r,n);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),ay(i)){const u=(t=i.details)===null||t===void 0?void 0:t.code;if(u==="identity_already_exists"||u==="identity_not_found"||u==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:o,redirectType:a}=s;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(r){return D(r)?{error:r}:{error:new kf("Unexpected error during initialization",r)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var r,n,s;try{const i=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(n=(r=t==null?void 0:t.options)===null||r===void 0?void 0:r.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:(s=t==null?void 0:t.options)===null||s===void 0?void 0:s.captchaToken}},xform:bt}),{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const u=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",u)),{data:{user:c,session:u},error:null}}catch(i){if(D(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(t){var r,n,s;try{let i;if("email"in t){const{email:d,password:p,options:m}=t;let y=null,w=null;this.flowType==="pkce"&&([y,w]=await jr(this.storage,this.storageKey)),i=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:m==null?void 0:m.emailRedirectTo,body:{email:d,password:p,data:(r=m==null?void 0:m.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken},code_challenge:y,code_challenge_method:w},xform:bt})}else if("phone"in t){const{phone:d,password:p,options:m}=t;i=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:d,password:p,data:(n=m==null?void 0:m.data)!==null&&n!==void 0?n:{},channel:(s=m==null?void 0:m.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken}},xform:bt})}else throw new As("You must provide either an email or phone number and a password");const{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const u=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",u)),{data:{user:c,session:u},error:null}}catch(i){if(D(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(t){try{let r;if("email"in t){const{email:i,password:o,options:a}=t;r=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:lc})}else if("phone"in t){const{phone:i,password:o,options:a}=t;r=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:lc})}else throw new As("You must provide either an email or phone number and a password");const{data:n,error:s}=r;return s?{data:{user:null,session:null},error:s}:!n||!n.session||!n.user?{data:{user:null,session:null},error:new To}:(n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign({user:n.user,session:n.session},n.weak_password?{weakPassword:n.weak_password}:null),error:s})}catch(r){if(D(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithOAuth(t){var r,n,s,i;return await this._handleProviderSignIn(t.provider,{redirectTo:(r=t.options)===null||r===void 0?void 0:r.redirectTo,scopes:(n=t.options)===null||n===void 0?void 0:n.scopes,queryParams:(s=t.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(i=t.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async _exchangeCodeForSession(t){const r=await Ls(this.storage,`${this.storageKey}-code-verifier`),[n,s]=(r??"").split("/");try{const{data:i,error:o}=await $(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:n},xform:bt});if(await Ms(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new To}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:s??null}),error:o})}catch(i){if(D(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(t){try{const{options:r,provider:n,token:s,access_token:i,nonce:o}=t,a=await $(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:n,id_token:s,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:r==null?void 0:r.captchaToken}},xform:bt}),{data:u,error:c}=a;return c?{data:{user:null,session:null},error:c}:!u||!u.session||!u.user?{data:{user:null,session:null},error:new To}:(u.session&&(await this._saveSession(u.session),await this._notifyAllSubscribers("SIGNED_IN",u.session)),{data:u,error:c})}catch(r){if(D(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithOtp(t){var r,n,s,i,o;try{if("email"in t){const{email:a,options:u}=t;let c=null,d=null;this.flowType==="pkce"&&([c,d]=await jr(this.storage,this.storageKey));const{error:p}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(r=u==null?void 0:u.data)!==null&&r!==void 0?r:{},create_user:(n=u==null?void 0:u.shouldCreateUser)!==null&&n!==void 0?n:!0,gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken},code_challenge:c,code_challenge_method:d},redirectTo:u==null?void 0:u.emailRedirectTo});return{data:{user:null,session:null},error:p}}if("phone"in t){const{phone:a,options:u}=t,{data:c,error:d}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(s=u==null?void 0:u.data)!==null&&s!==void 0?s:{},create_user:(i=u==null?void 0:u.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken},channel:(o=u==null?void 0:u.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:c==null?void 0:c.message_id},error:d}}throw new As("You must provide either an email or phone number.")}catch(a){if(D(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(t){var r,n;try{let s,i;"options"in t&&(s=(r=t.options)===null||r===void 0?void 0:r.redirectTo,i=(n=t.options)===null||n===void 0?void 0:n.captchaToken);const{data:o,error:a}=await $(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:bt});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const u=o.session,c=o.user;return u!=null&&u.access_token&&(await this._saveSession(u),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",u)),{data:{user:c,session:u},error:null}}catch(s){if(D(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(t){var r,n,s;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=await jr(this.storage,this.storageKey)),await $(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(n=(r=t.options)===null||r===void 0?void 0:r.redirectTo)!==null&&n!==void 0?n:void 0}),!((s=t==null?void 0:t.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:Ry})}catch(i){if(D(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:r},error:n}=t;if(n)throw n;if(!r)throw new Et;const{error:s}=await $(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:r.access_token});return{data:{user:null,session:null},error:s}})}catch(t){if(D(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const r=`${this.url}/resend`;if("email"in t){const{email:n,type:s,options:i}=t,{error:o}=await $(this.fetch,"POST",r,{headers:this.headers,body:{email:n,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:n,type:s,options:i}=t,{data:o,error:a}=await $(this.fetch,"POST",r,{headers:this.headers,body:{phone:n,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new As("You must provide either an email or phone number and a type")}catch(r){if(D(r))return{data:{user:null,session:null},error:r};throw r}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async r=>r))}async _acquireLock(t,r){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const n=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await n,await r()))();return this.pendingInLock.push((async()=>{try{await s}catch{}})()),s}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const n=r();for(this.pendingInLock.push((async()=>{try{await n}catch{}})()),await n;this.pendingInLock.length;){const s=[...this.pendingInLock];await Promise.all(s),this.pendingInLock.splice(0,s.length)}return await n}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const r=await this.__loadSession();return await t(r)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const r=await Ls(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",r),r!==null&&(this._isValidSession(r)?t=r:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const n=t.expires_at?t.expires_at*1e3-Date.now()<Co:!1;if(this._debug("#__loadSession()",`session has${n?"":" not"} expired`,"expires_at",t.expires_at),!n){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(u,c,d)=>(!o&&c==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(u,c,d))})}return{data:{session:t},error:null}}const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{session:null},error:i}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Rt}):await this._useSession(async r=>{var n,s,i;const{data:o,error:a}=r;if(a)throw a;return!(!((n=o.session)===null||n===void 0)&&n.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new Et}:await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(s=o.session)===null||s===void 0?void 0:s.access_token)!==null&&i!==void 0?i:void 0,xform:Rt})})}catch(r){if(D(r))return oy(r)&&(await this._removeSession(),await Ms(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:r};throw r}}async updateUser(t,r={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,r))}async _updateUser(t,r={}){try{return await this._useSession(async n=>{const{data:s,error:i}=n;if(i)throw i;if(!s.session)throw new Et;const o=s.session;let a=null,u=null;this.flowType==="pkce"&&t.email!=null&&([a,u]=await jr(this.storage,this.storageKey));const{data:c,error:d}=await $(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:r==null?void 0:r.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:a,code_challenge_method:u}),jwt:o.access_token,xform:Rt});if(d)throw d;return o.user=c.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(n){if(D(n))return{data:{user:null},error:n};throw n}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new Et;const r=Date.now()/1e3;let n=r,s=!0,i=null;const{payload:o}=Po(t.access_token);if(o.exp&&(n=o.exp,s=n<=r),s){const{session:a,error:u}=await this._callRefreshToken(t.refresh_token);if(u)return{data:{user:null,session:null},error:u};if(!a)return{data:{user:null,session:null},error:null};i=a}else{const{data:a,error:u}=await this._getUser(t.access_token);if(u)throw u;i={access_token:t.access_token,refresh_token:t.refresh_token,user:a.user,token_type:"bearer",expires_in:n-r,expires_at:n},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(r){if(D(r))return{data:{session:null,user:null},error:r};throw r}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async r=>{var n;if(!t){const{data:o,error:a}=r;if(a)throw a;t=(n=o.session)!==null&&n!==void 0?n:void 0}if(!(t!=null&&t.refresh_token))throw new Et;const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(r){if(D(r))return{data:{user:null,session:null},error:r};throw r}}async _getSessionFromURL(t,r){try{if(!nt())throw new Ds("No browser detected.");if(t.error||t.error_description||t.error_code)throw new Ds(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(r){case"implicit":if(this.flowType==="pkce")throw new rc("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Ds("Not a valid implicit grant flow url.");break;default:}if(r==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new rc("No code detected.");const{data:h,error:g}=await this._exchangeCodeForSession(t.code);if(g)throw g;const _=new URL(window.location.href);return _.searchParams.delete("code"),window.history.replaceState(window.history.state,"",_.toString()),{data:{session:h.session,redirectType:null},error:null}}const{provider_token:n,provider_refresh_token:s,access_token:i,refresh_token:o,expires_in:a,expires_at:u,token_type:c}=t;if(!i||!a||!o||!c)throw new Ds("No session defined in URL");const d=Math.round(Date.now()/1e3),p=parseInt(a);let m=d+p;u&&(m=parseInt(u));const y=m-d;y*1e3<=Or&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${y}s, should have been closer to ${p}s`);const w=m-p;d-w>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",w,m,d):d-w<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",w,m,d);const{data:x,error:k}=await this._getUser(i);if(k)throw k;const f={provider_token:n,provider_refresh_token:s,access_token:i,expires_in:p,expires_at:m,refresh_token:o,token_type:c,user:x.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:f,redirectType:t.type},error:null}}catch(n){if(D(n))return{data:{session:null,redirectType:null},error:n};throw n}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const r=await Ls(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&r)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async r=>{var n;const{data:s,error:i}=r;if(i)return{error:i};const o=(n=s.session)===null||n===void 0?void 0:n.access_token;if(o){const{error:a}=await this.admin.signOut(o,t);if(a&&!(iy(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return t!=="others"&&(await this._removeSession(),await Ms(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const r=my(),n={id:r,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",r),this.stateChangeEmitters.delete(r)}};return this._debug("#onAuthStateChange()","registered callback with id",r),this.stateChangeEmitters.set(r,n),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(r)})))(),{data:{subscription:n}}}async _emitInitialSession(t){return await this._useSession(async r=>{var n,s;try{const{data:{session:i},error:o}=r;if(o)throw o;await((n=this.stateChangeEmitters.get(t))===null||n===void 0?void 0:n.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",t,"session",i)}catch(i){await((s=this.stateChangeEmitters.get(t))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",i),console.error(i)}})}async resetPasswordForEmail(t,r={}){let n=null,s=null;this.flowType==="pkce"&&([n,s]=await jr(this.storage,this.storageKey,!0));try{return await $(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:n,code_challenge_method:s,gotrue_meta_security:{captcha_token:r.captchaToken}},headers:this.headers,redirectTo:r.redirectTo})}catch(i){if(D(i))return{data:null,error:i};throw i}}async getUserIdentities(){var t;try{const{data:r,error:n}=await this.getUser();if(n)throw n;return{data:{identities:(t=r.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(r){if(D(r))return{data:null,error:r};throw r}}async linkIdentity(t){var r;try{const{data:n,error:s}=await this._useSession(async i=>{var o,a,u,c,d;const{data:p,error:m}=i;if(m)throw m;const y=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=t.options)===null||a===void 0?void 0:a.scopes,queryParams:(u=t.options)===null||u===void 0?void 0:u.queryParams,skipBrowserRedirect:!0});return await $(this.fetch,"GET",y,{headers:this.headers,jwt:(d=(c=p.session)===null||c===void 0?void 0:c.access_token)!==null&&d!==void 0?d:void 0})});if(s)throw s;return nt()&&!(!((r=t.options)===null||r===void 0)&&r.skipBrowserRedirect)&&window.location.assign(n==null?void 0:n.url),{data:{provider:t.provider,url:n==null?void 0:n.url},error:null}}catch(n){if(D(n))return{data:{provider:t.provider,url:null},error:n};throw n}}async unlinkIdentity(t){try{return await this._useSession(async r=>{var n,s;const{data:i,error:o}=r;if(o)throw o;return await $(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(s=(n=i.session)===null||n===void 0?void 0:n.access_token)!==null&&s!==void 0?s:void 0})})}catch(r){if(D(r))return{data:null,error:r};throw r}}async _refreshAccessToken(t){const r=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(r,"begin");try{const n=Date.now();return await wy(async s=>(s>0&&await vy(200*Math.pow(2,s-1)),this._debug(r,"refreshing attempt",s),await $(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:bt})),(s,i)=>{const o=200*Math.pow(2,s);return i&&Oo(i)&&Date.now()+o-n<Or})}catch(n){if(this._debug(r,"error",n),D(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(r,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,r){const n=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:r.redirectTo,scopes:r.scopes,queryParams:r.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",r,"url",n),nt()&&!r.skipBrowserRedirect&&window.location.assign(n),{data:{provider:t,url:n},error:null}}async _recoverAndRefresh(){var t;const r="#_recoverAndRefresh()";this._debug(r,"begin");try{const n=await Ls(this.storage,this.storageKey);if(this._debug(r,"session from storage",n),!this._isValidSession(n)){this._debug(r,"session is not valid"),n!==null&&await this._removeSession();return}const s=((t=n.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<Co;if(this._debug(r,`session has${s?"":" not"} expired with margin of ${Co}s`),s){if(this.autoRefreshToken&&n.refresh_token){const{error:i}=await this._callRefreshToken(n.refresh_token);i&&(console.error(i),Oo(i)||(this._debug(r,"refresh failed with a non-retryable error, removing the session",i),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){this._debug(r,"error",n),console.error(n);return}finally{this._debug(r,"end")}}async _callRefreshToken(t){var r,n;if(!t)throw new Et;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Yi;const{data:i,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!i.session)throw new Et;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const a={session:i.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(i){if(this._debug(s,"error",i),D(i)){const o={session:null,error:i};return Oo(i)||await this._removeSession(),(r=this.refreshingDeferred)===null||r===void 0||r.resolve(o),o}throw(n=this.refreshingDeferred)===null||n===void 0||n.reject(i),i}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(t,r,n=!0){const s=`#_notifyAllSubscribers(${t})`;this._debug(s,"begin",r,`broadcast = ${n}`);try{this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:t,session:r});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(t,r)}catch(u){i.push(u)}});if(await Promise.all(o),i.length>0){for(let a=0;a<i.length;a+=1)console.error(i[a]);throw i[0]}}finally{this._debug(s,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await bf(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Ms(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&nt()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(r){console.error("removing visibilitychange callback failed",r)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),Or);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async r=>{const{data:{session:n}}=r;if(!n||!n.refresh_token||!n.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const s=Math.floor((n.expires_at*1e3-t)/Or);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${Or}ms, refresh threshold is ${Ra} ticks`),s<=Ra&&await this._callRefreshToken(n.refresh_token)})}catch(r){console.error("Auto refresh tick failed with error. This is likely a transient error.",r)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof jf)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!nt()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const r=`#_onVisibilityChanged(${t})`;this._debug(r,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(r,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,r,n){const s=[`provider=${encodeURIComponent(r)}`];if(n!=null&&n.redirectTo&&s.push(`redirect_to=${encodeURIComponent(n.redirectTo)}`),n!=null&&n.scopes&&s.push(`scopes=${encodeURIComponent(n.scopes)}`),this.flowType==="pkce"){const[i,o]=await jr(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});s.push(a.toString())}if(n!=null&&n.queryParams){const i=new URLSearchParams(n.queryParams);s.push(i.toString())}return n!=null&&n.skipBrowserRedirect&&s.push(`skip_http_redirect=${n.skipBrowserRedirect}`),`${t}?${s.join("&")}`}async _unenroll(t){try{return await this._useSession(async r=>{var n;const{data:s,error:i}=r;return i?{data:null,error:i}:await $(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(n=s==null?void 0:s.session)===null||n===void 0?void 0:n.access_token})})}catch(r){if(D(r))return{data:null,error:r};throw r}}async _enroll(t){try{return await this._useSession(async r=>{var n,s;const{data:i,error:o}=r;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:u,error:c}=await $(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(n=i==null?void 0:i.session)===null||n===void 0?void 0:n.access_token});return c?{data:null,error:c}:(t.factorType==="totp"&&(!((s=u==null?void 0:u.totp)===null||s===void 0)&&s.qr_code)&&(u.totp.qr_code=`data:image/svg+xml;utf-8,${u.totp.qr_code}`),{data:u,error:null})})}catch(r){if(D(r))return{data:null,error:r};throw r}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async r=>{var n;const{data:s,error:i}=r;if(i)return{data:null,error:i};const{data:o,error:a}=await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(n=s==null?void 0:s.session)===null||n===void 0?void 0:n.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(r){if(D(r))return{data:null,error:r};throw r}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async r=>{var n;const{data:s,error:i}=r;return i?{data:null,error:i}:await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(n=s==null?void 0:s.session)===null||n===void 0?void 0:n.access_token})})}catch(r){if(D(r))return{data:null,error:r};throw r}})}async _challengeAndVerify(t){const{data:r,error:n}=await this._challenge({factorId:t.factorId});return n?{data:null,error:n}:await this._verify({factorId:t.factorId,challengeId:r.id,code:t.code})}async _listFactors(){const{data:{user:t},error:r}=await this.getUser();if(r)return{data:null,error:r};const n=(t==null?void 0:t.factors)||[],s=n.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=n.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:n,totp:s,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var r,n;const{data:{session:s},error:i}=t;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Po(s.access_token);let a=null;o.aal&&(a=o.aal);let u=a;((n=(r=s.user.factors)===null||r===void 0?void 0:r.filter(p=>p.status==="verified"))!==null&&n!==void 0?n:[]).length>0&&(u="aal2");const d=o.amr||[];return{data:{currentLevel:a,nextLevel:u,currentAuthenticationMethods:d},error:null}}))}async fetchJwk(t,r={keys:[]}){let n=r.keys.find(o=>o.kid===t);if(n||(n=this.jwks.keys.find(o=>o.kid===t),n&&this.jwks_cached_at+ny>Date.now()))return n;const{data:s,error:i}=await $(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!s.keys||s.keys.length===0)throw new Un("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),n=s.keys.find(o=>o.kid===t),!n)throw new Un("No matching signing key found in JWKS");return n}async getClaims(t,r={keys:[]}){try{let n=t;if(!n){const{data:y,error:w}=await this.getSession();if(w||!y.session)return{data:null,error:w};n=y.session.access_token}const{header:s,payload:i,signature:o,raw:{header:a,payload:u}}=Po(n);if(jy(i.exp),!s.kid||s.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:y}=await this.getUser(n);if(y)throw y;return{data:{claims:i,header:s,signature:o},error:null}}const c=Ny(s.alg),d=await this.fetchJwk(s.kid,r),p=await crypto.subtle.importKey("jwk",d,c,!0,["verify"]);if(!await crypto.subtle.verify(c,p,o,fy(`${a}.${u}`)))throw new Un("Invalid JWT signature");return{data:{claims:i,header:s,signature:o},error:null}}catch(n){if(D(n))return{data:null,error:n};throw n}}}is.nextInstanceID=0;const By=is;class Wy extends By{constructor(t){super(t)}}var Gy=globalThis&&globalThis.__awaiter||function(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(d){try{c(n.next(d))}catch(p){o(p)}}function u(d){try{c(n.throw(d))}catch(p){o(p)}}function c(d){d.done?i(d.value):s(d.value).then(a,u)}c((n=n.apply(e,t||[])).next())})};class Vy{constructor(t,r,n){var s,i,o;if(this.supabaseUrl=t,this.supabaseKey=r,!t)throw new Error("supabaseUrl is required.");if(!r)throw new Error("supabaseKey is required.");const a=Q0(t),u=new URL(a);this.realtimeUrl=new URL("realtime/v1",u),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",u),this.storageUrl=new URL("storage/v1",u),this.functionsUrl=new URL("functions/v1",u);const c=`sb-${u.hostname.split(".")[0]}-auth-token`,d={db:B0,realtime:G0,auth:Object.assign(Object.assign({},W0),{storageKey:c}),global:H0},p=X0(n??{},d);this.storageKey=(s=p.auth.storageKey)!==null&&s!==void 0?s:"",this.headers=(i=p.global.headers)!==null&&i!==void 0?i:{},p.accessToken?(this.accessToken=p.accessToken,this.auth=new Proxy({},{get:(m,y)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(y)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=p.auth)!==null&&o!==void 0?o:{},this.headers,p.global.fetch),this.fetch=K0(r,this._getAccessToken.bind(this),p.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},p.realtime)),this.rest=new c0(new URL("rest/v1",u).href,{headers:this.headers,schema:p.db.schema,fetch:this.fetch}),p.accessToken||this._listenForAuthEvents()}get functions(){return new Fg(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new U0(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,r={},n={}){return this.rest.rpc(t,r,n)}channel(t,r={config:{}}){return this.realtime.channel(t,r)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,r;return Gy(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:n}=yield this.auth.getSession();return(r=(t=n.session)===null||t===void 0?void 0:t.access_token)!==null&&r!==void 0?r:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:r,detectSessionInUrl:n,storage:s,storageKey:i,flowType:o,lock:a,debug:u},c,d){const p={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Wy({url:this.authUrl.href,headers:Object.assign(Object.assign({},p),c),storageKey:i,autoRefreshToken:t,persistSession:r,detectSessionInUrl:n,storage:s,flowType:o,lock:a,debug:u,fetch:d,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new b0(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((r,n)=>{this._handleTokenChanged(r,"CLIENT",n==null?void 0:n.access_token)})}_handleTokenChanged(t,r,n){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==n?this.changedAccessToken=n:t==="SIGNED_OUT"&&(this.realtime.setAuth(),r=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const qy=(e,t,r)=>new Vy(e,t,r),Yy="https://nbzbqptayogfxdtmfavm.supabase.co",Ky="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ztn2k4bRfzyJBAoHcwIPnq4tEvUBED5Gz3culqBjiYQ",re=qy(Yy,Ky),Nf=I.createContext({}),Ki=()=>{const e=I.useContext(Nf);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e},Jy=({children:e})=>{const[t,r]=I.useState(null),[n,s]=I.useState(!0);I.useEffect(()=>{(async()=>{var x;console.log("Getting initial session...");const{data:{session:w}}=await re.auth.getSession();console.log("Initial session:",((x=w==null?void 0:w.user)==null?void 0:x.id)||"No user"),r((w==null?void 0:w.user)??null),s(!1)})();const{data:{subscription:y}}=re.auth.onAuthStateChange(async(w,x)=>{var k;console.log("Auth state change:",w,((k=x==null?void 0:x.user)==null?void 0:k.id)||"No user"),r((x==null?void 0:x.user)??null),s(!1)});return()=>y.unsubscribe()},[]);const p={user:t,loading:n,signUp:async(m,y,w={})=>{try{const{data:x,error:k}=await re.auth.signUp({email:m,password:y,options:{data:w}});if(k)throw k;return{data:x,error:null}}catch(x){return{data:null,error:x}}},signIn:async(m,y)=>{try{const{data:w,error:x}=await re.auth.signInWithPassword({email:m,password:y});if(x)throw x;return{data:w,error:null}}catch(w){return{data:null,error:w}}},signOut:async()=>{try{const{error:m}=await re.auth.signOut();if(m)throw m;return{error:null}}catch(m){return{error:m}}},resetPassword:async m=>{try{const{data:y,error:w}=await re.auth.resetPasswordForEmail(m,{redirectTo:`${window.location.origin}/reset-password`});if(w)throw w;return{data:y,error:null}}catch(y){return{data:null,error:y}}},updatePassword:async m=>{try{const{data:y,error:w}=await re.auth.updateUser({password:m});if(w)throw w;return{data:y,error:null}}catch(y){return{data:null,error:y}}},updateProfile:async m=>{try{const{data:y,error:w}=await re.auth.updateUser({data:m});if(w)throw w;return{data:y,error:null}}catch(y){return{data:null,error:y}}}};return l.jsx(Nf.Provider,{value:p,children:e})},Qy=()=>{const{user:e,signOut:t}=Ki(),[r,n]=I.useState(!1),s=async()=>{await t(),n(!1)},i=()=>{var a,u,c;return(a=e==null?void 0:e.user_metadata)!=null&&a.full_name?e.user_metadata.full_name:(u=e==null?void 0:e.user_metadata)!=null&&u.first_name?e.user_metadata.first_name:((c=e==null?void 0:e.email)==null?void 0:c.split("@")[0])||"User"},o=()=>i().split(" ").map(u=>u[0]).join("").toUpperCase().slice(0,2);return l.jsxs("div",{className:"relative",children:[l.jsxs("button",{onClick:()=>n(!r),className:"flex items-center space-x-2 bg-gray-50 hover:bg-gray-100 rounded-lg px-3 py-2 transition-colors",children:[l.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium",children:o()}),l.jsxs("div",{className:"hidden sm:block text-left",children:[l.jsx("div",{className:"text-sm font-medium text-gray-900",children:i()}),l.jsx("div",{className:"text-xs text-gray-500",children:e==null?void 0:e.email})]}),l.jsx(Tl,{className:"w-4 h-4 text-gray-400"})]}),r&&l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"fixed inset-0 z-10",onClick:()=>n(!1)}),l.jsxs("div",{className:"absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-20",children:[l.jsx("div",{className:"p-4 border-b border-gray-100",children:l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium",children:o()}),l.jsxs("div",{children:[l.jsx("div",{className:"font-medium text-gray-900",children:i()}),l.jsx("div",{className:"text-sm text-gray-500",children:e==null?void 0:e.email})]})]})}),l.jsxs("div",{className:"py-2",children:[l.jsxs("button",{onClick:()=>{n(!1)},className:"w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[l.jsx(Og,{className:"w-4 h-4"}),l.jsx("span",{children:"Settings"})]}),l.jsxs("button",{onClick:s,className:"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2",children:[l.jsx(Eg,{className:"w-4 h-4"}),l.jsx("span",{children:"Sign Out"})]})]})]})]})]})},dc=({onNewTask:e,filter:t,setFilter:r,stats:n,viewMode:s,setViewMode:i})=>{const[o,a]=I.useState(!1),u=()=>{r({status:"",priority:"",category:"",search:""})},c=t.status||t.priority||t.category||t.search;return l.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10",children:l.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[l.jsxs("div",{className:"flex items-center justify-between h-16",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"bg-gradient-to-r from-primary-500 to-purple-600 rounded-xl p-2",children:l.jsx(Bt,{className:"w-6 h-6 text-white"})}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Idea Manager"}),l.jsx("p",{className:"text-sm text-gray-500 hidden sm:block",children:"Transform ideas into reality"})]})]}),l.jsxs("div",{className:"hidden lg:flex items-center space-x-6 bg-gray-50 rounded-lg px-4 py-2",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-3 h-3 bg-purple-500 rounded-full"}),l.jsx("span",{className:"text-sm font-medium text-gray-900",children:n.ideas}),l.jsx("span",{className:"text-sm text-gray-600",children:"Ideas"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),l.jsx("span",{className:"text-sm font-medium text-gray-900",children:n.inProgress}),l.jsx("span",{className:"text-sm text-gray-600",children:"Active"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),l.jsx("span",{className:"text-sm font-medium text-gray-900",children:n.completed}),l.jsx("span",{className:"text-sm text-gray-600",children:"Done"})]})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsxs("div",{className:"relative hidden md:block",children:[l.jsx(Yu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx("input",{type:"text",placeholder:"Search ideas...",className:"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm",value:t.search,onChange:d=>r(p=>({...p,search:d.target.value}))})]}),l.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[l.jsx("button",{onClick:()=>i("dashboard"),className:`p-1.5 rounded-md transition-all duration-200 ${s==="dashboard"?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,title:"Dashboard View",children:l.jsx(kg,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>i("list"),className:`p-1.5 rounded-md transition-all duration-200 ${s==="list"?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,title:"List View",children:l.jsx(Sg,{className:"w-4 h-4"})})]}),l.jsxs("button",{onClick:()=>a(!o),className:`p-2 rounded-lg border transition-all duration-200 ${c||o?"bg-primary-50 border-primary-200 text-primary-700 shadow-sm":"bg-white border-gray-300 text-gray-600 hover:bg-gray-50"}`,title:"Filters",children:[l.jsx(_g,{className:"w-5 h-5"}),c&&l.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full"})]}),l.jsxs("button",{onClick:e,className:"bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2 transition-all duration-200 shadow-sm hover:shadow-md",children:[l.jsx(Wt,{className:"w-4 h-4"}),l.jsx("span",{className:"hidden sm:inline",children:"New Idea"})]}),l.jsx(Qy,{})]})]}),l.jsx("div",{className:"md:hidden pb-3",children:l.jsxs("div",{className:"relative",children:[l.jsx(Yu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx("input",{type:"text",placeholder:"Search ideas...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm",value:t.search,onChange:d=>r(p=>({...p,search:d.target.value}))})]})}),o&&l.jsxs("div",{className:"border-t border-gray-100 py-4 space-y-4",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Filter & Sort"}),c&&l.jsxs("button",{onClick:u,className:"text-sm text-gray-500 hover:text-gray-700 flex items-center space-x-1",children:[l.jsx(vr,{className:"w-4 h-4"}),l.jsx("span",{children:"Clear all"})]})]}),l.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-2",children:"Status"}),l.jsxs("select",{className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:t.status,onChange:d=>r(p=>({...p,status:d.target.value})),children:[l.jsx("option",{value:"",children:"All Statuses"}),l.jsx("option",{value:P.IDEA,children:"💡 Ideas"}),l.jsx("option",{value:P.PLANNING,children:"📋 Planning"}),l.jsx("option",{value:P.IN_PROGRESS,children:"⚡ In Progress"}),l.jsx("option",{value:P.REVIEW,children:"👀 Review"}),l.jsx("option",{value:P.COMPLETED,children:"✅ Completed"}),l.jsx("option",{value:P.ON_HOLD,children:"⏸️ On Hold"}),l.jsx("option",{value:P.CANCELLED,children:"❌ Cancelled"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-2",children:"Priority"}),l.jsxs("select",{className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:t.priority,onChange:d=>r(p=>({...p,priority:d.target.value})),children:[l.jsx("option",{value:"",children:"All Priorities"}),l.jsx("option",{value:Ie.URGENT,children:"🔴 Urgent"}),l.jsx("option",{value:Ie.HIGH,children:"🟠 High"}),l.jsx("option",{value:Ie.MEDIUM,children:"🟡 Medium"}),l.jsx("option",{value:Ie.LOW,children:"🟢 Low"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-2",children:"Category"}),l.jsxs("select",{className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:t.category,onChange:d=>r(p=>({...p,category:d.target.value})),children:[l.jsx("option",{value:"",children:"All Categories"}),l.jsx("option",{value:me.FEATURE,children:"✨ Feature"}),l.jsx("option",{value:me.BUG_FIX,children:"🐛 Bug Fix"}),l.jsx("option",{value:me.IMPROVEMENT,children:"🚀 Improvement"}),l.jsx("option",{value:me.RESEARCH,children:"🔍 Research"}),l.jsx("option",{value:me.DOCUMENTATION,children:"📚 Documentation"}),l.jsx("option",{value:me.TESTING,children:"🧪 Testing"}),l.jsx("option",{value:me.OTHER,children:"📋 Other"})]})]})]})]})]})})},Xy=({tasks:e,onNewTask:t,filter:r,setFilter:n,stats:s,onViewModeChange:i})=>{var p,m,y,w,x;const o={[P.IDEA]:e.filter(k=>k.status===P.IDEA),[P.PLANNING]:e.filter(k=>k.status===P.PLANNING),[P.IN_PROGRESS]:e.filter(k=>k.status===P.IN_PROGRESS),[P.REVIEW]:e.filter(k=>k.status===P.REVIEW),[P.COMPLETED]:e.filter(k=>k.status===P.COMPLETED),[P.ON_HOLD]:e.filter(k=>k.status===P.ON_HOLD)},a=[{key:P.IDEA,title:"Ideas",icon:Bt,color:"purple",description:"Capture and explore new concepts",count:o[P.IDEA].length,action:"Brainstorm",nextStage:"planning"},{key:P.PLANNING,title:"Planning",icon:Xh,color:"blue",description:"Define scope and create action plans",count:o[P.PLANNING].length,action:"Plan",nextStage:"execution"},{key:P.IN_PROGRESS,title:"In Progress",icon:tf,color:"orange",description:"Actively working on implementation",count:o[P.IN_PROGRESS].length,action:"Execute",nextStage:"review"},{key:P.REVIEW,title:"Review",icon:Jh,color:"amber",description:"Testing and validation phase",count:o[P.REVIEW].length,action:"Review",nextStage:"completion"},{key:P.COMPLETED,title:"Completed",icon:hr,color:"green",description:"Successfully finished tasks",count:o[P.COMPLETED].length,action:"Celebrate",nextStage:null}],u=({stage:k,isActive:f,onClick:h})=>{const g=k.icon,_={purple:"bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100",blue:"bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100",orange:"bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100",amber:"bg-amber-50 border-amber-200 text-amber-700 hover:bg-amber-100",green:"bg-green-50 border-green-200 text-green-700 hover:bg-green-100"};return l.jsxs("button",{onClick:h,className:`p-4 rounded-xl border-2 transition-all duration-200 text-left w-full ${f?"ring-2 ring-offset-2 ring-primary-500 "+_[k.color]:_[k.color]}`,children:[l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsx(g,{className:"w-6 h-6"}),l.jsx("span",{className:"text-2xl font-bold",children:k.count})]}),l.jsx("h3",{className:"font-semibold mb-1",children:k.title}),l.jsx("p",{className:"text-sm opacity-80",children:k.description}),l.jsxs("div",{className:"mt-2 flex items-center text-sm font-medium",children:[l.jsx("span",{children:k.action}),k.nextStage&&l.jsx(_i,{className:"w-3 h-3 ml-1"})]})]})},c=k=>{n(f=>({...f,status:k})),i&&i("list")},d=()=>{t()};return l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[l.jsxs("div",{className:"flex items-center justify-between mb-6",children:[l.jsxs("div",{children:[l.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Your Innovation Pipeline"}),l.jsx("p",{className:"text-gray-600",children:"Track ideas from conception to completion"})]}),l.jsxs("button",{onClick:d,className:"bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-6 py-3 rounded-xl font-medium flex items-center space-x-2 transition-all duration-200 shadow-sm hover:shadow-md",children:[l.jsx(Wt,{className:"w-5 h-5"}),l.jsx("span",{children:"New Idea"})]})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:a.map((k,f)=>l.jsxs("div",{className:"relative",children:[l.jsx(u,{stage:k,isActive:r.status===k.key,onClick:()=>c(k.key)}),f<a.length-1&&l.jsx("div",{className:"hidden lg:block absolute top-1/2 -right-2 transform -translate-y-1/2 z-10",children:l.jsx(_i,{className:"w-4 h-4 text-gray-400"})})]},k.key))})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[l.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[l.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[l.jsx("div",{className:"bg-blue-100 rounded-lg p-2",children:l.jsx(Zh,{className:"w-5 h-5 text-blue-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"Progress Overview"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-sm text-gray-600",children:"Total Active"}),l.jsx("span",{className:"font-semibold",children:s.inProgress+s.planning})]}),l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-sm text-gray-600",children:"Completion Rate"}),l.jsxs("span",{className:"font-semibold",children:[s.total>0?Math.round(s.completed/s.total*100):0,"%"]})]})]})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[l.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[l.jsx("div",{className:"bg-purple-100 rounded-lg p-2",children:l.jsx(Bt,{className:"w-5 h-5 text-purple-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"Ideas Pipeline"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-sm text-gray-600",children:"New Ideas"}),l.jsx("span",{className:"font-semibold",children:s.ideas})]}),l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-sm text-gray-600",children:"Ready to Plan"}),l.jsx("span",{className:"font-semibold",children:o[P.IDEA].length})]})]})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[l.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[l.jsx("div",{className:"bg-green-100 rounded-lg p-2",children:l.jsx(hr,{className:"w-5 h-5 text-green-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"Recent Wins"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-sm text-gray-600",children:"Completed"}),l.jsx("span",{className:"font-semibold",children:s.completed})]}),l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-sm text-gray-600",children:"This Month"}),l.jsx("span",{className:"font-semibold",children:o[P.COMPLETED].filter(k=>{const f=new Date(k.updatedAt||k.createdAt),h=new Date;return f.getMonth()===h.getMonth()&&f.getFullYear()===h.getFullYear()}).length})]})]})]})]}),r.status&&l.jsxs("div",{className:"bg-gradient-to-r from-primary-50 to-purple-50 rounded-xl border border-primary-200 p-6",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"bg-primary-100 rounded-lg p-2",children:((p=a.find(k=>k.key===r.status))==null?void 0:p.icon)&&Vc.createElement(a.find(k=>k.key===r.status).icon,{className:"w-5 h-5 text-primary-600"})}),l.jsxs("h3",{className:"font-semibold text-gray-900",children:[(m=a.find(k=>k.key===r.status))==null?void 0:m.title," Stage"]})]}),l.jsx("button",{onClick:()=>{n(k=>({...k,status:""})),i&&i("dashboard")},className:"text-gray-400 hover:text-gray-600",children:l.jsx(vr,{className:"w-5 h-5"})})]}),l.jsx("p",{className:"text-gray-700 mb-4",children:(y=a.find(k=>k.key===r.status))==null?void 0:y.description}),l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("button",{onClick:d,className:"bg-white text-primary-700 border border-primary-200 px-4 py-2 rounded-lg font-medium hover:bg-primary-50 transition-colors",children:"Add New Task"}),((w=a.find(k=>k.key===r.status))==null?void 0:w.nextStage)&&l.jsxs("span",{className:"text-sm text-gray-600",children:["Next: Move tasks to ",(x=a.find(k=>k.key===r.status))==null?void 0:x.nextStage]})]})]})]})};function Si(e){"@babel/helpers - typeof";return Si=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Si(e)}function xt(e){if(e===null||e===!0||e===!1)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function Q(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function Ue(e){Q(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||Si(e)==="object"&&t==="[object Date]"?new Date(e.getTime()):typeof e=="number"||t==="[object Number]"?new Date(e):((typeof e=="string"||t==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function Cf(e,t){Q(2,arguments);var r=Ue(e),n=xt(t);return isNaN(n)?new Date(NaN):(n&&r.setDate(r.getDate()+n),r)}function Zy(e,t){Q(2,arguments);var r=Ue(e).getTime(),n=xt(t);return new Date(r+n)}var ev={};function Ji(){return ev}function tv(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}function hc(e){Q(1,arguments);var t=Ue(e);return t.setHours(0,0,0,0),t}function Dl(e,t){Q(2,arguments);var r=hc(e),n=hc(t);return r.getTime()===n.getTime()}function rv(e){return Q(1,arguments),e instanceof Date||Si(e)==="object"&&Object.prototype.toString.call(e)==="[object Date]"}function nv(e){if(Q(1,arguments),!rv(e)&&typeof e!="number")return!1;var t=Ue(e);return!isNaN(Number(t))}function sv(e,t){Q(2,arguments);var r=xt(t);return Zy(e,-r)}var iv=864e5;function ov(e){Q(1,arguments);var t=Ue(e),r=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var n=t.getTime(),s=r-n;return Math.floor(s/iv)+1}function Ei(e){Q(1,arguments);var t=1,r=Ue(e),n=r.getUTCDay(),s=(n<t?7:0)+n-t;return r.setUTCDate(r.getUTCDate()-s),r.setUTCHours(0,0,0,0),r}function Tf(e){Q(1,arguments);var t=Ue(e),r=t.getUTCFullYear(),n=new Date(0);n.setUTCFullYear(r+1,0,4),n.setUTCHours(0,0,0,0);var s=Ei(n),i=new Date(0);i.setUTCFullYear(r,0,4),i.setUTCHours(0,0,0,0);var o=Ei(i);return t.getTime()>=s.getTime()?r+1:t.getTime()>=o.getTime()?r:r-1}function av(e){Q(1,arguments);var t=Tf(e),r=new Date(0);r.setUTCFullYear(t,0,4),r.setUTCHours(0,0,0,0);var n=Ei(r);return n}var lv=6048e5;function uv(e){Q(1,arguments);var t=Ue(e),r=Ei(t).getTime()-av(t).getTime();return Math.round(r/lv)+1}function bi(e,t){var r,n,s,i,o,a,u,c;Q(1,arguments);var d=Ji(),p=xt((r=(n=(s=(i=t==null?void 0:t.weekStartsOn)!==null&&i!==void 0?i:t==null||(o=t.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.weekStartsOn)!==null&&s!==void 0?s:d.weekStartsOn)!==null&&n!==void 0?n:(u=d.locale)===null||u===void 0||(c=u.options)===null||c===void 0?void 0:c.weekStartsOn)!==null&&r!==void 0?r:0);if(!(p>=0&&p<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var m=Ue(e),y=m.getUTCDay(),w=(y<p?7:0)+y-p;return m.setUTCDate(m.getUTCDate()-w),m.setUTCHours(0,0,0,0),m}function Of(e,t){var r,n,s,i,o,a,u,c;Q(1,arguments);var d=Ue(e),p=d.getUTCFullYear(),m=Ji(),y=xt((r=(n=(s=(i=t==null?void 0:t.firstWeekContainsDate)!==null&&i!==void 0?i:t==null||(o=t.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&s!==void 0?s:m.firstWeekContainsDate)!==null&&n!==void 0?n:(u=m.locale)===null||u===void 0||(c=u.options)===null||c===void 0?void 0:c.firstWeekContainsDate)!==null&&r!==void 0?r:1);if(!(y>=1&&y<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var w=new Date(0);w.setUTCFullYear(p+1,0,y),w.setUTCHours(0,0,0,0);var x=bi(w,t),k=new Date(0);k.setUTCFullYear(p,0,y),k.setUTCHours(0,0,0,0);var f=bi(k,t);return d.getTime()>=x.getTime()?p+1:d.getTime()>=f.getTime()?p:p-1}function cv(e,t){var r,n,s,i,o,a,u,c;Q(1,arguments);var d=Ji(),p=xt((r=(n=(s=(i=t==null?void 0:t.firstWeekContainsDate)!==null&&i!==void 0?i:t==null||(o=t.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&s!==void 0?s:d.firstWeekContainsDate)!==null&&n!==void 0?n:(u=d.locale)===null||u===void 0||(c=u.options)===null||c===void 0?void 0:c.firstWeekContainsDate)!==null&&r!==void 0?r:1),m=Of(e,t),y=new Date(0);y.setUTCFullYear(m,0,p),y.setUTCHours(0,0,0,0);var w=bi(y,t);return w}var dv=6048e5;function hv(e,t){Q(1,arguments);var r=Ue(e),n=bi(r,t).getTime()-cv(r,t).getTime();return Math.round(n/dv)+1}function F(e,t){for(var r=e<0?"-":"",n=Math.abs(e).toString();n.length<t;)n="0"+n;return r+n}var fv={y:function(t,r){var n=t.getUTCFullYear(),s=n>0?n:1-n;return F(r==="yy"?s%100:s,r.length)},M:function(t,r){var n=t.getUTCMonth();return r==="M"?String(n+1):F(n+1,2)},d:function(t,r){return F(t.getUTCDate(),r.length)},a:function(t,r){var n=t.getUTCHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h:function(t,r){return F(t.getUTCHours()%12||12,r.length)},H:function(t,r){return F(t.getUTCHours(),r.length)},m:function(t,r){return F(t.getUTCMinutes(),r.length)},s:function(t,r){return F(t.getUTCSeconds(),r.length)},S:function(t,r){var n=r.length,s=t.getUTCMilliseconds(),i=Math.floor(s*Math.pow(10,n-3));return F(i,r.length)}};const St=fv;var Cr={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},pv={G:function(t,r,n){var s=t.getUTCFullYear()>0?1:0;switch(r){case"G":case"GG":case"GGG":return n.era(s,{width:"abbreviated"});case"GGGGG":return n.era(s,{width:"narrow"});case"GGGG":default:return n.era(s,{width:"wide"})}},y:function(t,r,n){if(r==="yo"){var s=t.getUTCFullYear(),i=s>0?s:1-s;return n.ordinalNumber(i,{unit:"year"})}return St.y(t,r)},Y:function(t,r,n,s){var i=Of(t,s),o=i>0?i:1-i;if(r==="YY"){var a=o%100;return F(a,2)}return r==="Yo"?n.ordinalNumber(o,{unit:"year"}):F(o,r.length)},R:function(t,r){var n=Tf(t);return F(n,r.length)},u:function(t,r){var n=t.getUTCFullYear();return F(n,r.length)},Q:function(t,r,n){var s=Math.ceil((t.getUTCMonth()+1)/3);switch(r){case"Q":return String(s);case"QQ":return F(s,2);case"Qo":return n.ordinalNumber(s,{unit:"quarter"});case"QQQ":return n.quarter(s,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(s,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(s,{width:"wide",context:"formatting"})}},q:function(t,r,n){var s=Math.ceil((t.getUTCMonth()+1)/3);switch(r){case"q":return String(s);case"qq":return F(s,2);case"qo":return n.ordinalNumber(s,{unit:"quarter"});case"qqq":return n.quarter(s,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(s,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(s,{width:"wide",context:"standalone"})}},M:function(t,r,n){var s=t.getUTCMonth();switch(r){case"M":case"MM":return St.M(t,r);case"Mo":return n.ordinalNumber(s+1,{unit:"month"});case"MMM":return n.month(s,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(s,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(s,{width:"wide",context:"formatting"})}},L:function(t,r,n){var s=t.getUTCMonth();switch(r){case"L":return String(s+1);case"LL":return F(s+1,2);case"Lo":return n.ordinalNumber(s+1,{unit:"month"});case"LLL":return n.month(s,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(s,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(s,{width:"wide",context:"standalone"})}},w:function(t,r,n,s){var i=hv(t,s);return r==="wo"?n.ordinalNumber(i,{unit:"week"}):F(i,r.length)},I:function(t,r,n){var s=uv(t);return r==="Io"?n.ordinalNumber(s,{unit:"week"}):F(s,r.length)},d:function(t,r,n){return r==="do"?n.ordinalNumber(t.getUTCDate(),{unit:"date"}):St.d(t,r)},D:function(t,r,n){var s=ov(t);return r==="Do"?n.ordinalNumber(s,{unit:"dayOfYear"}):F(s,r.length)},E:function(t,r,n){var s=t.getUTCDay();switch(r){case"E":case"EE":case"EEE":return n.day(s,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(s,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(s,{width:"short",context:"formatting"});case"EEEE":default:return n.day(s,{width:"wide",context:"formatting"})}},e:function(t,r,n,s){var i=t.getUTCDay(),o=(i-s.weekStartsOn+8)%7||7;switch(r){case"e":return String(o);case"ee":return F(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(i,{width:"short",context:"formatting"});case"eeee":default:return n.day(i,{width:"wide",context:"formatting"})}},c:function(t,r,n,s){var i=t.getUTCDay(),o=(i-s.weekStartsOn+8)%7||7;switch(r){case"c":return String(o);case"cc":return F(o,r.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(i,{width:"narrow",context:"standalone"});case"cccccc":return n.day(i,{width:"short",context:"standalone"});case"cccc":default:return n.day(i,{width:"wide",context:"standalone"})}},i:function(t,r,n){var s=t.getUTCDay(),i=s===0?7:s;switch(r){case"i":return String(i);case"ii":return F(i,r.length);case"io":return n.ordinalNumber(i,{unit:"day"});case"iii":return n.day(s,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(s,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(s,{width:"short",context:"formatting"});case"iiii":default:return n.day(s,{width:"wide",context:"formatting"})}},a:function(t,r,n){var s=t.getUTCHours(),i=s/12>=1?"pm":"am";switch(r){case"a":case"aa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(i,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(t,r,n){var s=t.getUTCHours(),i;switch(s===12?i=Cr.noon:s===0?i=Cr.midnight:i=s/12>=1?"pm":"am",r){case"b":case"bb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(i,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(t,r,n){var s=t.getUTCHours(),i;switch(s>=17?i=Cr.evening:s>=12?i=Cr.afternoon:s>=4?i=Cr.morning:i=Cr.night,r){case"B":case"BB":case"BBB":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(i,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(t,r,n){if(r==="ho"){var s=t.getUTCHours()%12;return s===0&&(s=12),n.ordinalNumber(s,{unit:"hour"})}return St.h(t,r)},H:function(t,r,n){return r==="Ho"?n.ordinalNumber(t.getUTCHours(),{unit:"hour"}):St.H(t,r)},K:function(t,r,n){var s=t.getUTCHours()%12;return r==="Ko"?n.ordinalNumber(s,{unit:"hour"}):F(s,r.length)},k:function(t,r,n){var s=t.getUTCHours();return s===0&&(s=24),r==="ko"?n.ordinalNumber(s,{unit:"hour"}):F(s,r.length)},m:function(t,r,n){return r==="mo"?n.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):St.m(t,r)},s:function(t,r,n){return r==="so"?n.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):St.s(t,r)},S:function(t,r){return St.S(t,r)},X:function(t,r,n,s){var i=s._originalDate||t,o=i.getTimezoneOffset();if(o===0)return"Z";switch(r){case"X":return pc(o);case"XXXX":case"XX":return ir(o);case"XXXXX":case"XXX":default:return ir(o,":")}},x:function(t,r,n,s){var i=s._originalDate||t,o=i.getTimezoneOffset();switch(r){case"x":return pc(o);case"xxxx":case"xx":return ir(o);case"xxxxx":case"xxx":default:return ir(o,":")}},O:function(t,r,n,s){var i=s._originalDate||t,o=i.getTimezoneOffset();switch(r){case"O":case"OO":case"OOO":return"GMT"+fc(o,":");case"OOOO":default:return"GMT"+ir(o,":")}},z:function(t,r,n,s){var i=s._originalDate||t,o=i.getTimezoneOffset();switch(r){case"z":case"zz":case"zzz":return"GMT"+fc(o,":");case"zzzz":default:return"GMT"+ir(o,":")}},t:function(t,r,n,s){var i=s._originalDate||t,o=Math.floor(i.getTime()/1e3);return F(o,r.length)},T:function(t,r,n,s){var i=s._originalDate||t,o=i.getTime();return F(o,r.length)}};function fc(e,t){var r=e>0?"-":"+",n=Math.abs(e),s=Math.floor(n/60),i=n%60;if(i===0)return r+String(s);var o=t||"";return r+String(s)+o+F(i,2)}function pc(e,t){if(e%60===0){var r=e>0?"-":"+";return r+F(Math.abs(e)/60,2)}return ir(e,t)}function ir(e,t){var r=t||"",n=e>0?"-":"+",s=Math.abs(e),i=F(Math.floor(s/60),2),o=F(s%60,2);return n+i+r+o}const mv=pv;var mc=function(t,r){switch(t){case"P":return r.date({width:"short"});case"PP":return r.date({width:"medium"});case"PPP":return r.date({width:"long"});case"PPPP":default:return r.date({width:"full"})}},Pf=function(t,r){switch(t){case"p":return r.time({width:"short"});case"pp":return r.time({width:"medium"});case"ppp":return r.time({width:"long"});case"pppp":default:return r.time({width:"full"})}},gv=function(t,r){var n=t.match(/(P+)(p+)?/)||[],s=n[1],i=n[2];if(!i)return mc(t,r);var o;switch(s){case"P":o=r.dateTime({width:"short"});break;case"PP":o=r.dateTime({width:"medium"});break;case"PPP":o=r.dateTime({width:"long"});break;case"PPPP":default:o=r.dateTime({width:"full"});break}return o.replace("{{date}}",mc(s,r)).replace("{{time}}",Pf(i,r))},yv={p:Pf,P:gv};const vv=yv;var wv=["D","DD"],xv=["YY","YYYY"];function _v(e){return wv.indexOf(e)!==-1}function kv(e){return xv.indexOf(e)!==-1}function gc(e,t,r){if(e==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var Sv={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ev=function(t,r,n){var s,i=Sv[t];return typeof i=="string"?s=i:r===1?s=i.one:s=i.other.replace("{{count}}",r.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+s:s+" ago":s};const bv=Ev;function Ro(e){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.width?String(t.width):e.defaultWidth,n=e.formats[r]||e.formats[e.defaultWidth];return n}}var jv={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Nv={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Cv={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Tv={date:Ro({formats:jv,defaultWidth:"full"}),time:Ro({formats:Nv,defaultWidth:"full"}),dateTime:Ro({formats:Cv,defaultWidth:"full"})};const Ov=Tv;var Pv={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Rv=function(t,r,n,s){return Pv[t]};const Iv=Rv;function kn(e){return function(t,r){var n=r!=null&&r.context?String(r.context):"standalone",s;if(n==="formatting"&&e.formattingValues){var i=e.defaultFormattingWidth||e.defaultWidth,o=r!=null&&r.width?String(r.width):i;s=e.formattingValues[o]||e.formattingValues[i]}else{var a=e.defaultWidth,u=r!=null&&r.width?String(r.width):e.defaultWidth;s=e.values[u]||e.values[a]}var c=e.argumentCallback?e.argumentCallback(t):t;return s[c]}}var Av={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Dv={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Lv={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Mv={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},$v={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Uv={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Fv=function(t,r){var n=Number(t),s=n%100;if(s>20||s<10)switch(s%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},zv={ordinalNumber:Fv,era:kn({values:Av,defaultWidth:"wide"}),quarter:kn({values:Dv,defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:kn({values:Lv,defaultWidth:"wide"}),day:kn({values:Mv,defaultWidth:"wide"}),dayPeriod:kn({values:$v,defaultWidth:"wide",formattingValues:Uv,defaultFormattingWidth:"wide"})};const Hv=zv;function Sn(e){return function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=r.width,s=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],i=t.match(s);if(!i)return null;var o=i[0],a=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(a)?Wv(a,function(p){return p.test(o)}):Bv(a,function(p){return p.test(o)}),c;c=e.valueCallback?e.valueCallback(u):u,c=r.valueCallback?r.valueCallback(c):c;var d=t.slice(o.length);return{value:c,rest:d}}}function Bv(e,t){for(var r in e)if(e.hasOwnProperty(r)&&t(e[r]))return r}function Wv(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return r}function Gv(e){return function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.match(e.matchPattern);if(!n)return null;var s=n[0],i=t.match(e.parsePattern);if(!i)return null;var o=e.valueCallback?e.valueCallback(i[0]):i[0];o=r.valueCallback?r.valueCallback(o):o;var a=t.slice(s.length);return{value:o,rest:a}}}var Vv=/^(\d+)(th|st|nd|rd)?/i,qv=/\d+/i,Yv={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Kv={any:[/^b/i,/^(a|c)/i]},Jv={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Qv={any:[/1/i,/2/i,/3/i,/4/i]},Xv={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Zv={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},ew={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},tw={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},rw={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},nw={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},sw={ordinalNumber:Gv({matchPattern:Vv,parsePattern:qv,valueCallback:function(t){return parseInt(t,10)}}),era:Sn({matchPatterns:Yv,defaultMatchWidth:"wide",parsePatterns:Kv,defaultParseWidth:"any"}),quarter:Sn({matchPatterns:Jv,defaultMatchWidth:"wide",parsePatterns:Qv,defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:Sn({matchPatterns:Xv,defaultMatchWidth:"wide",parsePatterns:Zv,defaultParseWidth:"any"}),day:Sn({matchPatterns:ew,defaultMatchWidth:"wide",parsePatterns:tw,defaultParseWidth:"any"}),dayPeriod:Sn({matchPatterns:rw,defaultMatchWidth:"any",parsePatterns:nw,defaultParseWidth:"any"})};const iw=sw;var ow={code:"en-US",formatDistance:bv,formatLong:Ov,formatRelative:Iv,localize:Hv,match:iw,options:{weekStartsOn:0,firstWeekContainsDate:1}};const aw=ow;var lw=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,uw=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,cw=/^'([^]*?)'?$/,dw=/''/g,hw=/[a-zA-Z]/;function fw(e,t,r){var n,s,i,o,a,u,c,d,p,m,y,w,x,k,f,h,g,_;Q(2,arguments);var S=String(t),b=Ji(),E=(n=(s=r==null?void 0:r.locale)!==null&&s!==void 0?s:b.locale)!==null&&n!==void 0?n:aw,v=xt((i=(o=(a=(u=r==null?void 0:r.firstWeekContainsDate)!==null&&u!==void 0?u:r==null||(c=r.locale)===null||c===void 0||(d=c.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&a!==void 0?a:b.firstWeekContainsDate)!==null&&o!==void 0?o:(p=b.locale)===null||p===void 0||(m=p.options)===null||m===void 0?void 0:m.firstWeekContainsDate)!==null&&i!==void 0?i:1);if(!(v>=1&&v<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var j=xt((y=(w=(x=(k=r==null?void 0:r.weekStartsOn)!==null&&k!==void 0?k:r==null||(f=r.locale)===null||f===void 0||(h=f.options)===null||h===void 0?void 0:h.weekStartsOn)!==null&&x!==void 0?x:b.weekStartsOn)!==null&&w!==void 0?w:(g=b.locale)===null||g===void 0||(_=g.options)===null||_===void 0?void 0:_.weekStartsOn)!==null&&y!==void 0?y:0);if(!(j>=0&&j<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!E.localize)throw new RangeError("locale must contain localize property");if(!E.formatLong)throw new RangeError("locale must contain formatLong property");var C=Ue(e);if(!nv(C))throw new RangeError("Invalid time value");var X=tv(C),lt=sv(C,X),ut={firstWeekContainsDate:v,weekStartsOn:j,locale:E,_originalDate:C},Sr=S.match(uw).map(function(ue){var Se=ue[0];if(Se==="p"||Se==="P"){var tt=vv[Se];return tt(ue,E.formatLong)}return ue}).join("").match(lw).map(function(ue){if(ue==="''")return"'";var Se=ue[0];if(Se==="'")return pw(ue);var tt=mv[Se];if(tt)return!(r!=null&&r.useAdditionalWeekYearTokens)&&kv(ue)&&gc(ue,t,String(e)),!(r!=null&&r.useAdditionalDayOfYearTokens)&&_v(ue)&&gc(ue,t,String(e)),tt(lt,ue,E.localize,ut);if(Se.match(hw))throw new RangeError("Format string contains an unescaped latin alphabet character `"+Se+"`");return ue}).join("");return Sr}function pw(e){var t=e.match(cw);return t?t[1].replace(dw,"'"):e}function mw(e){return Q(1,arguments),Ue(e).getTime()<Date.now()}function Rf(e){return Q(1,arguments),Dl(e,Date.now())}function gw(e){return Q(1,arguments),Dl(e,Cf(Date.now(),1))}function yw(e,t){Q(2,arguments);var r=xt(t);return Cf(e,-r)}function vw(e){return Q(1,arguments),Dl(e,yw(Date.now(),1))}const yc=e=>{if(!e)return"";const t=new Date(e);return Rf(t)?"Today":gw(t)?"Tomorrow":vw(t)?"Yesterday":fw(t,"MMM dd, yyyy")},If=e=>e?mw(new Date(e))&&!Rf(new Date(e)):!1,ww=e=>{const t={feature:"✨",bug_fix:"🐛",improvement:"🚀",research:"🔍",documentation:"📚",testing:"🧪",other:"📋"};return t[e]||t.other},xw=e=>{if(!e||e.length===0)return 0;const t=e.filter(r=>r.completed).length;return Math.round(t/e.length*100)},_w=({task:e,onEdit:t,onDelete:r,onToggleFollowUp:n,onToggleProcessStep:s})=>{const[i,o]=I.useState(!1),[a,u]=I.useState(!1),c=xw(e.processSteps),d=If(e.dueDate),p={urgent:"bg-red-100 text-red-800 border-red-200",high:"bg-orange-100 text-orange-800 border-orange-200",medium:"bg-yellow-100 text-yellow-800 border-yellow-200",low:"bg-green-100 text-green-800 border-green-200"},m={idea:"bg-purple-100 text-purple-800",planning:"bg-blue-100 text-blue-800",in_progress:"bg-orange-100 text-orange-800",review:"bg-amber-100 text-amber-800",completed:"bg-green-100 text-green-800",on_hold:"bg-gray-100 text-gray-800",cancelled:"bg-red-100 text-red-800"},y=x=>x.replace(/_/g," ").replace(/\b\w/g,k=>k.toUpperCase()),w=x=>x.charAt(0).toUpperCase()+x.slice(1);return l.jsxs("div",{className:`bg-white rounded-xl shadow-sm border transition-all duration-200 hover:shadow-md ${d?"border-red-200 ring-1 ring-red-200":"border-gray-200 hover:border-gray-300"}`,children:[l.jsxs("div",{className:"p-5",children:[l.jsxs("div",{className:"flex items-start justify-between mb-3",children:[l.jsxs("div",{className:"flex items-center space-x-3 flex-1",children:[l.jsx("span",{className:"text-xl",children:ww(e.category)}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("h3",{className:"font-semibold text-gray-900 truncate",children:e.title}),e.description&&l.jsx("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:e.description})]})]}),l.jsxs("div",{className:"relative ml-2",children:[l.jsx("button",{onClick:()=>u(!a),className:"p-1.5 rounded-lg hover:bg-gray-100 transition-colors",children:l.jsx(jg,{className:"w-4 h-4 text-gray-400"})}),a&&l.jsxs("div",{className:"absolute right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 py-1 min-w-[140px]",children:[l.jsxs("button",{onClick:()=>{t(e),u(!1)},className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[l.jsx(Ng,{className:"w-4 h-4"}),l.jsx("span",{children:"Edit"})]}),l.jsxs("button",{onClick:()=>{o(!i),u(!1)},className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[l.jsx(bo,{className:"w-4 h-4"}),l.jsxs("span",{children:[i?"Hide":"View"," Details"]})]}),l.jsx("hr",{className:"my-1"}),l.jsxs("button",{onClick:()=>{r(e.id),u(!1)},className:"w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2",children:[l.jsx(ja,{className:"w-4 h-4"}),l.jsx("span",{children:"Delete"})]})]})]})]}),l.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[l.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${m[e.status]||"bg-gray-100 text-gray-800"}`,children:y(e.status)}),l.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${p[e.priority]||"bg-gray-100 text-gray-800 border-gray-200"}`,children:[l.jsx(Qh,{className:"w-3 h-3 mr-1"}),w(e.priority)]}),d&&l.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200",children:[l.jsx(Kh,{className:"w-3 h-3 mr-1"}),"Overdue"]})]}),e.processSteps&&e.processSteps.length>0&&l.jsxs("div",{className:"mb-4",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm mb-2",children:[l.jsx("span",{className:"text-gray-600",children:"Progress"}),l.jsxs("span",{className:"font-medium text-gray-900",children:[c,"%"]})]}),l.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:l.jsx("div",{className:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:{width:`${c}%`}})})]}),l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[e.dueDate&&l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(yg,{className:"w-4 h-4"}),l.jsx("span",{className:d?"text-red-600 font-medium":"",children:yc(e.dueDate)})]}),e.followUps&&e.followUps.length>0&&l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(bo,{className:"w-4 h-4"}),l.jsx("span",{children:e.followUps.length})]})]}),l.jsxs("button",{onClick:()=>o(!i),className:"text-primary-600 hover:text-primary-700 font-medium flex items-center space-x-1",children:[l.jsx("span",{children:i?"Less":"More"}),i?l.jsx(wg,{className:"w-4 h-4"}):l.jsx(Tl,{className:"w-4 h-4"})]})]})]}),i&&l.jsxs("div",{className:"border-t border-gray-100 p-5 space-y-4",children:[e.processSteps&&e.processSteps.length>0&&l.jsxs("div",{children:[l.jsxs("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[l.jsx(gg,{className:"w-4 h-4 mr-2"}),"Process Steps"]}),l.jsx("div",{className:"space-y-2",children:e.processSteps.map((x,k)=>l.jsxs("div",{className:"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50",children:[l.jsx("button",{onClick:()=>s(e.id,x.id||k),className:`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${x.completed?"bg-green-500 border-green-500 text-white":"border-gray-300 hover:border-gray-400"}`,children:x.completed&&l.jsx(hr,{className:"w-3 h-3"})}),l.jsxs("div",{className:"flex-1",children:[l.jsx("span",{className:`text-sm ${x.completed?"text-gray-500 line-through":"text-gray-900"}`,children:x.title}),x.description&&l.jsx("p",{className:"text-xs text-gray-600 mt-1",children:x.description})]})]},x.id||k))})]}),e.followUps&&e.followUps.length>0&&l.jsxs("div",{children:[l.jsxs("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[l.jsx(bo,{className:"w-4 h-4 mr-2"}),"Follow-ups"]}),l.jsx("div",{className:"space-y-2",children:e.followUps.map((x,k)=>l.jsxs("div",{className:"flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50",children:[l.jsx("button",{onClick:()=>n(e.id,x.id||k),className:`w-5 h-5 rounded border-2 flex items-center justify-center mt-0.5 transition-colors ${x.completed?"bg-green-500 border-green-500 text-white":"border-gray-300 hover:border-gray-400"}`,children:x.completed&&l.jsx(hr,{className:"w-3 h-3"})}),l.jsxs("div",{className:"flex-1",children:[l.jsx("p",{className:`text-sm ${x.completed?"text-gray-500 line-through":"text-gray-900"}`,children:x.description||x.text}),x.dueDate&&l.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Due: ",yc(x.dueDate)]})]})]},x.id||k))})]}),e.tags&&e.tags.length>0&&l.jsxs("div",{children:[l.jsxs("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[l.jsx(Pg,{className:"w-4 h-4 mr-2"}),"Tags"]}),l.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.map((x,k)=>l.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:x},k))})]})]})]})},kw=({tasks:e,onEdit:t,onDelete:r,onToggleFollowUp:n,onToggleProcessStep:s,onNewTask:i,filter:o})=>{const[a,u]=I.useState({overdue:!0,priority:!0,status:!0}),c=e.filter(f=>If(f.dueDate)&&f.status!==P.COMPLETED),d=e.filter(f=>f.priority===Ie.URGENT||f.priority===Ie.HIGH),p=e.reduce((f,h)=>(f[h.status]||(f[h.status]=[]),f[h.status].push(h),f),{}),m=[P.IDEA,P.PLANNING,P.IN_PROGRESS,P.REVIEW,P.COMPLETED,P.ON_HOLD,P.CANCELLED],y={[P.IDEA]:{label:"💡 Ideas",color:"purple"},[P.PLANNING]:{label:"📋 Planning",color:"blue"},[P.IN_PROGRESS]:{label:"⚡ In Progress",color:"orange"},[P.REVIEW]:{label:"👀 Review",color:"amber"},[P.COMPLETED]:{label:"✅ Completed",color:"green"},[P.ON_HOLD]:{label:"⏸️ On Hold",color:"gray"},[P.CANCELLED]:{label:"❌ Cancelled",color:"red"}},w=f=>{u(h=>({...h,[f]:!h[f]}))},x=({title:f,count:h,isExpanded:g,onToggle:_,icon:S,color:b,actionButton:E})=>l.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 mb-4",children:[l.jsxs("button",{onClick:_,className:"flex items-center space-x-3 flex-1 text-left",children:[g?l.jsx(Tl,{className:"w-5 h-5 text-gray-400"}):l.jsx(vg,{className:"w-5 h-5 text-gray-400"}),S&&l.jsx(S,{className:`w-5 h-5 text-${b}-600`}),l.jsx("h3",{className:"font-semibold text-gray-900",children:f}),l.jsx("span",{className:`ml-2 px-2 py-1 rounded-full text-xs font-medium bg-${b}-100 text-${b}-800`,children:h})]}),E]}),k=({tasks:f,emptyMessage:h,emptyAction:g})=>f.length===0?l.jsxs("div",{className:"text-center py-8 border-2 border-dashed border-gray-200 rounded-lg",children:[l.jsx("p",{className:"text-gray-500 mb-4",children:h}),g]}):l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:f.map(_=>l.jsx(_w,{task:_,onEdit:t,onDelete:r,onToggleFollowUp:n,onToggleProcessStep:s},_.id))});return e.length===0?l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:l.jsx("span",{className:"text-4xl",children:"🔍"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No tasks found"}),l.jsx("p",{className:"text-gray-600 mb-6",children:o.search||o.status||o.priority||o.category?"Try adjusting your filters or create a new task.":"Start by creating your first idea or task!"}),l.jsxs("button",{onClick:i,className:"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center space-x-2",children:[l.jsx(Wt,{className:"w-5 h-5"}),l.jsx("span",{children:"Create New Task"})]})]}):l.jsxs("div",{className:"space-y-6",children:[(o.search||o.status||o.priority||o.category)&&l.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),l.jsxs("span",{className:"text-sm font-medium text-blue-900",children:["Filtered Results",o.status&&` • Status: ${o.status.replace(/_/g," ").replace(/\b\w/g,f=>f.toUpperCase())}`,o.priority&&` • Priority: ${o.priority.charAt(0).toUpperCase()+o.priority.slice(1)}`,o.category&&` • Category: ${o.category.charAt(0).toUpperCase()+o.category.slice(1)}`,o.search&&` • Search: "${o.search}"`]})]}),l.jsxs("span",{className:"text-sm text-blue-700",children:[e.length," task",e.length!==1?"s":""," found"]})]})}),c.length>0&&l.jsxs("div",{children:[l.jsx(x,{title:"Overdue Tasks",count:c.length,isExpanded:a.overdue,onToggle:()=>w("overdue"),icon:Kh,color:"red"}),a.overdue&&l.jsx(k,{tasks:c,emptyMessage:"No overdue tasks"})]}),d.length>0&&l.jsxs("div",{children:[l.jsx(x,{title:"High Priority",count:d.length,isExpanded:a.priority,onToggle:()=>w("priority"),icon:Qh,color:"orange"}),a.priority&&l.jsx(k,{tasks:d,emptyMessage:"No high priority tasks"})]}),l.jsxs("div",{children:[l.jsx(x,{title:"All Tasks by Status",count:e.length,isExpanded:a.status,onToggle:()=>w("status"),icon:Rg,color:"blue",actionButton:l.jsxs("button",{onClick:i,className:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium inline-flex items-center space-x-2 text-sm",children:[l.jsx(Wt,{className:"w-4 h-4"}),l.jsx("span",{children:"Add Task"})]})}),a.status&&l.jsx("div",{className:"space-y-6",children:m.filter(f=>{var h;return((h=p[f])==null?void 0:h.length)>0}).map(f=>{const h=y[f],g=p[f];return l.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("h4",{className:"font-medium text-gray-900 flex items-center space-x-2",children:[l.jsx("span",{children:h.label}),l.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium bg-${h.color}-100 text-${h.color}-800`,children:g.length})]}),f===P.IDEA&&l.jsxs("button",{onClick:i,className:"text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center space-x-1",children:[l.jsx(Wt,{className:"w-4 h-4"}),l.jsx("span",{children:"Add Idea"})]})]}),l.jsx(k,{tasks:g,emptyMessage:`No ${h.label.toLowerCase()} tasks`,emptyAction:f===P.IDEA&&l.jsx("button",{onClick:i,className:"text-primary-600 hover:text-primary-700 font-medium",children:"Create your first idea"})})]},f)})})]})]})};var vc;(function(e){e.STRING="string",e.NUMBER="number",e.INTEGER="integer",e.BOOLEAN="boolean",e.ARRAY="array",e.OBJECT="object"})(vc||(vc={}));/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var wc;(function(e){e.LANGUAGE_UNSPECIFIED="language_unspecified",e.PYTHON="python"})(wc||(wc={}));var xc;(function(e){e.OUTCOME_UNSPECIFIED="outcome_unspecified",e.OUTCOME_OK="outcome_ok",e.OUTCOME_FAILED="outcome_failed",e.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"})(xc||(xc={}));/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const _c=["user","model","function","system"];var kc;(function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",e.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"})(kc||(kc={}));var Sc;(function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"})(Sc||(Sc={}));var Ec;(function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"})(Ec||(Ec={}));var bc;(function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER"})(bc||(bc={}));var zn;(function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.LANGUAGE="LANGUAGE",e.BLOCKLIST="BLOCKLIST",e.PROHIBITED_CONTENT="PROHIBITED_CONTENT",e.SPII="SPII",e.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",e.OTHER="OTHER"})(zn||(zn={}));var jc;(function(e){e.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",e.RETRIEVAL_QUERY="RETRIEVAL_QUERY",e.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",e.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",e.CLASSIFICATION="CLASSIFICATION",e.CLUSTERING="CLUSTERING"})(jc||(jc={}));var Nc;(function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.AUTO="AUTO",e.ANY="ANY",e.NONE="NONE"})(Nc||(Nc={}));var Cc;(function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.MODE_DYNAMIC="MODE_DYNAMIC"})(Cc||(Cc={}));/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ye extends Error{constructor(t){super(`[GoogleGenerativeAI Error]: ${t}`)}}class Tr extends ye{constructor(t,r){super(t),this.response=r}}class Af extends ye{constructor(t,r,n,s){super(t),this.status=r,this.statusText=n,this.errorDetails=s}}class Gt extends ye{}class Df extends ye{}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Sw="https://generativelanguage.googleapis.com",Ew="v1beta",bw="0.24.1",jw="genai-js";var wr;(function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.EMBED_CONTENT="embedContent",e.BATCH_EMBED_CONTENTS="batchEmbedContents"})(wr||(wr={}));class Nw{constructor(t,r,n,s,i){this.model=t,this.task=r,this.apiKey=n,this.stream=s,this.requestOptions=i}toString(){var t,r;const n=((t=this.requestOptions)===null||t===void 0?void 0:t.apiVersion)||Ew;let i=`${((r=this.requestOptions)===null||r===void 0?void 0:r.baseUrl)||Sw}/${n}/${this.model}:${this.task}`;return this.stream&&(i+="?alt=sse"),i}}function Cw(e){const t=[];return e!=null&&e.apiClient&&t.push(e.apiClient),t.push(`${jw}/${bw}`),t.join(" ")}async function Tw(e){var t;const r=new Headers;r.append("Content-Type","application/json"),r.append("x-goog-api-client",Cw(e.requestOptions)),r.append("x-goog-api-key",e.apiKey);let n=(t=e.requestOptions)===null||t===void 0?void 0:t.customHeaders;if(n){if(!(n instanceof Headers))try{n=new Headers(n)}catch(s){throw new Gt(`unable to convert customHeaders value ${JSON.stringify(n)} to Headers: ${s.message}`)}for(const[s,i]of n.entries()){if(s==="x-goog-api-key")throw new Gt(`Cannot set reserved header name ${s}`);if(s==="x-goog-api-client")throw new Gt(`Header name ${s} can only be set using the apiClient field`);r.append(s,i)}}return r}async function Ow(e,t,r,n,s,i){const o=new Nw(e,t,r,n,i);return{url:o.toString(),fetchOptions:Object.assign(Object.assign({},Aw(i)),{method:"POST",headers:await Tw(o),body:s})}}async function ms(e,t,r,n,s,i={},o=fetch){const{url:a,fetchOptions:u}=await Ow(e,t,r,n,s,i);return Pw(a,u,o)}async function Pw(e,t,r=fetch){let n;try{n=await r(e,t)}catch(s){Rw(s,e)}return n.ok||await Iw(n,e),n}function Rw(e,t){let r=e;throw r.name==="AbortError"?(r=new Df(`Request aborted when fetching ${t.toString()}: ${e.message}`),r.stack=e.stack):e instanceof Af||e instanceof Gt||(r=new ye(`Error fetching from ${t.toString()}: ${e.message}`),r.stack=e.stack),r}async function Iw(e,t){let r="",n;try{const s=await e.json();r=s.error.message,s.error.details&&(r+=` ${JSON.stringify(s.error.details)}`,n=s.error.details)}catch{}throw new Af(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${r}`,e.status,e.statusText,n)}function Aw(e){const t={};if((e==null?void 0:e.signal)!==void 0||(e==null?void 0:e.timeout)>=0){const r=new AbortController;(e==null?void 0:e.timeout)>=0&&setTimeout(()=>r.abort(),e.timeout),e!=null&&e.signal&&e.signal.addEventListener("abort",()=>{r.abort()}),t.signal=r.signal}return t}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Ll(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),Js(e.candidates[0]))throw new Tr(`${Tt(e)}`,e);return Dw(e)}else if(e.promptFeedback)throw new Tr(`Text not available. ${Tt(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),Js(e.candidates[0]))throw new Tr(`${Tt(e)}`,e);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),Tc(e)[0]}else if(e.promptFeedback)throw new Tr(`Function call not available. ${Tt(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),Js(e.candidates[0]))throw new Tr(`${Tt(e)}`,e);return Tc(e)}else if(e.promptFeedback)throw new Tr(`Function call not available. ${Tt(e)}`,e)},e}function Dw(e){var t,r,n,s;const i=[];if(!((r=(t=e.candidates)===null||t===void 0?void 0:t[0].content)===null||r===void 0)&&r.parts)for(const o of(s=(n=e.candidates)===null||n===void 0?void 0:n[0].content)===null||s===void 0?void 0:s.parts)o.text&&i.push(o.text),o.executableCode&&i.push("\n```"+o.executableCode.language+`
`+o.executableCode.code+"\n```\n"),o.codeExecutionResult&&i.push("\n```\n"+o.codeExecutionResult.output+"\n```\n");return i.length>0?i.join(""):""}function Tc(e){var t,r,n,s;const i=[];if(!((r=(t=e.candidates)===null||t===void 0?void 0:t[0].content)===null||r===void 0)&&r.parts)for(const o of(s=(n=e.candidates)===null||n===void 0?void 0:n[0].content)===null||s===void 0?void 0:s.parts)o.functionCall&&i.push(o.functionCall);if(i.length>0)return i}const Lw=[zn.RECITATION,zn.SAFETY,zn.LANGUAGE];function Js(e){return!!e.finishReason&&Lw.includes(e.finishReason)}function Tt(e){var t,r,n;let s="";if((!e.candidates||e.candidates.length===0)&&e.promptFeedback)s+="Response was blocked",!((t=e.promptFeedback)===null||t===void 0)&&t.blockReason&&(s+=` due to ${e.promptFeedback.blockReason}`),!((r=e.promptFeedback)===null||r===void 0)&&r.blockReasonMessage&&(s+=`: ${e.promptFeedback.blockReasonMessage}`);else if(!((n=e.candidates)===null||n===void 0)&&n[0]){const i=e.candidates[0];Js(i)&&(s+=`Candidate was blocked due to ${i.finishReason}`,i.finishMessage&&(s+=`: ${i.finishMessage}`))}return s}function os(e){return this instanceof os?(this.v=e,this):new os(e)}function Mw(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(e,t||[]),s,i=[];return s={},o("next"),o("throw"),o("return"),s[Symbol.asyncIterator]=function(){return this},s;function o(m){n[m]&&(s[m]=function(y){return new Promise(function(w,x){i.push([m,y,w,x])>1||a(m,y)})})}function a(m,y){try{u(n[m](y))}catch(w){p(i[0][3],w)}}function u(m){m.value instanceof os?Promise.resolve(m.value.v).then(c,d):p(i[0][2],m)}function c(m){a("next",m)}function d(m){a("throw",m)}function p(m,y){m(y),i.shift(),i.length&&a(i[0][0],i[0][1])}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Oc=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function $w(e){const t=e.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0})),r=zw(t),[n,s]=r.tee();return{stream:Fw(n),response:Uw(s)}}async function Uw(e){const t=[],r=e.getReader();for(;;){const{done:n,value:s}=await r.read();if(n)return Ll(Hw(t));t.push(s)}}function Fw(e){return Mw(this,arguments,function*(){const r=e.getReader();for(;;){const{value:n,done:s}=yield os(r.read());if(s)break;yield yield os(Ll(n))}})}function zw(e){const t=e.getReader();return new ReadableStream({start(n){let s="";return i();function i(){return t.read().then(({value:o,done:a})=>{if(a){if(s.trim()){n.error(new ye("Failed to parse stream"));return}n.close();return}s+=o;let u=s.match(Oc),c;for(;u;){try{c=JSON.parse(u[1])}catch{n.error(new ye(`Error parsing JSON response: "${u[1]}"`));return}n.enqueue(c),s=s.substring(u[0].length),u=s.match(Oc)}return i()}).catch(o=>{let a=o;throw a.stack=o.stack,a.name==="AbortError"?a=new Df("Request aborted when reading from the stream"):a=new ye("Error reading from the stream"),a})}}})}function Hw(e){const t=e[e.length-1],r={promptFeedback:t==null?void 0:t.promptFeedback};for(const n of e){if(n.candidates){let s=0;for(const i of n.candidates)if(r.candidates||(r.candidates=[]),r.candidates[s]||(r.candidates[s]={index:s}),r.candidates[s].citationMetadata=i.citationMetadata,r.candidates[s].groundingMetadata=i.groundingMetadata,r.candidates[s].finishReason=i.finishReason,r.candidates[s].finishMessage=i.finishMessage,r.candidates[s].safetyRatings=i.safetyRatings,i.content&&i.content.parts){r.candidates[s].content||(r.candidates[s].content={role:i.content.role||"user",parts:[]});const o={};for(const a of i.content.parts)a.text&&(o.text=a.text),a.functionCall&&(o.functionCall=a.functionCall),a.executableCode&&(o.executableCode=a.executableCode),a.codeExecutionResult&&(o.codeExecutionResult=a.codeExecutionResult),Object.keys(o).length===0&&(o.text=""),r.candidates[s].content.parts.push(o)}s++}n.usageMetadata&&(r.usageMetadata=n.usageMetadata)}return r}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Lf(e,t,r,n){const s=await ms(t,wr.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(r),n);return $w(s)}async function Mf(e,t,r,n){const i=await(await ms(t,wr.GENERATE_CONTENT,e,!1,JSON.stringify(r),n)).json();return{response:Ll(i)}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function $f(e){if(e!=null){if(typeof e=="string")return{role:"system",parts:[{text:e}]};if(e.text)return{role:"system",parts:[e]};if(e.parts)return e.role?e:{role:"system",parts:e.parts}}}function as(e){let t=[];if(typeof e=="string")t=[{text:e}];else for(const r of e)typeof r=="string"?t.push({text:r}):t.push(r);return Bw(t)}function Bw(e){const t={role:"user",parts:[]},r={role:"function",parts:[]};let n=!1,s=!1;for(const i of e)"functionResponse"in i?(r.parts.push(i),s=!0):(t.parts.push(i),n=!0);if(n&&s)throw new ye("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!n&&!s)throw new ye("No content is provided for sending chat message.");return n?t:r}function Ww(e,t){var r;let n={model:t==null?void 0:t.model,generationConfig:t==null?void 0:t.generationConfig,safetySettings:t==null?void 0:t.safetySettings,tools:t==null?void 0:t.tools,toolConfig:t==null?void 0:t.toolConfig,systemInstruction:t==null?void 0:t.systemInstruction,cachedContent:(r=t==null?void 0:t.cachedContent)===null||r===void 0?void 0:r.name,contents:[]};const s=e.generateContentRequest!=null;if(e.contents){if(s)throw new Gt("CountTokensRequest must have one of contents or generateContentRequest, not both.");n.contents=e.contents}else if(s)n=Object.assign(Object.assign({},n),e.generateContentRequest);else{const i=as(e);n.contents=[i]}return{generateContentRequest:n}}function Pc(e){let t;return e.contents?t=e:t={contents:[as(e)]},e.systemInstruction&&(t.systemInstruction=$f(e.systemInstruction)),t}function Gw(e){return typeof e=="string"||Array.isArray(e)?{content:as(e)}:e}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Rc=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],Vw={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function qw(e){let t=!1;for(const r of e){const{role:n,parts:s}=r;if(!t&&n!=="user")throw new ye(`First content should be with role 'user', got ${n}`);if(!_c.includes(n))throw new ye(`Each item should include role field. Got ${n} but valid roles are: ${JSON.stringify(_c)}`);if(!Array.isArray(s))throw new ye("Content should have 'parts' property with an array of Parts");if(s.length===0)throw new ye("Each Content should have at least one part");const i={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(const a of s)for(const u of Rc)u in a&&(i[u]+=1);const o=Vw[n];for(const a of Rc)if(!o.includes(a)&&i[a]>0)throw new ye(`Content with role '${n}' can't contain '${a}' part`);t=!0}}function Ic(e){var t;if(e.candidates===void 0||e.candidates.length===0)return!1;const r=(t=e.candidates[0])===null||t===void 0?void 0:t.content;if(r===void 0||r.parts===void 0||r.parts.length===0)return!1;for(const n of r.parts)if(n===void 0||Object.keys(n).length===0||n.text!==void 0&&n.text==="")return!1;return!0}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ac="SILENT_ERROR";class Yw{constructor(t,r,n,s={}){this.model=r,this.params=n,this._requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=t,n!=null&&n.history&&(qw(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(t,r={}){var n,s,i,o,a,u;await this._sendPromise;const c=as(t),d={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(s=this.params)===null||s===void 0?void 0:s.generationConfig,tools:(i=this.params)===null||i===void 0?void 0:i.tools,toolConfig:(o=this.params)===null||o===void 0?void 0:o.toolConfig,systemInstruction:(a=this.params)===null||a===void 0?void 0:a.systemInstruction,cachedContent:(u=this.params)===null||u===void 0?void 0:u.cachedContent,contents:[...this._history,c]},p=Object.assign(Object.assign({},this._requestOptions),r);let m;return this._sendPromise=this._sendPromise.then(()=>Mf(this._apiKey,this.model,d,p)).then(y=>{var w;if(Ic(y.response)){this._history.push(c);const x=Object.assign({parts:[],role:"model"},(w=y.response.candidates)===null||w===void 0?void 0:w[0].content);this._history.push(x)}else{const x=Tt(y.response);x&&console.warn(`sendMessage() was unsuccessful. ${x}. Inspect response object for details.`)}m=y}).catch(y=>{throw this._sendPromise=Promise.resolve(),y}),await this._sendPromise,m}async sendMessageStream(t,r={}){var n,s,i,o,a,u;await this._sendPromise;const c=as(t),d={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(s=this.params)===null||s===void 0?void 0:s.generationConfig,tools:(i=this.params)===null||i===void 0?void 0:i.tools,toolConfig:(o=this.params)===null||o===void 0?void 0:o.toolConfig,systemInstruction:(a=this.params)===null||a===void 0?void 0:a.systemInstruction,cachedContent:(u=this.params)===null||u===void 0?void 0:u.cachedContent,contents:[...this._history,c]},p=Object.assign(Object.assign({},this._requestOptions),r),m=Lf(this._apiKey,this.model,d,p);return this._sendPromise=this._sendPromise.then(()=>m).catch(y=>{throw new Error(Ac)}).then(y=>y.response).then(y=>{if(Ic(y)){this._history.push(c);const w=Object.assign({},y.candidates[0].content);w.role||(w.role="model"),this._history.push(w)}else{const w=Tt(y);w&&console.warn(`sendMessageStream() was unsuccessful. ${w}. Inspect response object for details.`)}}).catch(y=>{y.message!==Ac&&console.error(y)}),m}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Kw(e,t,r,n){return(await ms(t,wr.COUNT_TOKENS,e,!1,JSON.stringify(r),n)).json()}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Jw(e,t,r,n){return(await ms(t,wr.EMBED_CONTENT,e,!1,JSON.stringify(r),n)).json()}async function Qw(e,t,r,n){const s=r.requests.map(o=>Object.assign(Object.assign({},o),{model:t}));return(await ms(t,wr.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:s}),n)).json()}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Dc{constructor(t,r,n={}){this.apiKey=t,this._requestOptions=n,r.model.includes("/")?this.model=r.model:this.model=`models/${r.model}`,this.generationConfig=r.generationConfig||{},this.safetySettings=r.safetySettings||[],this.tools=r.tools,this.toolConfig=r.toolConfig,this.systemInstruction=$f(r.systemInstruction),this.cachedContent=r.cachedContent}async generateContent(t,r={}){var n;const s=Pc(t),i=Object.assign(Object.assign({},this._requestOptions),r);return Mf(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:(n=this.cachedContent)===null||n===void 0?void 0:n.name},s),i)}async generateContentStream(t,r={}){var n;const s=Pc(t),i=Object.assign(Object.assign({},this._requestOptions),r);return Lf(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:(n=this.cachedContent)===null||n===void 0?void 0:n.name},s),i)}startChat(t){var r;return new Yw(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:(r=this.cachedContent)===null||r===void 0?void 0:r.name},t),this._requestOptions)}async countTokens(t,r={}){const n=Ww(t,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),s=Object.assign(Object.assign({},this._requestOptions),r);return Kw(this.apiKey,this.model,n,s)}async embedContent(t,r={}){const n=Gw(t),s=Object.assign(Object.assign({},this._requestOptions),r);return Jw(this.apiKey,this.model,n,s)}async batchEmbedContents(t,r={}){const n=Object.assign(Object.assign({},this._requestOptions),r);return Qw(this.apiKey,this.model,t,n)}}/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Xw{constructor(t){this.apiKey=t}getGenerativeModel(t,r){if(!t.model)throw new ye("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new Dc(this.apiKey,t,r)}getGenerativeModelFromCachedContent(t,r,n){if(!t.name)throw new Gt("Cached content must contain a `name` field.");if(!t.model)throw new Gt("Cached content must contain a `model` field.");const s=["model","systemInstruction"];for(const o of s)if(r!=null&&r[o]&&t[o]&&(r==null?void 0:r[o])!==t[o]){if(o==="model"){const a=r.model.startsWith("models/")?r.model.replace("models/",""):r.model,u=t.model.startsWith("models/")?t.model.replace("models/",""):t.model;if(a===u)continue}throw new Gt(`Different value for "${o}" specified in modelParams (${r[o]}) and cachedContent (${t[o]})`)}const i=Object.assign(Object.assign({},r),{model:t.model,tools:t.tools,toolConfig:t.toolConfig,systemInstruction:t.systemInstruction,cachedContent:t});return new Dc(this.apiKey,i,n)}}const Zw="AIzaSyAQ4RLJw54mTJAJfYQ6YSziZ_rlIqL8M_E",ex=new Xw(Zw);class tx{constructor(){this.model=ex.getGenerativeModel({model:"gemini-1.5-flash"})}async generateTaskSuggestions(t){try{const r=`
        As an AI assistant for a task management platform, help generate task suggestions based on the following context:
        
        Context: ${t}
        
        Please provide 3-5 actionable task suggestions that would help the user achieve their goal. 
        Format your response as a JSON array of objects with the following structure:
        [
          {
            "title": "Task title",
            "description": "Detailed description",
            "priority": "high|medium|low",
            "category": "feature|improvement|research|documentation|testing|other",
            "estimatedHours": number
          }
        ]
        
        Make sure the suggestions are practical, specific, and actionable.
      `,o=(await(await this.model.generateContent(r)).response).text().match(/\[[\s\S]*\]/);return o?JSON.parse(o[0]):[]}catch(r){return console.error("Error generating task suggestions:",r),[]}}async improveTaskDescription(t,r){try{const n=`
        As an AI assistant, help improve this task description to make it more clear, actionable, and comprehensive:
        
        Title: ${t}
        Current Description: ${r||"No description provided"}
        
        Please provide an improved description that includes:
        - Clear objective
        - Specific steps or approach
        - Success criteria
        - Any relevant considerations
        
        Keep it concise but comprehensive. Respond with just the improved description text.
      `;return(await(await this.model.generateContent(n)).response).text().trim()}catch(n){return console.error("Error improving task description:",n),r}}async generateProcessSteps(t,r){try{const n=`
        Break down this task into specific, actionable process steps:
        
        Task: ${t}
        Description: ${r}
        
        Provide 3-7 process steps that would help complete this task effectively.
        Format your response as a JSON array of objects:
        [
          {
            "title": "Step title",
            "description": "Detailed step description"
          }
        ]
        
        Make each step specific, actionable, and in logical order.
      `,a=(await(await this.model.generateContent(n)).response).text().match(/\[[\s\S]*\]/);return a?JSON.parse(a[0]):[]}catch(n){return console.error("Error generating process steps:",n),[]}}async generateFollowUpSuggestions(t,r,n){try{const s=`
        Suggest relevant follow-up actions for this task:
        
        Task: ${t}
        Description: ${r}
        Current Status: ${n}
        
        Provide 2-4 follow-up suggestions that would help move this task forward or ensure its success.
        Format as a JSON array:
        [
          {
            "description": "Follow-up action description",
            "dueDate": "suggested due date (YYYY-MM-DD format, relative to today)"
          }
        ]
        
        Consider the current status and suggest appropriate next steps.
      `,u=(await(await this.model.generateContent(s)).response).text().match(/\[[\s\S]*\]/);return u?JSON.parse(u[0]):[]}catch(s){return console.error("Error generating follow-up suggestions:",s),[]}}async chatAssistant(t,r={}){try{const{tasks:n=[],currentTask:s=null}=r,i=`
        You are an AI assistant for a task management platform called "Idea Manager". 
        Help the user with their task management needs.
        
        Current context:
        - Total tasks: ${n.length}
        - Current task being viewed: ${s?`${s.title} (${s.status})`:"None"}
        
        User message: ${t}
        
        Provide helpful, actionable advice related to task management, productivity, or the specific question asked.
        Keep responses concise but informative. If the user is asking about specific features, explain how they can use the platform effectively.
      `;return(await(await this.model.generateContent(i)).response).text().trim()}catch(n){return console.error("Error in chat assistant:",n),"I'm sorry, I'm having trouble processing your request right now. Please try again."}}async analyzeProductivity(t){try{const r=t.map(u=>({status:u.status,priority:u.priority,category:u.category,createdAt:u.createdAt,updatedAt:u.updatedAt})),n=`
        Analyze this user's productivity and task management patterns:
        
        Tasks data: ${JSON.stringify(r)}
        
        Provide insights about:
        1. Productivity patterns
        2. Areas for improvement
        3. Specific recommendations
        
        Format as a JSON object:
        {
          "insights": ["insight 1", "insight 2", ...],
          "recommendations": ["recommendation 1", "recommendation 2", ...],
          "score": productivity_score_out_of_100
        }
      `,a=(await(await this.model.generateContent(n)).response).text().match(/\{[\s\S]*\}/);return a?JSON.parse(a[0]):{insights:["Unable to analyze productivity at this time"],recommendations:["Continue tracking your tasks for better insights"],score:75}}catch(r){return console.error("Error analyzing productivity:",r),{insights:["Unable to analyze productivity at this time"],recommendations:["Continue tracking your tasks for better insights"],score:75}}}}const Br=new tx,rx=({task:e,onSave:t,onClose:r})=>{const[n,s]=I.useState({title:"",description:"",status:P.IDEA,priority:Ie.MEDIUM,category:me.OTHER,tags:[],startDate:"",dueDate:"",estimatedHours:0,processSteps:[],followUps:[]}),[i,o]=I.useState(""),[a,u]=I.useState({title:"",description:""}),[c,d]=I.useState({description:"",dueDate:""}),[p,m]=I.useState(!1),[y,w]=I.useState(null);I.useEffect(()=>{e&&s({title:e.title||"",description:e.description||"",status:e.status||P.IDEA,priority:e.priority||Ie.MEDIUM,category:e.category||me.OTHER,tags:e.tags||[],startDate:e.startDate?e.startDate.split("T")[0]:"",dueDate:e.dueDate?e.dueDate.split("T")[0]:"",estimatedHours:e.estimatedHours||0,processSteps:e.processSteps||[],followUps:e.followUps||[]})},[e]);const x=v=>{if(v.preventDefault(),!n.title.trim())return;const j={...n,startDate:n.startDate?new Date(n.startDate).toISOString():null,dueDate:n.dueDate?new Date(n.dueDate).toISOString():null};t(j)},k=()=>{i.trim()&&!n.tags.includes(i.trim())&&(s(v=>({...v,tags:[...v.tags,i.trim()]})),o(""))},f=v=>{s(j=>({...j,tags:j.tags.filter(C=>C!==v)}))},h=()=>{a.title.trim()&&(s(v=>({...v,processSteps:[...v.processSteps,{id:Date.now().toString(),title:a.title,description:a.description,completed:!1,order:v.processSteps.length}]})),u({title:"",description:""}))},g=v=>{s(j=>({...j,processSteps:j.processSteps.filter(C=>C.id!==v)}))},_=()=>{c.description.trim()&&(s(v=>({...v,followUps:[...v.followUps,{id:Date.now().toString(),description:c.description,dueDate:c.dueDate?new Date(c.dueDate).toISOString():null,completed:!1}]})),d({description:"",dueDate:""}))},S=v=>{s(j=>({...j,followUps:j.followUps.filter(C=>C.id!==v)}))},b=async()=>{if(!n.title.trim()){alert("Please enter a title first");return}m(!0),w("description");try{const v=await Br.improveTaskDescription(n.title,n.description);s(j=>({...j,description:v}))}catch(v){console.error("Error improving description:",v),alert("Failed to improve description. Please try again.")}finally{m(!1),w(null)}},E=async()=>{if(!n.title.trim()){alert("Please enter a title first");return}m(!0),w("steps");try{const j=(await Br.generateProcessSteps(n.title,n.description)).map((C,X)=>({id:Date.now().toString()+X,title:C.title,description:C.description,completed:!1,order:n.processSteps.length+X}));s(C=>({...C,processSteps:[...C.processSteps,...j]}))}catch(v){console.error("Error generating steps:",v),alert("Failed to generate steps. Please try again.")}finally{m(!1),w(null)}};return l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[l.jsxs("div",{className:"sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between",children:[l.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:e?"Edit Task":"Create New Task"}),l.jsx("button",{onClick:r,className:"p-2 hover:bg-gray-100 rounded-full",children:l.jsx(vr,{className:"w-5 h-5"})})]}),l.jsxs("form",{onSubmit:x,className:"p-6 space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[l.jsxs("div",{className:"md:col-span-2",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),l.jsx("input",{type:"text",required:!0,className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.title,onChange:v=>s(j=>({...j,title:v.target.value})),placeholder:"Enter task title..."})]}),l.jsxs("div",{className:"md:col-span-2",children:[l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),l.jsxs("button",{type:"button",onClick:b,disabled:p||!n.title.trim(),className:"flex items-center space-x-1 text-sm text-primary-600 hover:text-primary-700 disabled:text-gray-400 disabled:cursor-not-allowed",children:[p&&y==="description"?l.jsx(sn,{className:"w-4 h-4 animate-spin"}):l.jsx(ef,{className:"w-4 h-4"}),l.jsx("span",{children:"AI Enhance"})]})]}),l.jsx("textarea",{rows:3,className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.description,onChange:v=>s(j=>({...j,description:v.target.value})),placeholder:"Describe your idea or task..."})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),l.jsxs("select",{className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.status,onChange:v=>s(j=>({...j,status:v.target.value})),children:[l.jsx("option",{value:P.IDEA,children:"Idea"}),l.jsx("option",{value:P.PLANNING,children:"Planning"}),l.jsx("option",{value:P.IN_PROGRESS,children:"In Progress"}),l.jsx("option",{value:P.REVIEW,children:"Review"}),l.jsx("option",{value:P.COMPLETED,children:"Completed"}),l.jsx("option",{value:P.ON_HOLD,children:"On Hold"}),l.jsx("option",{value:P.CANCELLED,children:"Cancelled"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),l.jsxs("select",{className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.priority,onChange:v=>s(j=>({...j,priority:v.target.value})),children:[l.jsx("option",{value:Ie.LOW,children:"Low"}),l.jsx("option",{value:Ie.MEDIUM,children:"Medium"}),l.jsx("option",{value:Ie.HIGH,children:"High"}),l.jsx("option",{value:Ie.URGENT,children:"Urgent"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),l.jsxs("select",{className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.category,onChange:v=>s(j=>({...j,category:v.target.value})),children:[l.jsx("option",{value:me.FEATURE,children:"Feature"}),l.jsx("option",{value:me.BUG_FIX,children:"Bug Fix"}),l.jsx("option",{value:me.IMPROVEMENT,children:"Improvement"}),l.jsx("option",{value:me.RESEARCH,children:"Research"}),l.jsx("option",{value:me.DOCUMENTATION,children:"Documentation"}),l.jsx("option",{value:me.TESTING,children:"Testing"}),l.jsx("option",{value:me.OTHER,children:"Other"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Estimated Hours"}),l.jsx("input",{type:"number",min:"0",step:"0.5",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.estimatedHours,onChange:v=>s(j=>({...j,estimatedHours:parseFloat(v.target.value)||0}))})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),l.jsx("input",{type:"date",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.startDate,onChange:v=>s(j=>({...j,startDate:v.target.value}))})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Due Date"}),l.jsx("input",{type:"date",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:n.dueDate,onChange:v=>s(j=>({...j,dueDate:v.target.value}))})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tags"}),l.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:n.tags.map((v,j)=>l.jsxs("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded-full text-sm flex items-center space-x-1",children:[l.jsx("span",{children:v}),l.jsx("button",{type:"button",onClick:()=>f(v),className:"hover:bg-primary-200 rounded-full p-0.5",children:l.jsx(vr,{className:"w-3 h-3"})})]},j))}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("input",{type:"text",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:i,onChange:v=>o(v.target.value),placeholder:"Add a tag...",onKeyPress:v=>v.key==="Enter"&&(v.preventDefault(),k())}),l.jsx("button",{type:"button",onClick:k,className:"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700",children:l.jsx(Wt,{className:"w-4 h-4"})})]})]}),l.jsxs("div",{children:[l.jsxs("div",{className:"flex items-center justify-between mb-2",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Process Steps"}),l.jsxs("button",{type:"button",onClick:E,disabled:p||!n.title.trim(),className:"flex items-center space-x-1 text-sm text-primary-600 hover:text-primary-700 disabled:text-gray-400 disabled:cursor-not-allowed",children:[p&&y==="steps"?l.jsx(sn,{className:"w-4 h-4 animate-spin"}):l.jsx(Bt,{className:"w-4 h-4"}),l.jsx("span",{children:"AI Generate"})]})]}),l.jsx("div",{className:"space-y-2 mb-4",children:n.processSteps.map((v,j)=>l.jsxs("div",{className:"flex items-start space-x-2 p-3 bg-gray-50 rounded-lg",children:[l.jsxs("div",{className:"flex-1",children:[l.jsx("p",{className:"font-medium text-gray-900",children:v.title}),v.description&&l.jsx("p",{className:"text-sm text-gray-600 mt-1",children:v.description})]}),l.jsx("button",{type:"button",onClick:()=>g(v.id),className:"text-red-600 hover:bg-red-100 p-1 rounded",children:l.jsx(ja,{className:"w-4 h-4"})})]},v.id))}),l.jsxs("div",{className:"space-y-2",children:[l.jsx("input",{type:"text",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:a.title,onChange:v=>u(j=>({...j,title:v.target.value})),placeholder:"Step title..."}),l.jsx("input",{type:"text",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:a.description,onChange:v=>u(j=>({...j,description:v.target.value})),placeholder:"Step description (optional)..."}),l.jsxs("button",{type:"button",onClick:h,className:"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2",children:[l.jsx(Wt,{className:"w-4 h-4"}),l.jsx("span",{children:"Add Step"})]})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Follow-ups"}),l.jsx("div",{className:"space-y-2 mb-4",children:n.followUps.map((v,j)=>l.jsxs("div",{className:"flex items-start space-x-2 p-3 bg-gray-50 rounded-lg",children:[l.jsxs("div",{className:"flex-1",children:[l.jsx("p",{className:"text-gray-900",children:v.description}),v.dueDate&&l.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["Due: ",new Date(v.dueDate).toLocaleDateString()]})]}),l.jsx("button",{type:"button",onClick:()=>S(v.id),className:"text-red-600 hover:bg-red-100 p-1 rounded",children:l.jsx(ja,{className:"w-4 h-4"})})]},v.id))}),l.jsxs("div",{className:"space-y-2",children:[l.jsx("input",{type:"text",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:c.description,onChange:v=>d(j=>({...j,description:v.target.value})),placeholder:"Follow-up description..."}),l.jsx("input",{type:"date",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent",value:c.dueDate,onChange:v=>d(j=>({...j,dueDate:v.target.value}))}),l.jsxs("button",{type:"button",onClick:_,className:"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2",children:[l.jsx(Wt,{className:"w-4 h-4"}),l.jsx("span",{children:"Add Follow-up"})]})]})]}),l.jsxs("div",{className:"flex justify-end space-x-4 pt-6 border-t border-gray-200",children:[l.jsx("button",{type:"button",onClick:r,className:"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:"Cancel"}),l.jsx("button",{type:"submit",className:"px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700",children:e?"Update Task":"Create Task"})]})]})]})})},nx=({onClose:e,onCreateFirstTask:t})=>{const[r,n]=I.useState(0),s=[{title:"Welcome to Idea Manager! 💡",description:"Transform your ideas into reality with our structured workflow",icon:Bt,content:l.jsxs("div",{className:"space-y-4",children:[l.jsx("p",{className:"text-gray-600",children:"Idea Manager helps you capture, develop, and execute your ideas through a clear, step-by-step process. Let's walk through how it works!"}),l.jsxs("div",{className:"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4",children:[l.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"What you'll learn:"}),l.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[l.jsx("li",{children:"• How the workflow stages work"}),l.jsx("li",{children:"• Best practices for managing ideas"}),l.jsx("li",{children:"• Tips for staying organized"})]})]})]})},{title:"The Innovation Pipeline",description:"Ideas flow through 5 clear stages",icon:_i,content:l.jsx("div",{className:"space-y-4",children:l.jsxs("div",{className:"grid grid-cols-1 gap-3",children:[l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg",children:[l.jsx(Bt,{className:"w-5 h-5 text-purple-600"}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900",children:"Ideas"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Capture raw concepts and inspirations"})]})]}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-blue-50 rounded-lg",children:[l.jsx(Xh,{className:"w-5 h-5 text-blue-600"}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900",children:"Planning"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Define scope, steps, and requirements"})]})]}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-orange-50 rounded-lg",children:[l.jsx(tf,{className:"w-5 h-5 text-orange-600"}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900",children:"In Progress"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Actively working on implementation"})]})]}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-amber-50 rounded-lg",children:[l.jsx(hr,{className:"w-5 h-5 text-amber-600"}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900",children:"Review"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Testing and validation"})]})]}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-green-50 rounded-lg",children:[l.jsx(hr,{className:"w-5 h-5 text-green-600"}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900",children:"Completed"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Successfully finished"})]})]})]})})},{title:"Key Features",description:"Tools to keep you organized and productive",icon:hr,content:l.jsx("div",{className:"space-y-4",children:l.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[l.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"📝 Process Steps"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Break down complex ideas into manageable steps with progress tracking"})]}),l.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"🔔 Follow-ups"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Set reminders and track actions with due dates"})]}),l.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"🏷️ Smart Organization"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Use categories, priorities, and tags to stay organized"})]}),l.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"📊 Progress Tracking"}),l.jsx("p",{className:"text-sm text-gray-600",children:"Visual dashboards and progress bars keep you motivated"})]})]})})},{title:"Ready to Start? 🚀",description:"Create your first idea and begin your journey",icon:Cg,content:l.jsxs("div",{className:"space-y-4",children:[l.jsx("p",{className:"text-gray-600",children:"You're all set! Click below to create your first idea and start transforming concepts into reality."}),l.jsxs("div",{className:"bg-gradient-to-r from-primary-50 to-purple-50 rounded-lg p-4",children:[l.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Pro Tips:"}),l.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[l.jsx("li",{children:"• Start simple - you can always add details later"}),l.jsx("li",{children:"• Use the dashboard view to see your progress at a glance"}),l.jsx("li",{children:"• Set due dates to keep yourself accountable"}),l.jsx("li",{children:"• Break big ideas into smaller, actionable steps"})]})]}),l.jsxs("button",{onClick:t,className:"w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center space-x-2 transition-all duration-200",children:[l.jsx(Bt,{className:"w-5 h-5"}),l.jsx("span",{children:"Create My First Idea"})]})]})}],i=s[r],o=i.icon;return l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-xl max-w-lg w-full max-h-[90vh] overflow-hidden",children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"bg-gradient-to-r from-primary-500 to-purple-600 rounded-lg p-2",children:l.jsx(o,{className:"w-5 h-5 text-white"})}),l.jsxs("div",{children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:i.title}),l.jsx("p",{className:"text-sm text-gray-600",children:i.description})]})]}),l.jsx("button",{onClick:e,className:"p-1 rounded-lg hover:bg-gray-100 transition-colors",children:l.jsx(vr,{className:"w-5 h-5 text-gray-400"})})]}),l.jsx("div",{className:"p-6 overflow-y-auto",children:i.content}),l.jsxs("div",{className:"flex items-center justify-between p-6 border-t border-gray-200",children:[l.jsx("div",{className:"flex space-x-2",children:s.map((a,u)=>l.jsx("div",{className:`w-2 h-2 rounded-full transition-colors ${u===r?"bg-primary-600":"bg-gray-300"}`},u))}),l.jsxs("div",{className:"flex items-center space-x-3",children:[r>0&&l.jsx("button",{onClick:()=>n(r-1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 font-medium",children:"Back"}),r<s.length-1?l.jsxs("button",{onClick:()=>n(r+1),className:"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium flex items-center space-x-2",children:[l.jsx("span",{children:"Next"}),l.jsx(_i,{className:"w-4 h-4"})]}):l.jsx("button",{onClick:e,className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium",children:"Get Started"})]})]})]})})},sx=({tasks:e,currentTask:t,onTaskSuggestions:r,onImproveDescription:n,onGenerateSteps:s})=>{const[i,o]=I.useState(!1),[a,u]=I.useState([{id:1,type:"ai",content:"Hi! I'm your AI assistant. I can help you with task management, generate ideas, improve descriptions, and provide productivity insights. How can I help you today?",timestamp:new Date}]),[c,d]=I.useState(""),[p,m]=I.useState(!1),[y,w]=I.useState(null),x=I.useRef(null),k=()=>{var v;(v=x.current)==null||v.scrollIntoView({behavior:"smooth"})};I.useEffect(()=>{k()},[a]);const f=(v,j)=>{const C={id:Date.now(),type:v,content:j,timestamp:new Date};u(X=>[...X,C])},h=async()=>{if(!c.trim()||p)return;const v=c.trim();d(""),f("user",v),m(!0);try{const j=await Br.chatAssistant(v,{tasks:e,currentTask:t});f("ai",j)}catch{f("ai","I'm sorry, I encountered an error. Please try again.")}finally{m(!1)}},g=v=>{v.key==="Enter"&&!v.shiftKey&&(v.preventDefault(),h())},E=[{icon:Bt,label:"Task Suggestions",action:async()=>{w("suggestions"),m(!0),f("user","Generate task suggestions for my current project");try{const v=`Current tasks: ${e.length} total. Recent tasks: ${e.slice(0,3).map(C=>C.title).join(", ")}`,j=await Br.generateTaskSuggestions(v);j.length>0?(f("ai",`I've generated ${j.length} task suggestions for you:`),j.forEach((C,X)=>{f("ai",`${X+1}. **${C.title}**
${C.description}
*Priority: ${C.priority}, Category: ${C.category}*`)}),r==null||r(j)):f("ai","I couldn't generate specific suggestions right now. Try describing your project goals for better recommendations.")}catch{f("ai","I had trouble generating suggestions. Please try again.")}finally{m(!1),w(null)}},feature:"suggestions"},{icon:ef,label:"Improve Task",action:async()=>{if(!t){f("ai","Please select a task first, then I can help improve its description.");return}w("improve"),m(!0),f("user",`Improve description for: ${t.title}`);try{const v=await Br.improveTaskDescription(t.title,t.description);f("ai",`Here's an improved description for "${t.title}":

${v}`),n==null||n(t.id,v)}catch{f("ai","I had trouble improving the description. Please try again.")}finally{m(!1),w(null)}},feature:"improve",disabled:!t},{icon:Zh,label:"Productivity Analysis",action:async()=>{if(e.length===0){f("ai","You need some tasks first before I can analyze your productivity patterns.");return}w("analysis"),m(!0),f("user","Analyze my productivity patterns");try{const v=await Br.analyzeProductivity(e);f("ai",`**Productivity Analysis (Score: ${v.score}/100)**

**Insights:**
${v.insights.map(j=>`• ${j}`).join(`
`)}

**Recommendations:**
${v.recommendations.map(j=>`• ${j}`).join(`
`)}`)}catch{f("ai","I had trouble analyzing your productivity. Please try again.")}finally{m(!1),w(null)}},feature:"analysis"}];return i?l.jsxs("div",{className:"fixed bottom-6 right-6 w-96 h-[600px] bg-white rounded-xl shadow-2xl border border-gray-200 flex flex-col z-50",children:[l.jsxs("div",{className:"bg-gradient-to-r from-primary-600 to-purple-600 text-white p-4 rounded-t-xl flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(Vu,{className:"w-5 h-5"}),l.jsx("h3",{className:"font-semibold",children:"AI Assistant"})]}),l.jsx("button",{onClick:()=>o(!1),className:"text-white hover:text-gray-200 transition-colors",children:l.jsx(vr,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"p-3 border-b border-gray-200",children:l.jsx("div",{className:"grid grid-cols-3 gap-2",children:E.map(v=>{const j=v.icon;return l.jsxs("button",{onClick:v.action,disabled:v.disabled||p,className:`p-2 rounded-lg text-xs font-medium transition-all duration-200 ${v.disabled?"bg-gray-100 text-gray-400 cursor-not-allowed":y===v.feature?"bg-primary-100 text-primary-700 border border-primary-200":"bg-gray-50 text-gray-700 hover:bg-gray-100"}`,children:[l.jsx(j,{className:"w-4 h-4 mx-auto mb-1"}),l.jsx("div",{children:v.label})]},v.feature)})})}),l.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[a.map(v=>l.jsx("div",{className:`flex ${v.type==="user"?"justify-end":"justify-start"}`,children:l.jsxs("div",{className:`max-w-[80%] p-3 rounded-lg ${v.type==="user"?"bg-primary-600 text-white":"bg-gray-100 text-gray-900"}`,children:[l.jsx("div",{className:"whitespace-pre-wrap text-sm",children:v.content.split("**").map((j,C)=>C%2===1?l.jsx("strong",{children:j},C):j)}),l.jsx("div",{className:`text-xs mt-1 ${v.type==="user"?"text-primary-100":"text-gray-500"}`,children:v.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},v.id)),p&&l.jsx("div",{className:"flex justify-start",children:l.jsx("div",{className:"bg-gray-100 text-gray-900 p-3 rounded-lg",children:l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(sn,{className:"w-4 h-4 animate-spin"}),l.jsx("span",{className:"text-sm",children:"Thinking..."})]})})}),l.jsx("div",{ref:x})]}),l.jsx("div",{className:"p-4 border-t border-gray-200",children:l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("textarea",{value:c,onChange:v=>d(v.target.value),onKeyPress:g,placeholder:"Ask me anything about task management...",className:"flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent",rows:"2",disabled:p}),l.jsx("button",{onClick:h,disabled:!c.trim()||p,className:"bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors",children:l.jsx(Tg,{className:"w-4 h-4"})})]})})]}):l.jsx("button",{onClick:()=>o(!0),className:"fixed bottom-6 right-6 bg-gradient-to-r from-primary-600 to-purple-600 hover:from-primary-700 hover:to-purple-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50",title:"AI Assistant",children:l.jsx(Vu,{className:"w-6 h-6"})})},ix=({isOpen:e,onClose:t,initialMode:r="signin"})=>{const[n,s]=I.useState(r),[i,o]=I.useState(!1),[a,u]=I.useState(!1),[c,d]=I.useState(""),[p,m]=I.useState(""),[y,w]=I.useState({email:"",password:"",confirmPassword:"",firstName:"",lastName:""}),{signIn:x,signUp:k,resetPassword:f}=Ki(),h=E=>{w(v=>({...v,[E.target.name]:E.target.value})),d("")},g=()=>{if(!y.email)return d("Email is required"),!1;if(n!=="forgot"&&!y.password)return d("Password is required"),!1;if(n==="signup"){if(y.password!==y.confirmPassword)return d("Passwords do not match"),!1;if(y.password.length<6)return d("Password must be at least 6 characters"),!1;if(!y.firstName)return d("First name is required"),!1}return!0},_=async E=>{if(E.preventDefault(),!!g()){u(!0),d(""),m("");try{if(n==="signin"){const{error:v}=await x(y.email,y.password);v?d(v.message):t()}else if(n==="signup"){const{error:v}=await k(y.email,y.password,{first_name:y.firstName,last_name:y.lastName,full_name:`${y.firstName} ${y.lastName}`.trim()});v?d(v.message):(m("Check your email for the confirmation link!"),s("signin"))}else if(n==="forgot"){const{error:v}=await f(y.email);v?d(v.message):(m("Check your email for the password reset link!"),s("signin"))}}catch{d("An unexpected error occurred")}finally{u(!1)}}},S=()=>{w({email:"",password:"",confirmPassword:"",firstName:"",lastName:""}),d(""),m("")},b=E=>{s(E),S()};return e?l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-xl w-full max-w-md",children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[l.jsxs("div",{children:[l.jsxs("h2",{className:"text-xl font-bold text-gray-900",children:[n==="signin"&&"Welcome Back",n==="signup"&&"Create Account",n==="forgot"&&"Reset Password"]}),l.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:[n==="signin"&&"Sign in to your account",n==="signup"&&"Start your innovation journey",n==="forgot"&&"Enter your email to reset password"]})]}),l.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:l.jsx(vr,{className:"w-5 h-5"})})]}),l.jsxs("form",{onSubmit:_,className:"p-6 space-y-4",children:[c&&l.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:c}),p&&l.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg text-sm",children:p}),n==="signup"&&l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),l.jsxs("div",{className:"relative",children:[l.jsx(Ku,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx("input",{type:"text",name:"firstName",value:y.firstName,onChange:h,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"John",required:!0})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),l.jsxs("div",{className:"relative",children:[l.jsx(Ku,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx("input",{type:"text",name:"lastName",value:y.lastName,onChange:h,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Doe"})]})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),l.jsxs("div",{className:"relative",children:[l.jsx(bg,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx("input",{type:"email",name:"email",value:y.email,onChange:h,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"<EMAIL>",required:!0})]})]}),n!=="forgot"&&l.jsxs(l.Fragment,{children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),l.jsxs("div",{className:"relative",children:[l.jsx(qu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx("input",{type:i?"text":"password",name:"password",value:y.password,onChange:h,className:"w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"••••••••",required:!0}),l.jsx("button",{type:"button",onClick:()=>o(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:i?l.jsx(xg,{className:"w-4 h-4"}):l.jsx(Jh,{className:"w-4 h-4"})})]})]}),n==="signup"&&l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),l.jsxs("div",{className:"relative",children:[l.jsx(qu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),l.jsx("input",{type:i?"text":"password",name:"confirmPassword",value:y.confirmPassword,onChange:h,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"••••••••",required:!0})]})]})]}),l.jsxs("button",{type:"submit",disabled:a,className:"w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2",children:[a&&l.jsx(sn,{className:"w-4 h-4 animate-spin"}),l.jsxs("span",{children:[n==="signin"&&"Sign In",n==="signup"&&"Create Account",n==="forgot"&&"Send Reset Link"]})]}),l.jsxs("div",{className:"text-center space-y-2",children:[n==="signin"&&l.jsxs(l.Fragment,{children:[l.jsx("button",{type:"button",onClick:()=>b("forgot"),className:"text-sm text-primary-600 hover:text-primary-700",children:"Forgot your password?"}),l.jsxs("div",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",l.jsx("button",{type:"button",onClick:()=>b("signup"),className:"text-primary-600 hover:text-primary-700 font-medium",children:"Sign up"})]})]}),n==="signup"&&l.jsxs("div",{className:"text-sm text-gray-600",children:["Already have an account?"," ",l.jsx("button",{type:"button",onClick:()=>b("signin"),className:"text-primary-600 hover:text-primary-700 font-medium",children:"Sign in"})]}),n==="forgot"&&l.jsxs("div",{className:"text-sm text-gray-600",children:["Remember your password?"," ",l.jsx("button",{type:"button",onClick:()=>b("signin"),className:"text-primary-600 hover:text-primary-700 font-medium",children:"Sign in"})]})]})]})]})}):null},ox=async e=>{try{const t=localStorage.getItem("idea-tasks");if(!t)return;const r=JSON.parse(t);if(r.length===0)return;console.log(`Migrating ${r.length} tasks from localStorage to Supabase`);for(const n of r){const{data:s,error:i}=await re.from("tasks").insert([{user_id:e,title:n.title,description:n.description||"",status:n.status,priority:n.priority,category:n.category,tags:n.tags||[],start_date:n.startDate,due_date:n.dueDate,estimated_hours:n.estimatedHours||0,actual_hours:n.actualHours||0,progress:n.progress||0}]).select().single();if(i){console.error("Error migrating task:",n.title,i);continue}if(n.followUps&&n.followUps.length>0){const o=n.followUps.map(u=>({task_id:s.id,description:u.description||u.text,due_date:u.dueDate,completed:u.completed||!1})),{error:a}=await re.from("follow_ups").insert(o);a&&console.error("Error migrating follow-ups for task:",n.title,a)}if(n.processSteps&&n.processSteps.length>0){const o=n.processSteps.map((u,c)=>({task_id:s.id,title:u.title,description:u.description||"",completed:u.completed||!1,step_order:u.order||c})),{error:a}=await re.from("process_steps").insert(o);a&&console.error("Error migrating process steps for task:",n.title,a)}}localStorage.setItem("idea-tasks-backup",t),localStorage.removeItem("idea-tasks"),console.log("Migration completed successfully")}catch(t){throw console.error("Migration failed:",t),t}},ax=()=>{const{user:e}=Ki(),[t,r]=I.useState([]),[n,s]=I.useState(!0),[i,o]=I.useState({status:"",priority:"",category:"",search:""}),a=async()=>{if(!e){console.log("No user, clearing tasks"),r([]),s(!1);return}try{console.log("Loading tasks for user:",e.id),s(!0);const{data:f,error:h}=await re.from("tasks").select(`
          *,
          follow_ups (*),
          process_steps (*),
          attachments (*)
        `).eq("user_id",e.id).order("created_at",{ascending:!1});if(h)throw console.error("Error loading tasks:",h),h;console.log("Loaded tasks:",(f==null?void 0:f.length)||0);const g=f.map(_=>{var S,b;return{id:_.id,title:_.title,description:_.description,status:_.status,priority:_.priority,category:_.category,tags:_.tags||[],startDate:_.start_date,dueDate:_.due_date,estimatedHours:_.estimated_hours,actualHours:_.actual_hours,progress:_.progress,followUps:((S=_.follow_ups)==null?void 0:S.map(E=>({id:E.id,description:E.description,dueDate:E.due_date,completed:E.completed,createdAt:E.created_at})))||[],processSteps:((b=_.process_steps)==null?void 0:b.map(E=>({id:E.id,title:E.title,description:E.description,completed:E.completed,order:E.step_order,createdAt:E.created_at})))||[],attachments:_.attachments||[],createdAt:_.created_at,updatedAt:_.updated_at}});r(g)}catch(f){console.error("Error loading tasks:",f)}finally{s(!1)}};I.useEffect(()=>{(async()=>{if(!e){console.log("No user in useEffect");return}console.log("Initializing user data for:",e.id);try{const{data:h,error:g}=await re.from("tasks").select("id").eq("user_id",e.id).limit(1);if(g){console.error("Error checking existing tasks:",g),await a();return}if(console.log("Existing tasks check:",(h==null?void 0:h.length)||0),!h||h.length===0)try{await ox(e.id)}catch(_){console.error("Migration failed:",_)}await a()}catch(h){console.error("Error in initializeUserData:",h),await a()}})()},[e]);const u=async f=>{if(!e)return null;try{const h={user_id:e.id,title:f.title,description:f.description||"",status:f.status||P.IDEA,priority:f.priority||Ie.MEDIUM,category:f.category||"other",tags:f.tags||[],start_date:f.startDate||null,due_date:f.dueDate||null,estimated_hours:f.estimatedHours||0,actual_hours:f.actualHours||0,progress:f.progress||0},{data:g,error:_}=await re.from("tasks").insert([h]).select().single();if(_)throw _;const S={id:g.id,title:g.title,description:g.description,status:g.status,priority:g.priority,category:g.category,tags:g.tags||[],startDate:g.start_date,dueDate:g.due_date,estimatedHours:g.estimated_hours,actualHours:g.actual_hours,progress:g.progress,followUps:[],processSteps:[],attachments:[],createdAt:g.created_at,updatedAt:g.updated_at};return r(b=>[S,...b]),S}catch(h){return console.error("Error creating task:",h),null}},c=async(f,h)=>{if(e)try{const g={};h.title!==void 0&&(g.title=h.title),h.description!==void 0&&(g.description=h.description),h.status!==void 0&&(g.status=h.status),h.priority!==void 0&&(g.priority=h.priority),h.category!==void 0&&(g.category=h.category),h.tags!==void 0&&(g.tags=h.tags),h.startDate!==void 0&&(g.start_date=h.startDate),h.dueDate!==void 0&&(g.due_date=h.dueDate),h.estimatedHours!==void 0&&(g.estimated_hours=h.estimatedHours),h.actualHours!==void 0&&(g.actual_hours=h.actualHours),h.progress!==void 0&&(g.progress=h.progress);const{error:_}=await re.from("tasks").update(g).eq("id",f).eq("user_id",e.id);if(_)throw _;r(S=>S.map(b=>b.id===f?{...b,...h,updatedAt:new Date().toISOString()}:b))}catch(g){console.error("Error updating task:",g)}},d=async f=>{if(e)try{const{error:h}=await re.from("tasks").delete().eq("id",f).eq("user_id",e.id);if(h)throw h;r(g=>g.filter(_=>_.id!==f))}catch(h){console.error("Error deleting task:",h)}},p=async(f,h)=>{if(e)try{const{data:g,error:_}=await re.from("follow_ups").insert([{task_id:f,description:h.description,due_date:h.dueDate,completed:!1}]).select().single();if(_)throw _;const S={id:g.id,description:g.description,dueDate:g.due_date,completed:g.completed,createdAt:g.created_at};r(b=>b.map(E=>E.id===f?{...E,followUps:[...E.followUps,S]}:E))}catch(g){console.error("Error adding follow-up:",g)}},m=async(f,h)=>{if(e)try{const g=t.find(b=>b.id===f),_=g==null?void 0:g.followUps.find(b=>b.id===h);if(!_)return;const{error:S}=await re.from("follow_ups").update({completed:!_.completed}).eq("id",h);if(S)throw S;r(b=>b.map(E=>E.id===f?{...E,followUps:E.followUps.map(v=>v.id===h?{...v,completed:!v.completed}:v)}:E))}catch(g){console.error("Error toggling follow-up:",g)}},y=async(f,h)=>{if(e)try{const g=t.find(v=>v.id===f),_=(g==null?void 0:g.processSteps.length)||0,{data:S,error:b}=await re.from("process_steps").insert([{task_id:f,title:h.title,description:h.description,completed:!1,step_order:_}]).select().single();if(b)throw b;const E={id:S.id,title:S.title,description:S.description,completed:S.completed,order:S.step_order,createdAt:S.created_at};r(v=>v.map(j=>j.id===f?{...j,processSteps:[...j.processSteps,E]}:j))}catch(g){console.error("Error adding process step:",g)}},w=async(f,h)=>{if(e)try{const g=t.find(b=>b.id===f),_=g==null?void 0:g.processSteps.find(b=>b.id===h);if(!_)return;const{error:S}=await re.from("process_steps").update({completed:!_.completed}).eq("id",h);if(S)throw S;r(b=>b.map(E=>E.id===f?{...E,processSteps:E.processSteps.map(v=>v.id===h?{...v,completed:!v.completed}:v)}:E))}catch(g){console.error("Error toggling process step:",g)}},x=()=>t.filter(f=>{const h=!i.status||f.status===i.status,g=!i.priority||f.priority===i.priority,_=!i.category||f.category===i.category,S=!i.search||f.title.toLowerCase().includes(i.search.toLowerCase())||f.description.toLowerCase().includes(i.search.toLowerCase())||f.tags.some(b=>b.toLowerCase().includes(i.search.toLowerCase()));return h&&g&&_&&S}),k=()=>{const f=t.length,h=t.filter(S=>S.status===P.COMPLETED).length,g=t.filter(S=>S.status===P.IN_PROGRESS).length,_=t.filter(S=>S.status===P.IDEA).length;return{total:f,completed:h,inProgress:g,ideas:_}};return{tasks:x(),allTasks:t,loading:n,filter:i,setFilter:o,createTask:u,updateTask:c,deleteTask:d,addFollowUp:p,toggleFollowUp:m,addProcessStep:y,toggleProcessStep:w,getTaskStats:k,refreshTasks:a}};function lx(){return l.jsx(Jy,{children:l.jsx(ux,{})})}function ux(){const{user:e,loading:t}=Ki(),{tasks:r,loading:n,filter:s,setFilter:i,createTask:o,updateTask:a,deleteTask:u,toggleFollowUp:c,toggleProcessStep:d,getTaskStats:p}=ax(),[m,y]=I.useState(!1),[w,x]=I.useState(null),[k,f]=I.useState("dashboard"),[h,g]=I.useState(!1),[_,S]=I.useState(null),[b,E]=I.useState(!1);I.useEffect(()=>{console.log("AppContent render - User:",(e==null?void 0:e.id)||"No user","AuthLoading:",t,"TasksLoading:",n,"Tasks count:",r.length)},[e,t,n,r.length]);const v=p();if(I.useEffect(()=>{!localStorage.getItem("hasSeenOnboarding")&&r.length===0&&!n&&g(!0)},[r.length,n]),t)return l.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:l.jsxs("div",{className:"text-center",children:[l.jsx(sn,{className:"w-8 h-8 animate-spin text-primary-600 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-600",children:"Loading..."})]})});if(!e)return l.jsxs("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:[l.jsxs("div",{className:"text-center max-w-md mx-auto p-8",children:[l.jsx("div",{className:"mx-auto w-24 h-24 bg-gradient-to-br from-primary-100 to-purple-100 rounded-full flex items-center justify-center mb-6",children:l.jsx("span",{className:"text-4xl",children:"💡"})}),l.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-3",children:"Welcome to Idea Manager"}),l.jsx("p",{className:"text-gray-600 mb-8",children:"Transform your ideas into reality with our structured workflow. Sign in to get started."}),l.jsx("button",{onClick:()=>E(!0),className:"bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl",children:"Get Started"})]}),l.jsx(ix,{isOpen:b,onClose:()=>E(!1),initialMode:"signin"})]});if(n)return l.jsxs("div",{className:"min-h-screen bg-gray-50",children:[l.jsx(dc,{onNewTask:()=>{},filter:s,setFilter:i,stats:{total:0,completed:0,inProgress:0,ideas:0},viewMode:k,setViewMode:f}),l.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:l.jsxs("div",{className:"text-center py-16",children:[l.jsx(sn,{className:"w-8 h-8 animate-spin text-primary-600 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-600",children:"Loading your ideas..."})]})})]});const j=()=>{x(null),y(!0)},C=R=>{x(R),S(R),y(!0)},X=R=>{w?a(w.id,R):o(R),y(!1),x(null)},lt=()=>{y(!1),x(null)},ut=R=>{window.confirm("Are you sure you want to delete this task?")&&u(R)},Sr=()=>{g(!1),localStorage.setItem("hasSeenOnboarding","true")},ue=()=>{g(!1),localStorage.setItem("hasSeenOnboarding","true"),j()},Se=R=>{if(R.length>0){const A=R[0];o({title:A.title,description:A.description,priority:A.priority,category:A.category,estimatedHours:A.estimatedHours||0})}},tt=(R,A)=>{a(R,{description:A})},T=(R,A)=>{const q=A.map((ee,Xt)=>({id:Date.now().toString()+Xt,title:ee.title,description:ee.description,completed:!1,order:Xt}));a(R,{processSteps:q})};return l.jsxs("div",{className:"min-h-screen bg-gray-50",children:[l.jsx(dc,{onNewTask:j,filter:s,setFilter:i,stats:v,viewMode:k,setViewMode:f}),l.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:r.length===0&&!Object.values(s).some(Boolean)?l.jsxs("div",{className:"text-center py-16",children:[l.jsx("div",{className:"mx-auto w-32 h-32 bg-gradient-to-br from-primary-100 to-purple-100 rounded-full flex items-center justify-center mb-6",children:l.jsx("span",{className:"text-5xl",children:"💡"})}),l.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Ready to innovate?"}),l.jsx("p",{className:"text-gray-600 mb-8 max-w-md mx-auto",children:"Start your innovation journey by capturing your first idea. Every great project begins with a single thought."}),l.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[l.jsx("button",{onClick:j,className:"bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white px-8 py-4 rounded-xl font-medium text-lg transition-all duration-200 shadow-lg hover:shadow-xl",children:"Create Your First Idea"}),l.jsx("button",{onClick:()=>g(!0),className:"border border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 rounded-xl font-medium text-lg transition-all duration-200",children:"Take a Tour"})]})]}):l.jsx(l.Fragment,{children:k==="dashboard"?l.jsx(Xy,{tasks:r,onNewTask:j,onEditTask:C,filter:s,setFilter:i,stats:v,onViewModeChange:f}):l.jsx(kw,{tasks:r,onEdit:C,onDelete:ut,onToggleFollowUp:c,onToggleProcessStep:d,onNewTask:j,filter:s})})}),m&&l.jsx(rx,{task:w,onSave:X,onClose:lt}),h&&l.jsx(nx,{onClose:Sr,onCreateFirstTask:ue}),l.jsx(sx,{tasks:r,currentTask:_,onTaskSuggestions:Se,onImproveDescription:tt,onGenerateSteps:T})]})}Io.createRoot(document.getElementById("root")).render(l.jsx(Vc.StrictMode,{children:l.jsx(lx,{})}));export{Uf as g};
