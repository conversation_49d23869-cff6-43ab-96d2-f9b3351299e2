{"version": 3, "file": "RealtimeClient.d.ts", "sourceRoot": "", "sources": ["../../src/RealtimeClient.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,SAAS,IAAI,WAAW,EAAE,MAAM,IAAI,CAAA;AAElD,OAAO,EAEL,gBAAgB,EAOjB,MAAM,iBAAiB,CAAA;AACxB,OAAO,UAAU,MAAM,kBAAkB,CAAA;AACzC,OAAO,KAAK,MAAM,aAAa,CAAA;AAG/B,OAAO,eAAe,MAAM,mBAAmB,CAAA;AAC/C,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAA;AAE/D,aAAK,KAAK,GAAG,OAAO,KAAK,CAAA;AAEzB,oBAAY,OAAO,GAAG;IACpB,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,UAAU,EAAE,MAAM,CAAA;IAClB,EAAE,EAAE,MAAM,CAAA;CACX,CAAA;AAED,oBAAY,qBAAqB,GAAG;IAClC,SAAS,CAAC,EAAE,wBAAwB,CAAA;IACpC,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,mBAAmB,CAAC,EAAE,MAAM,CAAA;IAC5B,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,MAAM,CAAC,EAAE,QAAQ,CAAA;IACjB,gBAAgB,CAAC,EAAE,QAAQ,CAAA;IAC3B,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAA;IACnC,MAAM,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAA;IAC/B,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,CAAA;IAC/C,KAAK,CAAC,EAAE,KAAK,CAAA;IACb,MAAM,CAAC,EAAE,OAAO,CAAA;IAChB,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,WAAW,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAA;CAC3C,CAAA;AAED,oBAAY,eAAe,GAAG;IAC5B,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,GAAG,CAAA;IACZ,GAAG,EAAE,MAAM,CAAA;IACX,QAAQ,CAAC,EAAE,MAAM,CAAA;CAClB,CAAA;AAED,oBAAY,6BAA6B,GAAG,IAAI,GAAG,WAAW,GAAG,OAAO,CAAA;AAIxE,MAAM,WAAW,wBAAwB;IACvC,KACE,OAAO,EAAE,MAAM,GAAG,GAAG,EACrB,QAAQ,CAAC,EAAE,GAAG,EACd,OAAO,CAAC,EAAE;QAAE,OAAO,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GACxC,aAAa,CAAA;CACjB;AAED,oBAAY,aAAa,GAAG,SAAS,GAAG,WAAW,GAAG,gBAAgB,CAAA;AAEtE,MAAM,WAAW,kBAAkB;IACjC,KAAK,EAAE,GAAG,CAAA;IACV,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;CACb;AASD,MAAM,CAAC,OAAO,OAAO,cAAc;IACjC,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAO;IACtC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAO;IAC5B,QAAQ,EAAE,eAAe,EAAE,CAAK;IAChC,QAAQ,EAAE,MAAM,CAAK;IACrB,YAAY,EAAE,MAAM,CAAK;IACzB,OAAO,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAkB;IACrD,MAAM,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAK;IACvC,OAAO,EAAE,MAAM,CAAkB;IACjC,SAAS,EAAE,wBAAwB,GAAG,IAAI,CAAA;IAC1C,mBAAmB,EAAE,MAAM,CAAQ;IACnC,cAAc,EAAE,UAAU,CAAC,OAAO,WAAW,CAAC,GAAG,SAAS,CAAY;IACtE,mBAAmB,EAAE,MAAM,GAAG,IAAI,CAAO;IACzC,GAAG,EAAE,MAAM,CAAI;IACf,cAAc,EAAE,KAAK,CAAA;IACrB,MAAM,EAAE,QAAQ,CAAO;IACvB,MAAM,EAAE,QAAQ,CAAA;IAChB,MAAM,EAAE,QAAQ,CAAA;IAChB,gBAAgB,EAAE,QAAQ,CAAA;IAC1B,IAAI,EAAE,aAAa,GAAG,IAAI,CAAO;IACjC,UAAU,EAAE,QAAQ,EAAE,CAAK;IAC3B,UAAU,EAAE,UAAU,CAAmB;IACzC,oBAAoB,EAAE;QACpB,IAAI,EAAE,QAAQ,EAAE,CAAA;QAChB,KAAK,EAAE,QAAQ,EAAE,CAAA;QACjB,KAAK,EAAE,QAAQ,EAAE,CAAA;QACjB,OAAO,EAAE,QAAQ,EAAE,CAAA;KACpB,CAKA;IACD,KAAK,EAAE,KAAK,CAAA;IACZ,WAAW,EAAE,CAAC,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAO;IACzD,MAAM,CAAC,EAAE,OAAO,CAAA;IAChB,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,SAAS,CAAC,EAAE,MAAM,CAAA;IAElB;;;;;;;;;;;;;;;;OAgBG;gBACS,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,qBAAqB;IAkD7D;;OAEG;IACH,OAAO,IAAI,IAAI;IAgCf;;;OAGG;IACH,WAAW,IAAI,MAAM;IAOrB;;;;;OAKG;IACH,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAehD;;OAEG;IACH,WAAW,IAAI,eAAe,EAAE;IAIhC;;;OAGG;IACG,aAAa,CACjB,OAAO,EAAE,eAAe,GACvB,OAAO,CAAC,6BAA6B,CAAC;IAQzC;;OAEG;IACG,iBAAiB,IAAI,OAAO,CAAC,6BAA6B,EAAE,CAAC;IAQnE;;;;OAIG;IACH,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG;IAIzC;;OAEG;IACH,eAAe,IAAI,gBAAgB;IAanC;;OAEG;IACH,WAAW,IAAI,OAAO;IAItB,OAAO,CACL,KAAK,EAAE,MAAM,EACb,MAAM,GAAE,sBAAuC,GAC9C,eAAe;IAMlB;;;;OAIG;IACH,IAAI,CAAC,IAAI,EAAE,eAAe,GAAG,IAAI;IAejC;;;;;;;;OAQG;IACG,OAAO,CAAC,KAAK,GAAE,MAAM,GAAG,IAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IAqCzD;;OAEG;IACG,aAAa;IAuBnB;;OAEG;IACH,eAAe;IA4Lf,OAAO,CAAC,gBAAgB;CAUzB;AAED,cAAM,gBAAgB;IACpB,UAAU,EAAE,MAAM,CAAgB;IAClC,KAAK,EAAE,QAAQ,CAAA;IACf,OAAO,EAAE,QAAQ,CAAW;IAC5B,OAAO,EAAE,QAAQ,CAAW;IAC5B,SAAS,EAAE,QAAQ,CAAW;IAC9B,MAAM,EAAE,QAAQ,CAAW;IAC3B,UAAU,EAAE,MAAM,CAA2B;IAC7C,IAAI,EAAE,QAAQ,CAAW;IACzB,GAAG,EAAE,MAAM,GAAG,GAAG,GAAG,IAAI,CAAO;gBAG7B,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,SAAS,EACrB,OAAO,EAAE;QAAE,KAAK,EAAE,QAAQ,CAAA;KAAE;CAK/B"}