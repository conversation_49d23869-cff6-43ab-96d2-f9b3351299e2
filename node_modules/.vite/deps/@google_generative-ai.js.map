{"version": 3, "sources": ["../../@google/generative-ai/dist/index.mjs"], "sourcesContent": ["/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nvar SchemaType;\n(function (SchemaType) {\n    /** String type. */\n    SchemaType[\"STRING\"] = \"string\";\n    /** Number type. */\n    SchemaType[\"NUMBER\"] = \"number\";\n    /** Integer type. */\n    SchemaType[\"INTEGER\"] = \"integer\";\n    /** Boolean type. */\n    SchemaType[\"BOOLEAN\"] = \"boolean\";\n    /** Array type. */\n    SchemaType[\"ARRAY\"] = \"array\";\n    /** Object type. */\n    SchemaType[\"OBJECT\"] = \"object\";\n})(SchemaType || (SchemaType = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @public\n */\nvar ExecutableCodeLanguage;\n(function (ExecutableCodeLanguage) {\n    ExecutableCodeLanguage[\"LANGUAGE_UNSPECIFIED\"] = \"language_unspecified\";\n    ExecutableCodeLanguage[\"PYTHON\"] = \"python\";\n})(ExecutableCodeLanguage || (ExecutableCodeLanguage = {}));\n/**\n * Possible outcomes of code execution.\n * @public\n */\nvar Outcome;\n(function (Outcome) {\n    /**\n     * Unspecified status. This value should not be used.\n     */\n    Outcome[\"OUTCOME_UNSPECIFIED\"] = \"outcome_unspecified\";\n    /**\n     * Code execution completed successfully.\n     */\n    Outcome[\"OUTCOME_OK\"] = \"outcome_ok\";\n    /**\n     * Code execution finished but with a failure. `stderr` should contain the\n     * reason.\n     */\n    Outcome[\"OUTCOME_FAILED\"] = \"outcome_failed\";\n    /**\n     * Code execution ran for too long, and was cancelled. There may or may not\n     * be a partial output present.\n     */\n    Outcome[\"OUTCOME_DEADLINE_EXCEEDED\"] = \"outcome_deadline_exceeded\";\n})(Outcome || (Outcome = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Possible roles.\n * @public\n */\nconst POSSIBLE_ROLES = [\"user\", \"model\", \"function\", \"system\"];\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nvar HarmCategory;\n(function (HarmCategory) {\n    HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n    HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n    HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n    HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n    HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n    HarmCategory[\"HARM_CATEGORY_CIVIC_INTEGRITY\"] = \"HARM_CATEGORY_CIVIC_INTEGRITY\";\n})(HarmCategory || (HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nvar HarmBlockThreshold;\n(function (HarmBlockThreshold) {\n    /** Threshold is unspecified. */\n    HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n    /** Content with NEGLIGIBLE will be allowed. */\n    HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n    /** Content with NEGLIGIBLE and LOW will be allowed. */\n    HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n    /** Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed. */\n    HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n    /** All content will be allowed. */\n    HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(HarmBlockThreshold || (HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nvar HarmProbability;\n(function (HarmProbability) {\n    /** Probability is unspecified. */\n    HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n    /** Content has a negligible chance of being unsafe. */\n    HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n    /** Content has a low chance of being unsafe. */\n    HarmProbability[\"LOW\"] = \"LOW\";\n    /** Content has a medium chance of being unsafe. */\n    HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n    /** Content has a high chance of being unsafe. */\n    HarmProbability[\"HIGH\"] = \"HIGH\";\n})(HarmProbability || (HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nvar BlockReason;\n(function (BlockReason) {\n    // A blocked reason was not specified.\n    BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n    // Content was blocked by safety settings.\n    BlockReason[\"SAFETY\"] = \"SAFETY\";\n    // Content was blocked, but the reason is uncategorized.\n    BlockReason[\"OTHER\"] = \"OTHER\";\n})(BlockReason || (BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nvar FinishReason;\n(function (FinishReason) {\n    // Default value. This value is unused.\n    FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n    // Natural stop point of the model or provided stop sequence.\n    FinishReason[\"STOP\"] = \"STOP\";\n    // The maximum number of tokens as specified in the request was reached.\n    FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n    // The candidate content was flagged for safety reasons.\n    FinishReason[\"SAFETY\"] = \"SAFETY\";\n    // The candidate content was flagged for recitation reasons.\n    FinishReason[\"RECITATION\"] = \"RECITATION\";\n    // The candidate content was flagged for using an unsupported language.\n    FinishReason[\"LANGUAGE\"] = \"LANGUAGE\";\n    // Token generation stopped because the content contains forbidden terms.\n    FinishReason[\"BLOCKLIST\"] = \"BLOCKLIST\";\n    // Token generation stopped for potentially containing prohibited content.\n    FinishReason[\"PROHIBITED_CONTENT\"] = \"PROHIBITED_CONTENT\";\n    // Token generation stopped because the content potentially contains Sensitive Personally Identifiable Information (SPII).\n    FinishReason[\"SPII\"] = \"SPII\";\n    // The function call generated by the model is invalid.\n    FinishReason[\"MALFORMED_FUNCTION_CALL\"] = \"MALFORMED_FUNCTION_CALL\";\n    // Unknown reason.\n    FinishReason[\"OTHER\"] = \"OTHER\";\n})(FinishReason || (FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nvar TaskType;\n(function (TaskType) {\n    TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n    TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n    TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n    TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n    TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n    TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(TaskType || (TaskType = {}));\n/**\n * @public\n */\nvar FunctionCallingMode;\n(function (FunctionCallingMode) {\n    // Unspecified function calling mode. This value should not be used.\n    FunctionCallingMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Default model behavior, model decides to predict either a function call\n    // or a natural language repspose.\n    FunctionCallingMode[\"AUTO\"] = \"AUTO\";\n    // Model is constrained to always predicting a function call only.\n    // If \"allowed_function_names\" are set, the predicted function call will be\n    // limited to any one of \"allowed_function_names\", else the predicted\n    // function call will be any one of the provided \"function_declarations\".\n    FunctionCallingMode[\"ANY\"] = \"ANY\";\n    // Model will not predict any function call. Model behavior is same as when\n    // not passing any function declarations.\n    FunctionCallingMode[\"NONE\"] = \"NONE\";\n})(FunctionCallingMode || (FunctionCallingMode = {}));\n/**\n * The mode of the predictor to be used in dynamic retrieval.\n * @public\n */\nvar DynamicRetrievalMode;\n(function (DynamicRetrievalMode) {\n    // Unspecified function calling mode. This value should not be used.\n    DynamicRetrievalMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Run retrieval only when system decides it is necessary.\n    DynamicRetrievalMode[\"MODE_DYNAMIC\"] = \"MODE_DYNAMIC\";\n})(DynamicRetrievalMode || (DynamicRetrievalMode = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Basic error type for this SDK.\n * @public\n */\nclass GoogleGenerativeAIError extends Error {\n    constructor(message) {\n        super(`[GoogleGenerativeAI Error]: ${message}`);\n    }\n}\n/**\n * Errors in the contents of a response from the model. This includes parsing\n * errors, or responses including a safety block reason.\n * @public\n */\nclass GoogleGenerativeAIResponseError extends GoogleGenerativeAIError {\n    constructor(message, response) {\n        super(message);\n        this.response = response;\n    }\n}\n/**\n * Error class covering HTTP errors when calling the server. Includes HTTP\n * status, statusText, and optional details, if provided in the server response.\n * @public\n */\nclass GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {\n    constructor(message, status, statusText, errorDetails) {\n        super(message);\n        this.status = status;\n        this.statusText = statusText;\n        this.errorDetails = errorDetails;\n    }\n}\n/**\n * Errors in the contents of a request originating from user input.\n * @public\n */\nclass GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {\n}\n/**\n * Error thrown when a request is aborted, either due to a timeout or\n * intentional cancellation by the user.\n * @public\n */\nclass GoogleGenerativeAIAbortError extends GoogleGenerativeAIError {\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst DEFAULT_API_VERSION = \"v1beta\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.24.1\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n    Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n    Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n    Task[\"COUNT_TOKENS\"] = \"countTokens\";\n    Task[\"EMBED_CONTENT\"] = \"embedContent\";\n    Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\nclass RequestUrl {\n    constructor(model, task, apiKey, stream, requestOptions) {\n        this.model = model;\n        this.task = task;\n        this.apiKey = apiKey;\n        this.stream = stream;\n        this.requestOptions = requestOptions;\n    }\n    toString() {\n        var _a, _b;\n        const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n        const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n        let url = `${baseUrl}/${apiVersion}/${this.model}:${this.task}`;\n        if (this.stream) {\n            url += \"?alt=sse\";\n        }\n        return url;\n    }\n}\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders(requestOptions) {\n    const clientHeaders = [];\n    if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiClient) {\n        clientHeaders.push(requestOptions.apiClient);\n    }\n    clientHeaders.push(`${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`);\n    return clientHeaders.join(\" \");\n}\nasync function getHeaders(url) {\n    var _a;\n    const headers = new Headers();\n    headers.append(\"Content-Type\", \"application/json\");\n    headers.append(\"x-goog-api-client\", getClientHeaders(url.requestOptions));\n    headers.append(\"x-goog-api-key\", url.apiKey);\n    let customHeaders = (_a = url.requestOptions) === null || _a === void 0 ? void 0 : _a.customHeaders;\n    if (customHeaders) {\n        if (!(customHeaders instanceof Headers)) {\n            try {\n                customHeaders = new Headers(customHeaders);\n            }\n            catch (e) {\n                throw new GoogleGenerativeAIRequestInputError(`unable to convert customHeaders value ${JSON.stringify(customHeaders)} to Headers: ${e.message}`);\n            }\n        }\n        for (const [headerName, headerValue] of customHeaders.entries()) {\n            if (headerName === \"x-goog-api-key\") {\n                throw new GoogleGenerativeAIRequestInputError(`Cannot set reserved header name ${headerName}`);\n            }\n            else if (headerName === \"x-goog-api-client\") {\n                throw new GoogleGenerativeAIRequestInputError(`Header name ${headerName} can only be set using the apiClient field`);\n            }\n            headers.append(headerName, headerValue);\n        }\n    }\n    return headers;\n}\nasync function constructModelRequest(model, task, apiKey, stream, body, requestOptions) {\n    const url = new RequestUrl(model, task, apiKey, stream, requestOptions);\n    return {\n        url: url.toString(),\n        fetchOptions: Object.assign(Object.assign({}, buildFetchOptions(requestOptions)), { method: \"POST\", headers: await getHeaders(url), body }),\n    };\n}\nasync function makeModelRequest(model, task, apiKey, stream, body, requestOptions = {}, \n// Allows this to be stubbed for tests\nfetchFn = fetch) {\n    const { url, fetchOptions } = await constructModelRequest(model, task, apiKey, stream, body, requestOptions);\n    return makeRequest(url, fetchOptions, fetchFn);\n}\nasync function makeRequest(url, fetchOptions, fetchFn = fetch) {\n    let response;\n    try {\n        response = await fetchFn(url, fetchOptions);\n    }\n    catch (e) {\n        handleResponseError(e, url);\n    }\n    if (!response.ok) {\n        await handleResponseNotOk(response, url);\n    }\n    return response;\n}\nfunction handleResponseError(e, url) {\n    let err = e;\n    if (err.name === \"AbortError\") {\n        err = new GoogleGenerativeAIAbortError(`Request aborted when fetching ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    else if (!(e instanceof GoogleGenerativeAIFetchError ||\n        e instanceof GoogleGenerativeAIRequestInputError)) {\n        err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    throw err;\n}\nasync function handleResponseNotOk(response, url) {\n    let message = \"\";\n    let errorDetails;\n    try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n            message += ` ${JSON.stringify(json.error.details)}`;\n            errorDetails = json.error.details;\n        }\n    }\n    catch (e) {\n        // ignored\n    }\n    throw new GoogleGenerativeAIFetchError(`Error fetching from ${url.toString()}: [${response.status} ${response.statusText}] ${message}`, response.status, response.statusText, errorDetails);\n}\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction buildFetchOptions(requestOptions) {\n    const fetchOptions = {};\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) !== undefined || (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n        const controller = new AbortController();\n        if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n            setTimeout(() => controller.abort(), requestOptions.timeout);\n        }\n        if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) {\n            requestOptions.signal.addEventListener(\"abort\", () => {\n                controller.abort();\n            });\n        }\n        fetchOptions.signal = controller.signal;\n    }\n    return fetchOptions;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nfunction addHelpers(response) {\n    response.text = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning text from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getText(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Text not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return \"\";\n    };\n    /**\n     * TODO: remove at next major version\n     */\n    response.functionCall = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            console.warn(`response.functionCall() is deprecated. ` +\n                `Use response.functionCalls() instead.`);\n            return getFunctionCalls(response)[0];\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    response.functionCalls = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getFunctionCalls(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    return response;\n}\n/**\n * Returns all text found in all parts of first candidate.\n */\nfunction getText(response) {\n    var _a, _b, _c, _d;\n    const textStrings = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.text) {\n                textStrings.push(part.text);\n            }\n            if (part.executableCode) {\n                textStrings.push(\"\\n```\" +\n                    part.executableCode.language +\n                    \"\\n\" +\n                    part.executableCode.code +\n                    \"\\n```\\n\");\n            }\n            if (part.codeExecutionResult) {\n                textStrings.push(\"\\n```\\n\" + part.codeExecutionResult.output + \"\\n```\\n\");\n            }\n        }\n    }\n    if (textStrings.length > 0) {\n        return textStrings.join(\"\");\n    }\n    else {\n        return \"\";\n    }\n}\n/**\n * Returns functionCall of first candidate.\n */\nfunction getFunctionCalls(response) {\n    var _a, _b, _c, _d;\n    const functionCalls = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.functionCall) {\n                functionCalls.push(part.functionCall);\n            }\n        }\n    }\n    if (functionCalls.length > 0) {\n        return functionCalls;\n    }\n    else {\n        return undefined;\n    }\n}\nconst badFinishReasons = [\n    FinishReason.RECITATION,\n    FinishReason.SAFETY,\n    FinishReason.LANGUAGE,\n];\nfunction hadBadFinishReason(candidate) {\n    return (!!candidate.finishReason &&\n        badFinishReasons.includes(candidate.finishReason));\n}\nfunction formatBlockErrorMessage(response) {\n    var _a, _b, _c;\n    let message = \"\";\n    if ((!response.candidates || response.candidates.length === 0) &&\n        response.promptFeedback) {\n        message += \"Response was blocked\";\n        if ((_a = response.promptFeedback) === null || _a === void 0 ? void 0 : _a.blockReason) {\n            message += ` due to ${response.promptFeedback.blockReason}`;\n        }\n        if ((_b = response.promptFeedback) === null || _b === void 0 ? void 0 : _b.blockReasonMessage) {\n            message += `: ${response.promptFeedback.blockReasonMessage}`;\n        }\n    }\n    else if ((_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0]) {\n        const firstCandidate = response.candidates[0];\n        if (hadBadFinishReason(firstCandidate)) {\n            message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n            if (firstCandidate.finishMessage) {\n                message += `: ${firstCandidate.finishMessage}`;\n            }\n        }\n    }\n    return message;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nfunction processStream(response) {\n    const inputStream = response.body.pipeThrough(new TextDecoderStream(\"utf8\", { fatal: true }));\n    const responseStream = getResponseStream(inputStream);\n    const [stream1, stream2] = responseStream.tee();\n    return {\n        stream: generateResponseSequence(stream1),\n        response: getResponsePromise(stream2),\n    };\n}\nasync function getResponsePromise(stream) {\n    const allResponses = [];\n    const reader = stream.getReader();\n    while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n            return addHelpers(aggregateResponses(allResponses));\n        }\n        allResponses.push(value);\n    }\n}\nfunction generateResponseSequence(stream) {\n    return __asyncGenerator(this, arguments, function* generateResponseSequence_1() {\n        const reader = stream.getReader();\n        while (true) {\n            const { value, done } = yield __await(reader.read());\n            if (done) {\n                break;\n            }\n            yield yield __await(addHelpers(value));\n        }\n    });\n}\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nfunction getResponseStream(inputStream) {\n    const reader = inputStream.getReader();\n    const stream = new ReadableStream({\n        start(controller) {\n            let currentText = \"\";\n            return pump();\n            function pump() {\n                return reader\n                    .read()\n                    .then(({ value, done }) => {\n                    if (done) {\n                        if (currentText.trim()) {\n                            controller.error(new GoogleGenerativeAIError(\"Failed to parse stream\"));\n                            return;\n                        }\n                        controller.close();\n                        return;\n                    }\n                    currentText += value;\n                    let match = currentText.match(responseLineRE);\n                    let parsedResponse;\n                    while (match) {\n                        try {\n                            parsedResponse = JSON.parse(match[1]);\n                        }\n                        catch (e) {\n                            controller.error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match[1]}\"`));\n                            return;\n                        }\n                        controller.enqueue(parsedResponse);\n                        currentText = currentText.substring(match[0].length);\n                        match = currentText.match(responseLineRE);\n                    }\n                    return pump();\n                })\n                    .catch((e) => {\n                    let err = e;\n                    err.stack = e.stack;\n                    if (err.name === \"AbortError\") {\n                        err = new GoogleGenerativeAIAbortError(\"Request aborted when reading from the stream\");\n                    }\n                    else {\n                        err = new GoogleGenerativeAIError(\"Error reading from the stream\");\n                    }\n                    throw err;\n                });\n            }\n        },\n    });\n    return stream;\n}\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nfunction aggregateResponses(responses) {\n    const lastResponse = responses[responses.length - 1];\n    const aggregatedResponse = {\n        promptFeedback: lastResponse === null || lastResponse === void 0 ? void 0 : lastResponse.promptFeedback,\n    };\n    for (const response of responses) {\n        if (response.candidates) {\n            let candidateIndex = 0;\n            for (const candidate of response.candidates) {\n                if (!aggregatedResponse.candidates) {\n                    aggregatedResponse.candidates = [];\n                }\n                if (!aggregatedResponse.candidates[candidateIndex]) {\n                    aggregatedResponse.candidates[candidateIndex] = {\n                        index: candidateIndex,\n                    };\n                }\n                // Keep overwriting, the last one will be final\n                aggregatedResponse.candidates[candidateIndex].citationMetadata =\n                    candidate.citationMetadata;\n                aggregatedResponse.candidates[candidateIndex].groundingMetadata =\n                    candidate.groundingMetadata;\n                aggregatedResponse.candidates[candidateIndex].finishReason =\n                    candidate.finishReason;\n                aggregatedResponse.candidates[candidateIndex].finishMessage =\n                    candidate.finishMessage;\n                aggregatedResponse.candidates[candidateIndex].safetyRatings =\n                    candidate.safetyRatings;\n                /**\n                 * Candidates should always have content and parts, but this handles\n                 * possible malformed responses.\n                 */\n                if (candidate.content && candidate.content.parts) {\n                    if (!aggregatedResponse.candidates[candidateIndex].content) {\n                        aggregatedResponse.candidates[candidateIndex].content = {\n                            role: candidate.content.role || \"user\",\n                            parts: [],\n                        };\n                    }\n                    const newPart = {};\n                    for (const part of candidate.content.parts) {\n                        if (part.text) {\n                            newPart.text = part.text;\n                        }\n                        if (part.functionCall) {\n                            newPart.functionCall = part.functionCall;\n                        }\n                        if (part.executableCode) {\n                            newPart.executableCode = part.executableCode;\n                        }\n                        if (part.codeExecutionResult) {\n                            newPart.codeExecutionResult = part.codeExecutionResult;\n                        }\n                        if (Object.keys(newPart).length === 0) {\n                            newPart.text = \"\";\n                        }\n                        aggregatedResponse.candidates[candidateIndex].content.parts.push(newPart);\n                    }\n                }\n            }\n            candidateIndex++;\n        }\n        if (response.usageMetadata) {\n            aggregatedResponse.usageMetadata = response.usageMetadata;\n        }\n    }\n    return aggregatedResponse;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function generateContentStream(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.STREAM_GENERATE_CONTENT, apiKey, \n    /* stream */ true, JSON.stringify(params), requestOptions);\n    return processStream(response);\n}\nasync function generateContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.GENERATE_CONTENT, apiKey, \n    /* stream */ false, JSON.stringify(params), requestOptions);\n    const responseJson = await response.json();\n    const enhancedResponse = addHelpers(responseJson);\n    return {\n        response: enhancedResponse,\n    };\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction formatSystemInstruction(input) {\n    // null or undefined\n    if (input == null) {\n        return undefined;\n    }\n    else if (typeof input === \"string\") {\n        return { role: \"system\", parts: [{ text: input }] };\n    }\n    else if (input.text) {\n        return { role: \"system\", parts: [input] };\n    }\n    else if (input.parts) {\n        if (!input.role) {\n            return { role: \"system\", parts: input.parts };\n        }\n        else {\n            return input;\n        }\n    }\n}\nfunction formatNewContent(request) {\n    let newParts = [];\n    if (typeof request === \"string\") {\n        newParts = [{ text: request }];\n    }\n    else {\n        for (const partOrString of request) {\n            if (typeof partOrString === \"string\") {\n                newParts.push({ text: partOrString });\n            }\n            else {\n                newParts.push(partOrString);\n            }\n        }\n    }\n    return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(parts) {\n    const userContent = { role: \"user\", parts: [] };\n    const functionContent = { role: \"function\", parts: [] };\n    let hasUserContent = false;\n    let hasFunctionContent = false;\n    for (const part of parts) {\n        if (\"functionResponse\" in part) {\n            functionContent.parts.push(part);\n            hasFunctionContent = true;\n        }\n        else {\n            userContent.parts.push(part);\n            hasUserContent = true;\n        }\n    }\n    if (hasUserContent && hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.\");\n    }\n    if (!hasUserContent && !hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"No content is provided for sending chat message.\");\n    }\n    if (hasUserContent) {\n        return userContent;\n    }\n    return functionContent;\n}\nfunction formatCountTokensInput(params, modelParams) {\n    var _a;\n    let formattedGenerateContentRequest = {\n        model: modelParams === null || modelParams === void 0 ? void 0 : modelParams.model,\n        generationConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.generationConfig,\n        safetySettings: modelParams === null || modelParams === void 0 ? void 0 : modelParams.safetySettings,\n        tools: modelParams === null || modelParams === void 0 ? void 0 : modelParams.tools,\n        toolConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.toolConfig,\n        systemInstruction: modelParams === null || modelParams === void 0 ? void 0 : modelParams.systemInstruction,\n        cachedContent: (_a = modelParams === null || modelParams === void 0 ? void 0 : modelParams.cachedContent) === null || _a === void 0 ? void 0 : _a.name,\n        contents: [],\n    };\n    const containsGenerateContentRequest = params.generateContentRequest != null;\n    if (params.contents) {\n        if (containsGenerateContentRequest) {\n            throw new GoogleGenerativeAIRequestInputError(\"CountTokensRequest must have one of contents or generateContentRequest, not both.\");\n        }\n        formattedGenerateContentRequest.contents = params.contents;\n    }\n    else if (containsGenerateContentRequest) {\n        formattedGenerateContentRequest = Object.assign(Object.assign({}, formattedGenerateContentRequest), params.generateContentRequest);\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedGenerateContentRequest.contents = [content];\n    }\n    return { generateContentRequest: formattedGenerateContentRequest };\n}\nfunction formatGenerateContentInput(params) {\n    let formattedRequest;\n    if (params.contents) {\n        formattedRequest = params;\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedRequest = { contents: [content] };\n    }\n    if (params.systemInstruction) {\n        formattedRequest.systemInstruction = formatSystemInstruction(params.systemInstruction);\n    }\n    return formattedRequest;\n}\nfunction formatEmbedContentInput(params) {\n    if (typeof params === \"string\" || Array.isArray(params)) {\n        const content = formatNewContent(params);\n        return { content };\n    }\n    return params;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// https://ai.google.dev/api/rest/v1beta/Content#part\nconst VALID_PART_FIELDS = [\n    \"text\",\n    \"inlineData\",\n    \"functionCall\",\n    \"functionResponse\",\n    \"executableCode\",\n    \"codeExecutionResult\",\n];\nconst VALID_PARTS_PER_ROLE = {\n    user: [\"text\", \"inlineData\"],\n    function: [\"functionResponse\"],\n    model: [\"text\", \"functionCall\", \"executableCode\", \"codeExecutionResult\"],\n    // System instructions shouldn't be in history anyway.\n    system: [\"text\"],\n};\nfunction validateChatHistory(history) {\n    let prevContent = false;\n    for (const currContent of history) {\n        const { role, parts } = currContent;\n        if (!prevContent && role !== \"user\") {\n            throw new GoogleGenerativeAIError(`First content should be with role 'user', got ${role}`);\n        }\n        if (!POSSIBLE_ROLES.includes(role)) {\n            throw new GoogleGenerativeAIError(`Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(POSSIBLE_ROLES)}`);\n        }\n        if (!Array.isArray(parts)) {\n            throw new GoogleGenerativeAIError(\"Content should have 'parts' property with an array of Parts\");\n        }\n        if (parts.length === 0) {\n            throw new GoogleGenerativeAIError(\"Each Content should have at least one part\");\n        }\n        const countFields = {\n            text: 0,\n            inlineData: 0,\n            functionCall: 0,\n            functionResponse: 0,\n            fileData: 0,\n            executableCode: 0,\n            codeExecutionResult: 0,\n        };\n        for (const part of parts) {\n            for (const key of VALID_PART_FIELDS) {\n                if (key in part) {\n                    countFields[key] += 1;\n                }\n            }\n        }\n        const validParts = VALID_PARTS_PER_ROLE[role];\n        for (const key of VALID_PART_FIELDS) {\n            if (!validParts.includes(key) && countFields[key] > 0) {\n                throw new GoogleGenerativeAIError(`Content with role '${role}' can't contain '${key}' part`);\n            }\n        }\n        prevContent = true;\n    }\n}\n/**\n * Returns true if the response is valid (could be appended to the history), flase otherwise.\n */\nfunction isValidResponse(response) {\n    var _a;\n    if (response.candidates === undefined || response.candidates.length === 0) {\n        return false;\n    }\n    const content = (_a = response.candidates[0]) === null || _a === void 0 ? void 0 : _a.content;\n    if (content === undefined) {\n        return false;\n    }\n    if (content.parts === undefined || content.parts.length === 0) {\n        return false;\n    }\n    for (const part of content.parts) {\n        if (part === undefined || Object.keys(part).length === 0) {\n            return false;\n        }\n        if (part.text !== undefined && part.text === \"\") {\n            return false;\n        }\n    }\n    return true;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = \"SILENT_ERROR\";\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nclass ChatSession {\n    constructor(apiKey, model, params, _requestOptions = {}) {\n        this.model = model;\n        this.params = params;\n        this._requestOptions = _requestOptions;\n        this._history = [];\n        this._sendPromise = Promise.resolve();\n        this._apiKey = apiKey;\n        if (params === null || params === void 0 ? void 0 : params.history) {\n            validateChatHistory(params.history);\n            this._history = params.history;\n        }\n    }\n    /**\n     * Gets the chat history so far. Blocked prompts are not added to history.\n     * Blocked candidates are not added to history, nor are the prompts that\n     * generated them.\n     */\n    async getHistory() {\n        await this._sendPromise;\n        return this._history;\n    }\n    /**\n     * Sends a chat message and receives a non-streaming\n     * {@link GenerateContentResult}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessage(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        let finalResult;\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => generateContent(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions))\n            .then((result) => {\n            var _a;\n            if (isValidResponse(result.response)) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({ parts: [], \n                    // Response seems to come back without a role set.\n                    role: \"model\" }, (_a = result.response.candidates) === null || _a === void 0 ? void 0 : _a[0].content);\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(result.response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n            finalResult = result;\n        })\n            .catch((e) => {\n            // Resets _sendPromise to avoid subsequent calls failing and throw error.\n            this._sendPromise = Promise.resolve();\n            throw e;\n        });\n        await this._sendPromise;\n        return finalResult;\n    }\n    /**\n     * Sends a chat message and receives the response as a\n     * {@link GenerateContentStreamResult} containing an iterable stream\n     * and a response promise.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessageStream(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        const streamPromise = generateContentStream(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions);\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => streamPromise)\n            // This must be handled to avoid unhandled rejection, but jump\n            // to the final catch block with a label to not log this error.\n            .catch((_ignored) => {\n            throw new Error(SILENT_ERROR);\n        })\n            .then((streamResult) => streamResult.response)\n            .then((response) => {\n            if (isValidResponse(response)) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({}, response.candidates[0].content);\n                // Response seems to come back without a role set.\n                if (!responseContent.role) {\n                    responseContent.role = \"model\";\n                }\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n        })\n            .catch((e) => {\n            // Errors in streamPromise are already catchable by the user as\n            // streamPromise is returned.\n            // Avoid duplicating the error message in logs.\n            if (e.message !== SILENT_ERROR) {\n                // Users do not have access to _sendPromise to catch errors\n                // downstream from streamPromise, so they should not throw.\n                console.error(e);\n            }\n        });\n        return streamPromise;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function countTokens(apiKey, model, params, singleRequestOptions) {\n    const response = await makeModelRequest(model, Task.COUNT_TOKENS, apiKey, false, JSON.stringify(params), singleRequestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function embedContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.EMBED_CONTENT, apiKey, false, JSON.stringify(params), requestOptions);\n    return response.json();\n}\nasync function batchEmbedContents(apiKey, model, params, requestOptions) {\n    const requestsWithModel = params.requests.map((request) => {\n        return Object.assign(Object.assign({}, request), { model });\n    });\n    const response = await makeModelRequest(model, Task.BATCH_EMBED_CONTENTS, apiKey, false, JSON.stringify({ requests: requestsWithModel }), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for generative model APIs.\n * @public\n */\nclass GenerativeModel {\n    constructor(apiKey, modelParams, _requestOptions = {}) {\n        this.apiKey = apiKey;\n        this._requestOptions = _requestOptions;\n        if (modelParams.model.includes(\"/\")) {\n            // Models may be named \"models/model-name\" or \"tunedModels/model-name\"\n            this.model = modelParams.model;\n        }\n        else {\n            // If path is not included, assume it's a non-tuned model.\n            this.model = `models/${modelParams.model}`;\n        }\n        this.generationConfig = modelParams.generationConfig || {};\n        this.safetySettings = modelParams.safetySettings || [];\n        this.tools = modelParams.tools;\n        this.toolConfig = modelParams.toolConfig;\n        this.systemInstruction = formatSystemInstruction(modelParams.systemInstruction);\n        this.cachedContent = modelParams.cachedContent;\n    }\n    /**\n     * Makes a single non-streaming call to the model\n     * and returns an object containing a single {@link GenerateContentResponse}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContent(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContent(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Makes a single streaming call to the model and returns an object\n     * containing an iterable stream that iterates over all chunks in the\n     * streaming response as well as a promise that returns the final\n     * aggregated response.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContentStream(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContentStream(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Gets a new {@link ChatSession} instance which can be used for\n     * multi-turn chats.\n     */\n    startChat(startChatParams) {\n        var _a;\n        return new ChatSession(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, startChatParams), this._requestOptions);\n    }\n    /**\n     * Counts the tokens in the provided request.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async countTokens(request, requestOptions = {}) {\n        const formattedParams = formatCountTokensInput(request, {\n            model: this.model,\n            generationConfig: this.generationConfig,\n            safetySettings: this.safetySettings,\n            tools: this.tools,\n            toolConfig: this.toolConfig,\n            systemInstruction: this.systemInstruction,\n            cachedContent: this.cachedContent,\n        });\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return countTokens(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds the provided content.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async embedContent(request, requestOptions = {}) {\n        const formattedParams = formatEmbedContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return embedContent(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds an array of {@link EmbedContentRequest}s.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async batchEmbedContents(batchEmbedContentRequest, requestOptions = {}) {\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return batchEmbedContents(this.apiKey, this.model, batchEmbedContentRequest, generativeModelRequestOptions);\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Top-level class for this SDK\n * @public\n */\nclass GoogleGenerativeAI {\n    constructor(apiKey) {\n        this.apiKey = apiKey;\n    }\n    /**\n     * Gets a {@link GenerativeModel} instance for the provided model name.\n     */\n    getGenerativeModel(modelParams, requestOptions) {\n        if (!modelParams.model) {\n            throw new GoogleGenerativeAIError(`Must provide a model name. ` +\n                `Example: genai.getGenerativeModel({ model: 'my-model-name' })`);\n        }\n        return new GenerativeModel(this.apiKey, modelParams, requestOptions);\n    }\n    /**\n     * Creates a {@link GenerativeModel} instance from provided content cache.\n     */\n    getGenerativeModelFromCachedContent(cachedContent, modelParams, requestOptions) {\n        if (!cachedContent.name) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `name` field.\");\n        }\n        if (!cachedContent.model) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `model` field.\");\n        }\n        /**\n         * Not checking tools and toolConfig for now as it would require a deep\n         * equality comparison and isn't likely to be a common case.\n         */\n        const disallowedDuplicates = [\"model\", \"systemInstruction\"];\n        for (const key of disallowedDuplicates) {\n            if ((modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) &&\n                cachedContent[key] &&\n                (modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) !== cachedContent[key]) {\n                if (key === \"model\") {\n                    const modelParamsComp = modelParams.model.startsWith(\"models/\")\n                        ? modelParams.model.replace(\"models/\", \"\")\n                        : modelParams.model;\n                    const cachedContentComp = cachedContent.model.startsWith(\"models/\")\n                        ? cachedContent.model.replace(\"models/\", \"\")\n                        : cachedContent.model;\n                    if (modelParamsComp === cachedContentComp) {\n                        continue;\n                    }\n                }\n                throw new GoogleGenerativeAIRequestInputError(`Different value for \"${key}\" specified in modelParams` +\n                    ` (${modelParams[key]}) and cachedContent (${cachedContent[key]})`);\n            }\n        }\n        const modelParamsFromCache = Object.assign(Object.assign({}, modelParams), { model: cachedContent.model, tools: cachedContent.tools, toolConfig: cachedContent.toolConfig, systemInstruction: cachedContent.systemInstruction, cachedContent });\n        return new GenerativeModel(this.apiKey, modelParamsFromCache, requestOptions);\n    }\n}\n\nexport { BlockReason, ChatSession, DynamicRetrievalMode, ExecutableCodeLanguage, FinishReason, FunctionCallingMode, GenerativeModel, GoogleGenerativeAI, GoogleGenerativeAIAbortError, GoogleGenerativeAIError, GoogleGenerativeAIFetchError, GoogleGenerativeAIRequestInputError, GoogleGenerativeAIResponseError, HarmBlockThreshold, HarmCategory, HarmProbability, Outcome, POSSIBLE_ROLES, SchemaType, TaskType };\n//# sourceMappingURL=index.mjs.map\n"], "mappings": ";;;AAKA,IAAI;AAAA,CACH,SAAUA,aAAY;AAEnB,EAAAA,YAAW,QAAQ,IAAI;AAEvB,EAAAA,YAAW,QAAQ,IAAI;AAEvB,EAAAA,YAAW,SAAS,IAAI;AAExB,EAAAA,YAAW,SAAS,IAAI;AAExB,EAAAA,YAAW,OAAO,IAAI;AAEtB,EAAAA,YAAW,QAAQ,IAAI;AAC3B,GAAG,eAAe,aAAa,CAAC,EAAE;AAqBlC,IAAI;AAAA,CACH,SAAUC,yBAAwB;AAC/B,EAAAA,wBAAuB,sBAAsB,IAAI;AACjD,EAAAA,wBAAuB,QAAQ,IAAI;AACvC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAK1D,IAAI;AAAA,CACH,SAAUC,UAAS;AAIhB,EAAAA,SAAQ,qBAAqB,IAAI;AAIjC,EAAAA,SAAQ,YAAY,IAAI;AAKxB,EAAAA,SAAQ,gBAAgB,IAAI;AAK5B,EAAAA,SAAQ,2BAA2B,IAAI;AAC3C,GAAG,YAAY,UAAU,CAAC,EAAE;AAsB5B,IAAM,iBAAiB,CAAC,QAAQ,SAAS,YAAY,QAAQ;AAK7D,IAAI;AAAA,CACH,SAAUC,eAAc;AACrB,EAAAA,cAAa,2BAA2B,IAAI;AAC5C,EAAAA,cAAa,2BAA2B,IAAI;AAC5C,EAAAA,cAAa,iCAAiC,IAAI;AAClD,EAAAA,cAAa,0BAA0B,IAAI;AAC3C,EAAAA,cAAa,iCAAiC,IAAI;AAClD,EAAAA,cAAa,+BAA+B,IAAI;AACpD,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAKtC,IAAI;AAAA,CACH,SAAUC,qBAAoB;AAE3B,EAAAA,oBAAmB,kCAAkC,IAAI;AAEzD,EAAAA,oBAAmB,qBAAqB,IAAI;AAE5C,EAAAA,oBAAmB,wBAAwB,IAAI;AAE/C,EAAAA,oBAAmB,iBAAiB,IAAI;AAExC,EAAAA,oBAAmB,YAAY,IAAI;AACvC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAKlD,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAExB,EAAAA,iBAAgB,8BAA8B,IAAI;AAElD,EAAAA,iBAAgB,YAAY,IAAI;AAEhC,EAAAA,iBAAgB,KAAK,IAAI;AAEzB,EAAAA,iBAAgB,QAAQ,IAAI;AAE5B,EAAAA,iBAAgB,MAAM,IAAI;AAC9B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAK5C,IAAI;AAAA,CACH,SAAUC,cAAa;AAEpB,EAAAA,aAAY,4BAA4B,IAAI;AAE5C,EAAAA,aAAY,QAAQ,IAAI;AAExB,EAAAA,aAAY,OAAO,IAAI;AAC3B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAKpC,IAAI;AAAA,CACH,SAAUC,eAAc;AAErB,EAAAA,cAAa,2BAA2B,IAAI;AAE5C,EAAAA,cAAa,MAAM,IAAI;AAEvB,EAAAA,cAAa,YAAY,IAAI;AAE7B,EAAAA,cAAa,QAAQ,IAAI;AAEzB,EAAAA,cAAa,YAAY,IAAI;AAE7B,EAAAA,cAAa,UAAU,IAAI;AAE3B,EAAAA,cAAa,WAAW,IAAI;AAE5B,EAAAA,cAAa,oBAAoB,IAAI;AAErC,EAAAA,cAAa,MAAM,IAAI;AAEvB,EAAAA,cAAa,yBAAyB,IAAI;AAE1C,EAAAA,cAAa,OAAO,IAAI;AAC5B,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAKtC,IAAI;AAAA,CACH,SAAUC,WAAU;AACjB,EAAAA,UAAS,uBAAuB,IAAI;AACpC,EAAAA,UAAS,iBAAiB,IAAI;AAC9B,EAAAA,UAAS,oBAAoB,IAAI;AACjC,EAAAA,UAAS,qBAAqB,IAAI;AAClC,EAAAA,UAAS,gBAAgB,IAAI;AAC7B,EAAAA,UAAS,YAAY,IAAI;AAC7B,GAAG,aAAa,WAAW,CAAC,EAAE;AAI9B,IAAI;AAAA,CACH,SAAUC,sBAAqB;AAE5B,EAAAA,qBAAoB,kBAAkB,IAAI;AAG1C,EAAAA,qBAAoB,MAAM,IAAI;AAK9B,EAAAA,qBAAoB,KAAK,IAAI;AAG7B,EAAAA,qBAAoB,MAAM,IAAI;AAClC,GAAG,wBAAwB,sBAAsB,CAAC,EAAE;AAKpD,IAAI;AAAA,CACH,SAAUC,uBAAsB;AAE7B,EAAAA,sBAAqB,kBAAkB,IAAI;AAE3C,EAAAA,sBAAqB,cAAc,IAAI;AAC3C,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAsBtD,IAAM,0BAAN,cAAsC,MAAM;AAAA,EACxC,YAAY,SAAS;AACjB,UAAM,+BAA+B,OAAO,EAAE;AAAA,EAClD;AACJ;AAMA,IAAM,kCAAN,cAA8C,wBAAwB;AAAA,EAClE,YAAY,SAAS,UAAU;AAC3B,UAAM,OAAO;AACb,SAAK,WAAW;AAAA,EACpB;AACJ;AAMA,IAAM,+BAAN,cAA2C,wBAAwB;AAAA,EAC/D,YAAY,SAAS,QAAQ,YAAY,cAAc;AACnD,UAAM,OAAO;AACb,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACxB;AACJ;AAKA,IAAM,sCAAN,cAAkD,wBAAwB;AAC1E;AAMA,IAAM,+BAAN,cAA2C,wBAAwB;AACnE;AAkBA,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAK5B,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAI;AAAA,CACH,SAAUC,OAAM;AACb,EAAAA,MAAK,kBAAkB,IAAI;AAC3B,EAAAA,MAAK,yBAAyB,IAAI;AAClC,EAAAA,MAAK,cAAc,IAAI;AACvB,EAAAA,MAAK,eAAe,IAAI;AACxB,EAAAA,MAAK,sBAAsB,IAAI;AACnC,GAAG,SAAS,OAAO,CAAC,EAAE;AACtB,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,OAAO,MAAM,QAAQ,QAAQ,gBAAgB;AACrD,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,WAAW;AACP,QAAI,IAAI;AACR,UAAM,eAAe,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AACtG,UAAM,YAAY,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAChG,QAAI,MAAM,GAAG,OAAO,IAAI,UAAU,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI;AAC7D,QAAI,KAAK,QAAQ;AACb,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,iBAAiB,gBAAgB;AACtC,QAAM,gBAAgB,CAAC;AACvB,MAAI,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,WAAW;AAC1F,kBAAc,KAAK,eAAe,SAAS;AAAA,EAC/C;AACA,gBAAc,KAAK,GAAG,kBAAkB,IAAI,eAAe,EAAE;AAC7D,SAAO,cAAc,KAAK,GAAG;AACjC;AACA,eAAe,WAAW,KAAK;AAC3B,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ;AAC5B,UAAQ,OAAO,gBAAgB,kBAAkB;AACjD,UAAQ,OAAO,qBAAqB,iBAAiB,IAAI,cAAc,CAAC;AACxE,UAAQ,OAAO,kBAAkB,IAAI,MAAM;AAC3C,MAAI,iBAAiB,KAAK,IAAI,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG;AACtF,MAAI,eAAe;AACf,QAAI,EAAE,yBAAyB,UAAU;AACrC,UAAI;AACA,wBAAgB,IAAI,QAAQ,aAAa;AAAA,MAC7C,SACO,GAAG;AACN,cAAM,IAAI,oCAAoC,yCAAyC,KAAK,UAAU,aAAa,CAAC,gBAAgB,EAAE,OAAO,EAAE;AAAA,MACnJ;AAAA,IACJ;AACA,eAAW,CAAC,YAAY,WAAW,KAAK,cAAc,QAAQ,GAAG;AAC7D,UAAI,eAAe,kBAAkB;AACjC,cAAM,IAAI,oCAAoC,mCAAmC,UAAU,EAAE;AAAA,MACjG,WACS,eAAe,qBAAqB;AACzC,cAAM,IAAI,oCAAoC,eAAe,UAAU,4CAA4C;AAAA,MACvH;AACA,cAAQ,OAAO,YAAY,WAAW;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO;AACX;AACA,eAAe,sBAAsB,OAAO,MAAM,QAAQ,QAAQ,MAAM,gBAAgB;AACpF,QAAM,MAAM,IAAI,WAAW,OAAO,MAAM,QAAQ,QAAQ,cAAc;AACtE,SAAO;AAAA,IACH,KAAK,IAAI,SAAS;AAAA,IAClB,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,cAAc,CAAC,GAAG,EAAE,QAAQ,QAAQ,SAAS,MAAM,WAAW,GAAG,GAAG,KAAK,CAAC;AAAA,EAC9I;AACJ;AACA,eAAe,iBAAiB,OAAO,MAAM,QAAQ,QAAQ,MAAM,iBAAiB,CAAC,GAErF,UAAU,OAAO;AACb,QAAM,EAAE,KAAK,aAAa,IAAI,MAAM,sBAAsB,OAAO,MAAM,QAAQ,QAAQ,MAAM,cAAc;AAC3G,SAAO,YAAY,KAAK,cAAc,OAAO;AACjD;AACA,eAAe,YAAY,KAAK,cAAc,UAAU,OAAO;AAC3D,MAAI;AACJ,MAAI;AACA,eAAW,MAAM,QAAQ,KAAK,YAAY;AAAA,EAC9C,SACO,GAAG;AACN,wBAAoB,GAAG,GAAG;AAAA,EAC9B;AACA,MAAI,CAAC,SAAS,IAAI;AACd,UAAM,oBAAoB,UAAU,GAAG;AAAA,EAC3C;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,GAAG,KAAK;AACjC,MAAI,MAAM;AACV,MAAI,IAAI,SAAS,cAAc;AAC3B,UAAM,IAAI,6BAA6B,iCAAiC,IAAI,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AACtG,QAAI,QAAQ,EAAE;AAAA,EAClB,WACS,EAAE,aAAa,gCACpB,aAAa,sCAAsC;AACnD,UAAM,IAAI,wBAAwB,uBAAuB,IAAI,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AACvF,QAAI,QAAQ,EAAE;AAAA,EAClB;AACA,QAAM;AACV;AACA,eAAe,oBAAoB,UAAU,KAAK;AAC9C,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,cAAU,KAAK,MAAM;AACrB,QAAI,KAAK,MAAM,SAAS;AACpB,iBAAW,IAAI,KAAK,UAAU,KAAK,MAAM,OAAO,CAAC;AACjD,qBAAe,KAAK,MAAM;AAAA,IAC9B;AAAA,EACJ,SACO,GAAG;AAAA,EAEV;AACA,QAAM,IAAI,6BAA6B,uBAAuB,IAAI,SAAS,CAAC,MAAM,SAAS,MAAM,IAAI,SAAS,UAAU,KAAK,OAAO,IAAI,SAAS,QAAQ,SAAS,YAAY,YAAY;AAC9L;AAMA,SAAS,kBAAkB,gBAAgB;AACvC,QAAM,eAAe,CAAC;AACtB,OAAK,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,YAAY,WAAc,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,YAAY,GAAG;AACxM,UAAM,aAAa,IAAI,gBAAgB;AACvC,SAAK,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,YAAY,GAAG;AAC/F,iBAAW,MAAM,WAAW,MAAM,GAAG,eAAe,OAAO;AAAA,IAC/D;AACA,QAAI,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,QAAQ;AACvF,qBAAe,OAAO,iBAAiB,SAAS,MAAM;AAClD,mBAAW,MAAM;AAAA,MACrB,CAAC;AAAA,IACL;AACA,iBAAa,SAAS,WAAW;AAAA,EACrC;AACA,SAAO;AACX;AAsBA,SAAS,WAAW,UAAU;AAC1B,WAAS,OAAO,MAAM;AAClB,QAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,UAAI,SAAS,WAAW,SAAS,GAAG;AAChC,gBAAQ,KAAK,qBAAqB,SAAS,WAAW,MAAM,6HAEU;AAAA,MAC1E;AACA,UAAI,mBAAmB,SAAS,WAAW,CAAC,CAAC,GAAG;AAC5C,cAAM,IAAI,gCAAgC,GAAG,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,MAC9F;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B,WACS,SAAS,gBAAgB;AAC9B,YAAM,IAAI,gCAAgC,uBAAuB,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAClH;AACA,WAAO;AAAA,EACX;AAIA,WAAS,eAAe,MAAM;AAC1B,QAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,UAAI,SAAS,WAAW,SAAS,GAAG;AAChC,gBAAQ,KAAK,qBAAqB,SAAS,WAAW,MAAM,uIAEU;AAAA,MAC1E;AACA,UAAI,mBAAmB,SAAS,WAAW,CAAC,CAAC,GAAG;AAC5C,cAAM,IAAI,gCAAgC,GAAG,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,MAC9F;AACA,cAAQ,KAAK,8EAC8B;AAC3C,aAAO,iBAAiB,QAAQ,EAAE,CAAC;AAAA,IACvC,WACS,SAAS,gBAAgB;AAC9B,YAAM,IAAI,gCAAgC,gCAAgC,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAC3H;AACA,WAAO;AAAA,EACX;AACA,WAAS,gBAAgB,MAAM;AAC3B,QAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACvD,UAAI,SAAS,WAAW,SAAS,GAAG;AAChC,gBAAQ,KAAK,qBAAqB,SAAS,WAAW,MAAM,uIAEU;AAAA,MAC1E;AACA,UAAI,mBAAmB,SAAS,WAAW,CAAC,CAAC,GAAG;AAC5C,cAAM,IAAI,gCAAgC,GAAG,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,MAC9F;AACA,aAAO,iBAAiB,QAAQ;AAAA,IACpC,WACS,SAAS,gBAAgB;AAC9B,YAAM,IAAI,gCAAgC,gCAAgC,wBAAwB,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAC3H;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIA,SAAS,QAAQ,UAAU;AACvB,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,cAAc,CAAC;AACrB,OAAK,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACpI,eAAW,SAAS,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACnJ,UAAI,KAAK,MAAM;AACX,oBAAY,KAAK,KAAK,IAAI;AAAA,MAC9B;AACA,UAAI,KAAK,gBAAgB;AACrB,oBAAY,KAAK,UACb,KAAK,eAAe,WACpB,OACA,KAAK,eAAe,OACpB,SAAS;AAAA,MACjB;AACA,UAAI,KAAK,qBAAqB;AAC1B,oBAAY,KAAK,YAAY,KAAK,oBAAoB,SAAS,SAAS;AAAA,MAC5E;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,YAAY,SAAS,GAAG;AACxB,WAAO,YAAY,KAAK,EAAE;AAAA,EAC9B,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,iBAAiB,UAAU;AAChC,MAAI,IAAI,IAAI,IAAI;AAChB,QAAM,gBAAgB,CAAC;AACvB,OAAK,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACpI,eAAW,SAAS,MAAM,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,EAAE,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACnJ,UAAI,KAAK,cAAc;AACnB,sBAAc,KAAK,KAAK,YAAY;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,cAAc,SAAS,GAAG;AAC1B,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,IAAM,mBAAmB;AAAA,EACrB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACjB;AACA,SAAS,mBAAmB,WAAW;AACnC,SAAQ,CAAC,CAAC,UAAU,gBAChB,iBAAiB,SAAS,UAAU,YAAY;AACxD;AACA,SAAS,wBAAwB,UAAU;AACvC,MAAI,IAAI,IAAI;AACZ,MAAI,UAAU;AACd,OAAK,CAAC,SAAS,cAAc,SAAS,WAAW,WAAW,MACxD,SAAS,gBAAgB;AACzB,eAAW;AACX,SAAK,KAAK,SAAS,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AACpF,iBAAW,WAAW,SAAS,eAAe,WAAW;AAAA,IAC7D;AACA,SAAK,KAAK,SAAS,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB;AAC3F,iBAAW,KAAK,SAAS,eAAe,kBAAkB;AAAA,IAC9D;AAAA,EACJ,YACU,KAAK,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,GAAG;AAC5E,UAAM,iBAAiB,SAAS,WAAW,CAAC;AAC5C,QAAI,mBAAmB,cAAc,GAAG;AACpC,iBAAW,gCAAgC,eAAe,YAAY;AACtE,UAAI,eAAe,eAAe;AAC9B,mBAAW,KAAK,eAAe,aAAa;AAAA,MAChD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAmBA,SAAS,QAAQ,GAAG;AAChB,SAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AACvE;AAEA,SAAS,iBAAiB,SAAS,YAAY,WAAW;AACtD,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AACpH,WAAS,KAAK,GAAG;AAAE,QAAI,EAAE,CAAC;AAAG,QAAE,CAAC,IAAI,SAAU,GAAG;AAAE,eAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,YAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAA,EAAG;AACzI,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI;AAAE,WAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IAAG,SAAS,GAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAK,GAAG;AAAE,MAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE;AAAQ,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAuBA,IAAM,iBAAiB;AASvB,SAAS,cAAc,UAAU;AAC7B,QAAM,cAAc,SAAS,KAAK,YAAY,IAAI,kBAAkB,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;AAC5F,QAAM,iBAAiB,kBAAkB,WAAW;AACpD,QAAM,CAAC,SAAS,OAAO,IAAI,eAAe,IAAI;AAC9C,SAAO;AAAA,IACH,QAAQ,yBAAyB,OAAO;AAAA,IACxC,UAAU,mBAAmB,OAAO;AAAA,EACxC;AACJ;AACA,eAAe,mBAAmB,QAAQ;AACtC,QAAM,eAAe,CAAC;AACtB,QAAM,SAAS,OAAO,UAAU;AAChC,SAAO,MAAM;AACT,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI,MAAM;AACN,aAAO,WAAW,mBAAmB,YAAY,CAAC;AAAA,IACtD;AACA,iBAAa,KAAK,KAAK;AAAA,EAC3B;AACJ;AACA,SAAS,yBAAyB,QAAQ;AACtC,SAAO,iBAAiB,MAAM,WAAW,UAAU,6BAA6B;AAC5E,UAAM,SAAS,OAAO,UAAU;AAChC,WAAO,MAAM;AACT,YAAM,EAAE,OAAO,KAAK,IAAI,MAAM,QAAQ,OAAO,KAAK,CAAC;AACnD,UAAI,MAAM;AACN;AAAA,MACJ;AACA,YAAM,MAAM,QAAQ,WAAW,KAAK,CAAC;AAAA,IACzC;AAAA,EACJ,CAAC;AACL;AAMA,SAAS,kBAAkB,aAAa;AACpC,QAAM,SAAS,YAAY,UAAU;AACrC,QAAM,SAAS,IAAI,eAAe;AAAA,IAC9B,MAAM,YAAY;AACd,UAAI,cAAc;AAClB,aAAO,KAAK;AACZ,eAAS,OAAO;AACZ,eAAO,OACF,KAAK,EACL,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM;AAC3B,cAAI,MAAM;AACN,gBAAI,YAAY,KAAK,GAAG;AACpB,yBAAW,MAAM,IAAI,wBAAwB,wBAAwB,CAAC;AACtE;AAAA,YACJ;AACA,uBAAW,MAAM;AACjB;AAAA,UACJ;AACA,yBAAe;AACf,cAAI,QAAQ,YAAY,MAAM,cAAc;AAC5C,cAAI;AACJ,iBAAO,OAAO;AACV,gBAAI;AACA,+BAAiB,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,YACxC,SACO,GAAG;AACN,yBAAW,MAAM,IAAI,wBAAwB,iCAAiC,MAAM,CAAC,CAAC,GAAG,CAAC;AAC1F;AAAA,YACJ;AACA,uBAAW,QAAQ,cAAc;AACjC,0BAAc,YAAY,UAAU,MAAM,CAAC,EAAE,MAAM;AACnD,oBAAQ,YAAY,MAAM,cAAc;AAAA,UAC5C;AACA,iBAAO,KAAK;AAAA,QAChB,CAAC,EACI,MAAM,CAAC,MAAM;AACd,cAAI,MAAM;AACV,cAAI,QAAQ,EAAE;AACd,cAAI,IAAI,SAAS,cAAc;AAC3B,kBAAM,IAAI,6BAA6B,8CAA8C;AAAA,UACzF,OACK;AACD,kBAAM,IAAI,wBAAwB,+BAA+B;AAAA,UACrE;AACA,gBAAM;AAAA,QACV,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAKA,SAAS,mBAAmB,WAAW;AACnC,QAAM,eAAe,UAAU,UAAU,SAAS,CAAC;AACnD,QAAM,qBAAqB;AAAA,IACvB,gBAAgB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AAAA,EAC7F;AACA,aAAW,YAAY,WAAW;AAC9B,QAAI,SAAS,YAAY;AACrB,UAAI,iBAAiB;AACrB,iBAAW,aAAa,SAAS,YAAY;AACzC,YAAI,CAAC,mBAAmB,YAAY;AAChC,6BAAmB,aAAa,CAAC;AAAA,QACrC;AACA,YAAI,CAAC,mBAAmB,WAAW,cAAc,GAAG;AAChD,6BAAmB,WAAW,cAAc,IAAI;AAAA,YAC5C,OAAO;AAAA,UACX;AAAA,QACJ;AAEA,2BAAmB,WAAW,cAAc,EAAE,mBAC1C,UAAU;AACd,2BAAmB,WAAW,cAAc,EAAE,oBAC1C,UAAU;AACd,2BAAmB,WAAW,cAAc,EAAE,eAC1C,UAAU;AACd,2BAAmB,WAAW,cAAc,EAAE,gBAC1C,UAAU;AACd,2BAAmB,WAAW,cAAc,EAAE,gBAC1C,UAAU;AAKd,YAAI,UAAU,WAAW,UAAU,QAAQ,OAAO;AAC9C,cAAI,CAAC,mBAAmB,WAAW,cAAc,EAAE,SAAS;AACxD,+BAAmB,WAAW,cAAc,EAAE,UAAU;AAAA,cACpD,MAAM,UAAU,QAAQ,QAAQ;AAAA,cAChC,OAAO,CAAC;AAAA,YACZ;AAAA,UACJ;AACA,gBAAM,UAAU,CAAC;AACjB,qBAAW,QAAQ,UAAU,QAAQ,OAAO;AACxC,gBAAI,KAAK,MAAM;AACX,sBAAQ,OAAO,KAAK;AAAA,YACxB;AACA,gBAAI,KAAK,cAAc;AACnB,sBAAQ,eAAe,KAAK;AAAA,YAChC;AACA,gBAAI,KAAK,gBAAgB;AACrB,sBAAQ,iBAAiB,KAAK;AAAA,YAClC;AACA,gBAAI,KAAK,qBAAqB;AAC1B,sBAAQ,sBAAsB,KAAK;AAAA,YACvC;AACA,gBAAI,OAAO,KAAK,OAAO,EAAE,WAAW,GAAG;AACnC,sBAAQ,OAAO;AAAA,YACnB;AACA,+BAAmB,WAAW,cAAc,EAAE,QAAQ,MAAM,KAAK,OAAO;AAAA,UAC5E;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,IACJ;AACA,QAAI,SAAS,eAAe;AACxB,yBAAmB,gBAAgB,SAAS;AAAA,IAChD;AAAA,EACJ;AACA,SAAO;AACX;AAkBA,eAAe,sBAAsB,QAAQ,OAAO,QAAQ,gBAAgB;AACxE,QAAM,WAAW,MAAM;AAAA,IAAiB;AAAA,IAAO,KAAK;AAAA,IAAyB;AAAA;AAAA,IAChE;AAAA,IAAM,KAAK,UAAU,MAAM;AAAA,IAAG;AAAA,EAAc;AACzD,SAAO,cAAc,QAAQ;AACjC;AACA,eAAe,gBAAgB,QAAQ,OAAO,QAAQ,gBAAgB;AAClE,QAAM,WAAW,MAAM;AAAA,IAAiB;AAAA,IAAO,KAAK;AAAA,IAAkB;AAAA;AAAA,IACzD;AAAA,IAAO,KAAK,UAAU,MAAM;AAAA,IAAG;AAAA,EAAc;AAC1D,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,mBAAmB,WAAW,YAAY;AAChD,SAAO;AAAA,IACH,UAAU;AAAA,EACd;AACJ;AAkBA,SAAS,wBAAwB,OAAO;AAEpC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX,WACS,OAAO,UAAU,UAAU;AAChC,WAAO,EAAE,MAAM,UAAU,OAAO,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE;AAAA,EACtD,WACS,MAAM,MAAM;AACjB,WAAO,EAAE,MAAM,UAAU,OAAO,CAAC,KAAK,EAAE;AAAA,EAC5C,WACS,MAAM,OAAO;AAClB,QAAI,CAAC,MAAM,MAAM;AACb,aAAO,EAAE,MAAM,UAAU,OAAO,MAAM,MAAM;AAAA,IAChD,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,SAAS;AAC/B,MAAI,WAAW,CAAC;AAChB,MAAI,OAAO,YAAY,UAAU;AAC7B,eAAW,CAAC,EAAE,MAAM,QAAQ,CAAC;AAAA,EACjC,OACK;AACD,eAAW,gBAAgB,SAAS;AAChC,UAAI,OAAO,iBAAiB,UAAU;AAClC,iBAAS,KAAK,EAAE,MAAM,aAAa,CAAC;AAAA,MACxC,OACK;AACD,iBAAS,KAAK,YAAY;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,+CAA+C,QAAQ;AAClE;AASA,SAAS,+CAA+C,OAAO;AAC3D,QAAM,cAAc,EAAE,MAAM,QAAQ,OAAO,CAAC,EAAE;AAC9C,QAAM,kBAAkB,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;AACtD,MAAI,iBAAiB;AACrB,MAAI,qBAAqB;AACzB,aAAW,QAAQ,OAAO;AACtB,QAAI,sBAAsB,MAAM;AAC5B,sBAAgB,MAAM,KAAK,IAAI;AAC/B,2BAAqB;AAAA,IACzB,OACK;AACD,kBAAY,MAAM,KAAK,IAAI;AAC3B,uBAAiB;AAAA,IACrB;AAAA,EACJ;AACA,MAAI,kBAAkB,oBAAoB;AACtC,UAAM,IAAI,wBAAwB,4HAA4H;AAAA,EAClK;AACA,MAAI,CAAC,kBAAkB,CAAC,oBAAoB;AACxC,UAAM,IAAI,wBAAwB,kDAAkD;AAAA,EACxF;AACA,MAAI,gBAAgB;AAChB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,QAAQ,aAAa;AACjD,MAAI;AACJ,MAAI,kCAAkC;AAAA,IAClC,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IAC7E,kBAAkB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IACxF,gBAAgB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IACtF,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IAC7E,YAAY,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IAClF,mBAAmB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAAA,IACzF,gBAAgB,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,IAClJ,UAAU,CAAC;AAAA,EACf;AACA,QAAM,iCAAiC,OAAO,0BAA0B;AACxE,MAAI,OAAO,UAAU;AACjB,QAAI,gCAAgC;AAChC,YAAM,IAAI,oCAAoC,mFAAmF;AAAA,IACrI;AACA,oCAAgC,WAAW,OAAO;AAAA,EACtD,WACS,gCAAgC;AACrC,sCAAkC,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,+BAA+B,GAAG,OAAO,sBAAsB;AAAA,EACrI,OACK;AAED,UAAM,UAAU,iBAAiB,MAAM;AACvC,oCAAgC,WAAW,CAAC,OAAO;AAAA,EACvD;AACA,SAAO,EAAE,wBAAwB,gCAAgC;AACrE;AACA,SAAS,2BAA2B,QAAQ;AACxC,MAAI;AACJ,MAAI,OAAO,UAAU;AACjB,uBAAmB;AAAA,EACvB,OACK;AAED,UAAM,UAAU,iBAAiB,MAAM;AACvC,uBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE;AAAA,EAC7C;AACA,MAAI,OAAO,mBAAmB;AAC1B,qBAAiB,oBAAoB,wBAAwB,OAAO,iBAAiB;AAAA,EACzF;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,QAAQ;AACrC,MAAI,OAAO,WAAW,YAAY,MAAM,QAAQ,MAAM,GAAG;AACrD,UAAM,UAAU,iBAAiB,MAAM;AACvC,WAAO,EAAE,QAAQ;AAAA,EACrB;AACA,SAAO;AACX;AAmBA,IAAM,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,uBAAuB;AAAA,EACzB,MAAM,CAAC,QAAQ,YAAY;AAAA,EAC3B,UAAU,CAAC,kBAAkB;AAAA,EAC7B,OAAO,CAAC,QAAQ,gBAAgB,kBAAkB,qBAAqB;AAAA;AAAA,EAEvE,QAAQ,CAAC,MAAM;AACnB;AACA,SAAS,oBAAoB,SAAS;AAClC,MAAI,cAAc;AAClB,aAAW,eAAe,SAAS;AAC/B,UAAM,EAAE,MAAM,MAAM,IAAI;AACxB,QAAI,CAAC,eAAe,SAAS,QAAQ;AACjC,YAAM,IAAI,wBAAwB,iDAAiD,IAAI,EAAE;AAAA,IAC7F;AACA,QAAI,CAAC,eAAe,SAAS,IAAI,GAAG;AAChC,YAAM,IAAI,wBAAwB,4CAA4C,IAAI,yBAAyB,KAAK,UAAU,cAAc,CAAC,EAAE;AAAA,IAC/I;AACA,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,YAAM,IAAI,wBAAwB,6DAA6D;AAAA,IACnG;AACA,QAAI,MAAM,WAAW,GAAG;AACpB,YAAM,IAAI,wBAAwB,4CAA4C;AAAA,IAClF;AACA,UAAM,cAAc;AAAA,MAChB,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,IACzB;AACA,eAAW,QAAQ,OAAO;AACtB,iBAAW,OAAO,mBAAmB;AACjC,YAAI,OAAO,MAAM;AACb,sBAAY,GAAG,KAAK;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,aAAa,qBAAqB,IAAI;AAC5C,eAAW,OAAO,mBAAmB;AACjC,UAAI,CAAC,WAAW,SAAS,GAAG,KAAK,YAAY,GAAG,IAAI,GAAG;AACnD,cAAM,IAAI,wBAAwB,sBAAsB,IAAI,oBAAoB,GAAG,QAAQ;AAAA,MAC/F;AAAA,IACJ;AACA,kBAAc;AAAA,EAClB;AACJ;AAIA,SAAS,gBAAgB,UAAU;AAC/B,MAAI;AACJ,MAAI,SAAS,eAAe,UAAa,SAAS,WAAW,WAAW,GAAG;AACvE,WAAO;AAAA,EACX;AACA,QAAM,WAAW,KAAK,SAAS,WAAW,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACtF,MAAI,YAAY,QAAW;AACvB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,UAAU,UAAa,QAAQ,MAAM,WAAW,GAAG;AAC3D,WAAO;AAAA,EACX;AACA,aAAW,QAAQ,QAAQ,OAAO;AAC9B,QAAI,SAAS,UAAa,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AACtD,aAAO;AAAA,IACX;AACA,QAAI,KAAK,SAAS,UAAa,KAAK,SAAS,IAAI;AAC7C,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAqBA,IAAM,eAAe;AAOrB,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,QAAQ,OAAO,QAAQ,kBAAkB,CAAC,GAAG;AACrD,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,SAAK,WAAW,CAAC;AACjB,SAAK,eAAe,QAAQ,QAAQ;AACpC,SAAK,UAAU;AACf,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,0BAAoB,OAAO,OAAO;AAClC,WAAK,WAAW,OAAO;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,aAAa;AACf,UAAM,KAAK;AACX,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAY,SAAS,iBAAiB,CAAC,GAAG;AAC5C,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,UAAM,KAAK;AACX,UAAM,aAAa,iBAAiB,OAAO;AAC3C,UAAM,yBAAyB;AAAA,MAC3B,iBAAiB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC3E,mBAAmB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC7E,QAAQ,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAClE,aAAa,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACvE,oBAAoB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC9E,gBAAgB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC1E,UAAU,CAAC,GAAG,KAAK,UAAU,UAAU;AAAA,IAC3C;AACA,UAAM,4BAA4B,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe,GAAG,cAAc;AACvG,QAAI;AAEJ,SAAK,eAAe,KAAK,aACpB,KAAK,MAAM,gBAAgB,KAAK,SAAS,KAAK,OAAO,wBAAwB,yBAAyB,CAAC,EACvG,KAAK,CAAC,WAAW;AAClB,UAAIC;AACJ,UAAI,gBAAgB,OAAO,QAAQ,GAAG;AAClC,aAAK,SAAS,KAAK,UAAU;AAC7B,cAAM,kBAAkB,OAAO,OAAO;AAAA,UAAE,OAAO,CAAC;AAAA;AAAA,UAE5C,MAAM;AAAA,QAAQ,IAAIA,MAAK,OAAO,SAAS,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC,EAAE,OAAO;AACzG,aAAK,SAAS,KAAK,eAAe;AAAA,MACtC,OACK;AACD,cAAM,oBAAoB,wBAAwB,OAAO,QAAQ;AACjE,YAAI,mBAAmB;AACnB,kBAAQ,KAAK,mCAAmC,iBAAiB,wCAAwC;AAAA,QAC7G;AAAA,MACJ;AACA,oBAAc;AAAA,IAClB,CAAC,EACI,MAAM,CAAC,MAAM;AAEd,WAAK,eAAe,QAAQ,QAAQ;AACpC,YAAM;AAAA,IACV,CAAC;AACD,UAAM,KAAK;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,kBAAkB,SAAS,iBAAiB,CAAC,GAAG;AAClD,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,UAAM,KAAK;AACX,UAAM,aAAa,iBAAiB,OAAO;AAC3C,UAAM,yBAAyB;AAAA,MAC3B,iBAAiB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC3E,mBAAmB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC7E,QAAQ,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAClE,aAAa,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACvE,oBAAoB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC9E,gBAAgB,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC1E,UAAU,CAAC,GAAG,KAAK,UAAU,UAAU;AAAA,IAC3C;AACA,UAAM,4BAA4B,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe,GAAG,cAAc;AACvG,UAAM,gBAAgB,sBAAsB,KAAK,SAAS,KAAK,OAAO,wBAAwB,yBAAyB;AAEvH,SAAK,eAAe,KAAK,aACpB,KAAK,MAAM,aAAa,EAGxB,MAAM,CAAC,aAAa;AACrB,YAAM,IAAI,MAAM,YAAY;AAAA,IAChC,CAAC,EACI,KAAK,CAAC,iBAAiB,aAAa,QAAQ,EAC5C,KAAK,CAAC,aAAa;AACpB,UAAI,gBAAgB,QAAQ,GAAG;AAC3B,aAAK,SAAS,KAAK,UAAU;AAC7B,cAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,SAAS,WAAW,CAAC,EAAE,OAAO;AAExE,YAAI,CAAC,gBAAgB,MAAM;AACvB,0BAAgB,OAAO;AAAA,QAC3B;AACA,aAAK,SAAS,KAAK,eAAe;AAAA,MACtC,OACK;AACD,cAAM,oBAAoB,wBAAwB,QAAQ;AAC1D,YAAI,mBAAmB;AACnB,kBAAQ,KAAK,yCAAyC,iBAAiB,wCAAwC;AAAA,QACnH;AAAA,MACJ;AAAA,IACJ,CAAC,EACI,MAAM,CAAC,MAAM;AAId,UAAI,EAAE,YAAY,cAAc;AAG5B,gBAAQ,MAAM,CAAC;AAAA,MACnB;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AAkBA,eAAe,YAAY,QAAQ,OAAO,QAAQ,sBAAsB;AACpE,QAAM,WAAW,MAAM,iBAAiB,OAAO,KAAK,cAAc,QAAQ,OAAO,KAAK,UAAU,MAAM,GAAG,oBAAoB;AAC7H,SAAO,SAAS,KAAK;AACzB;AAkBA,eAAe,aAAa,QAAQ,OAAO,QAAQ,gBAAgB;AAC/D,QAAM,WAAW,MAAM,iBAAiB,OAAO,KAAK,eAAe,QAAQ,OAAO,KAAK,UAAU,MAAM,GAAG,cAAc;AACxH,SAAO,SAAS,KAAK;AACzB;AACA,eAAe,mBAAmB,QAAQ,OAAO,QAAQ,gBAAgB;AACrE,QAAM,oBAAoB,OAAO,SAAS,IAAI,CAAC,YAAY;AACvD,WAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,CAAC;AAAA,EAC9D,CAAC;AACD,QAAM,WAAW,MAAM,iBAAiB,OAAO,KAAK,sBAAsB,QAAQ,OAAO,KAAK,UAAU,EAAE,UAAU,kBAAkB,CAAC,GAAG,cAAc;AACxJ,SAAO,SAAS,KAAK;AACzB;AAsBA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,QAAQ,aAAa,kBAAkB,CAAC,GAAG;AACnD,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,QAAI,YAAY,MAAM,SAAS,GAAG,GAAG;AAEjC,WAAK,QAAQ,YAAY;AAAA,IAC7B,OACK;AAED,WAAK,QAAQ,UAAU,YAAY,KAAK;AAAA,IAC5C;AACA,SAAK,mBAAmB,YAAY,oBAAoB,CAAC;AACzD,SAAK,iBAAiB,YAAY,kBAAkB,CAAC;AACrD,SAAK,QAAQ,YAAY;AACzB,SAAK,aAAa,YAAY;AAC9B,SAAK,oBAAoB,wBAAwB,YAAY,iBAAiB;AAC9E,SAAK,gBAAgB,YAAY;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,gBAAgB,SAAS,iBAAiB,CAAC,GAAG;AAChD,QAAI;AACJ,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,UAAM,gCAAgC,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe,GAAG,cAAc;AAC3G,WAAO,gBAAgB,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,mBAAmB,KAAK,mBAAmB,gBAAgB,KAAK,KAAK,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG,eAAe,GAAG,6BAA6B;AAAA,EACrX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,sBAAsB,SAAS,iBAAiB,CAAC,GAAG;AACtD,QAAI;AACJ,UAAM,kBAAkB,2BAA2B,OAAO;AAC1D,UAAM,gCAAgC,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe,GAAG,cAAc;AAC3G,WAAO,sBAAsB,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,mBAAmB,KAAK,mBAAmB,gBAAgB,KAAK,KAAK,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG,eAAe,GAAG,6BAA6B;AAAA,EAC3X;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,iBAAiB;AACvB,QAAI;AACJ,WAAO,IAAI,YAAY,KAAK,QAAQ,KAAK,OAAO,OAAO,OAAO,EAAE,kBAAkB,KAAK,kBAAkB,gBAAgB,KAAK,gBAAgB,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,mBAAmB,KAAK,mBAAmB,gBAAgB,KAAK,KAAK,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG,eAAe,GAAG,KAAK,eAAe;AAAA,EAC5W;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,YAAY,SAAS,iBAAiB,CAAC,GAAG;AAC5C,UAAM,kBAAkB,uBAAuB,SAAS;AAAA,MACpD,OAAO,KAAK;AAAA,MACZ,kBAAkB,KAAK;AAAA,MACvB,gBAAgB,KAAK;AAAA,MACrB,OAAO,KAAK;AAAA,MACZ,YAAY,KAAK;AAAA,MACjB,mBAAmB,KAAK;AAAA,MACxB,eAAe,KAAK;AAAA,IACxB,CAAC;AACD,UAAM,gCAAgC,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe,GAAG,cAAc;AAC3G,WAAO,YAAY,KAAK,QAAQ,KAAK,OAAO,iBAAiB,6BAA6B;AAAA,EAC9F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,aAAa,SAAS,iBAAiB,CAAC,GAAG;AAC7C,UAAM,kBAAkB,wBAAwB,OAAO;AACvD,UAAM,gCAAgC,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe,GAAG,cAAc;AAC3G,WAAO,aAAa,KAAK,QAAQ,KAAK,OAAO,iBAAiB,6BAA6B;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,mBAAmB,0BAA0B,iBAAiB,CAAC,GAAG;AACpE,UAAM,gCAAgC,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe,GAAG,cAAc;AAC3G,WAAO,mBAAmB,KAAK,QAAQ,KAAK,OAAO,0BAA0B,6BAA6B;AAAA,EAC9G;AACJ;AAsBA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAY,QAAQ;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,aAAa,gBAAgB;AAC5C,QAAI,CAAC,YAAY,OAAO;AACpB,YAAM,IAAI,wBAAwB,0FACiC;AAAA,IACvE;AACA,WAAO,IAAI,gBAAgB,KAAK,QAAQ,aAAa,cAAc;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAIA,oCAAoC,eAAe,aAAa,gBAAgB;AAC5E,QAAI,CAAC,cAAc,MAAM;AACrB,YAAM,IAAI,oCAAoC,6CAA6C;AAAA,IAC/F;AACA,QAAI,CAAC,cAAc,OAAO;AACtB,YAAM,IAAI,oCAAoC,8CAA8C;AAAA,IAChG;AAKA,UAAM,uBAAuB,CAAC,SAAS,mBAAmB;AAC1D,eAAW,OAAO,sBAAsB;AACpC,WAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,GAAG,MAC1E,cAAc,GAAG,MAChB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,GAAG,OAAO,cAAc,GAAG,GAAG;AACrG,YAAI,QAAQ,SAAS;AACjB,gBAAM,kBAAkB,YAAY,MAAM,WAAW,SAAS,IACxD,YAAY,MAAM,QAAQ,WAAW,EAAE,IACvC,YAAY;AAClB,gBAAM,oBAAoB,cAAc,MAAM,WAAW,SAAS,IAC5D,cAAc,MAAM,QAAQ,WAAW,EAAE,IACzC,cAAc;AACpB,cAAI,oBAAoB,mBAAmB;AACvC;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,IAAI,oCAAoC,wBAAwB,GAAG,+BAChE,YAAY,GAAG,CAAC,wBAAwB,cAAc,GAAG,CAAC,GAAG;AAAA,MAC1E;AAAA,IACJ;AACA,UAAM,uBAAuB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,EAAE,OAAO,cAAc,OAAO,OAAO,cAAc,OAAO,YAAY,cAAc,YAAY,mBAAmB,cAAc,mBAAmB,cAAc,CAAC;AAC9O,WAAO,IAAI,gBAAgB,KAAK,QAAQ,sBAAsB,cAAc;AAAA,EAChF;AACJ;", "names": ["SchemaType", "ExecutableCodeLanguage", "Outcome", "HarmCategory", "HarmBlockThreshold", "HarmProbability", "BlockReason", "FinishReason", "TaskType", "FunctionCallingMode", "DynamicRetrievalMode", "Task", "_a"]}