# Supabase Setup Guide for Idea Manager

## Prerequisites
- Supabase project created (IdeaPool - nbzbqptayogfxdtmfavm)
- Access to Supabase dashboard

## Step 1: Set up Database Schema

1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/nbzbqptayogfxdtmfavm
2. Navigate to the **SQL Editor** in the left sidebar
3. Create a new query and copy-paste the entire content from `supabase-schema.sql`
4. Run the query to create all tables, policies, and indexes

## Step 2: Enable Authentication

1. Go to **Authentication** > **Settings** in your Supabase dashboard
2. Make sure **Enable email confirmations** is turned ON (recommended)
3. Configure your site URL:
   - Site URL: `http://localhost:5174` (for development)
   - Redirect URLs: `http://localhost:5174/**` (for development)

## Step 3: Configure Row Level Security (RLS)

The schema already includes RLS policies, but verify they're enabled:

1. Go to **Database** > **Tables**
2. Check that RLS is enabled for:
   - `tasks`
   - `follow_ups`
   - `process_steps`
   - `attachments`

## Step 4: Test the Application

1. Start the development server: `npm run dev`
2. Open http://localhost:5174
3. You should see the authentication screen
4. Try creating an account and signing in

## Database Schema Overview

### Tables Created:

1. **tasks** - Main tasks/ideas table
   - Stores all task information
   - Linked to authenticated users
   - Includes status, priority, dates, etc.

2. **follow_ups** - Task follow-up items
   - Linked to tasks
   - Can be marked as completed
   - Include due dates

3. **process_steps** - Task process steps
   - Linked to tasks
   - Ordered steps for task completion
   - Can be marked as completed

4. **attachments** - File attachments (for future use)
   - Linked to tasks
   - Stores file metadata

### Security Features:

- **Row Level Security (RLS)** enabled on all tables
- Users can only access their own data
- Policies prevent unauthorized access
- Automatic user_id filtering

### Performance Features:

- Indexes on frequently queried columns
- Automatic `updated_at` timestamp updates
- Optimized for filtering and sorting

## Troubleshooting

### Common Issues:

1. **Authentication not working**
   - Check that the anon key is correct in `src/lib/supabase.js`
   - Verify site URL in Supabase auth settings

2. **Database errors**
   - Make sure the schema was run successfully
   - Check that RLS policies are enabled
   - Verify user permissions

3. **Tasks not loading**
   - Check browser console for errors
   - Verify user is authenticated
   - Check network tab for API calls

### Getting Help:

- Check Supabase logs in the dashboard
- Use browser developer tools to debug
- Check the console for error messages

## Production Deployment

When deploying to production:

1. Update the site URL in Supabase auth settings
2. Add production domain to redirect URLs
3. Consider setting up custom SMTP for emails
4. Review and test all RLS policies
5. Set up database backups

## Next Steps

After setup is complete, you can:

1. Create your first account
2. Start adding ideas and tasks
3. Test all features (create, edit, delete tasks)
4. Try the workflow stages (idea → planning → in progress → review → completed)
5. Test follow-ups and process steps

The application now uses Supabase for:
- User authentication (signup, signin, password reset)
- Data storage (tasks, follow-ups, process steps)
- Real-time updates (when implemented)
- Secure data access with RLS
