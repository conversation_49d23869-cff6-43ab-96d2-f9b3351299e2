# Idea Manager - Transform Ideas into Reality

A modern, full-stack task management application built with React and Supabase that helps you capture, develop, and execute your ideas through a structured workflow.

## ✨ Features

### 🔐 Authentication & Security
- **Secure Authentication** - Email/password signup and signin
- **User Profiles** - Personalized user experience
- **Row Level Security** - Your data is private and secure
- **Password Reset** - Easy account recovery

### 💡 Idea Management
- **Structured Workflow** - Idea → Planning → In Progress → Review → Completed
- **Rich Task Details** - Title, description, priority, category, tags
- **Due Dates & Time Tracking** - Estimated and actual hours
- **Progress Tracking** - Visual progress indicators

### 📋 Advanced Features
- **Process Steps** - Break down complex ideas into manageable steps
- **Follow-ups** - Track action items with due dates
- **Smart Filtering** - Filter by status, priority, category, or search
- **Dashboard & List Views** - Multiple ways to view your ideas
- **Real-time Updates** - Changes sync instantly

### 🎨 Modern UI/UX
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Dark/Light Mode Ready** - Built with Tailwind CSS
- **Intuitive Interface** - Clean, modern design
- **Loading States** - Smooth user experience

## 🚀 Tech Stack

- **Frontend**: React 18, Tailwind CSS, Lucide Icons
- **Backend**: Supabase (PostgreSQL, Authentication, Real-time)
- **Build Tool**: Vite
- **Deployment**: Ready for Vercel, Netlify, or any static host

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd idea-manager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Follow the detailed instructions in `SUPABASE_SETUP.md`
   - Run the SQL schema from `supabase-schema.sql`
   - Update the Supabase configuration in `src/lib/supabase.js`

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   - Navigate to `http://localhost:5174`
   - Create an account and start managing your ideas!

## 🗄️ Database Schema

The application uses a well-structured PostgreSQL database with:

- **tasks** - Main ideas/tasks table
- **follow_ups** - Task follow-up items
- **process_steps** - Ordered task steps
- **attachments** - File attachments (future feature)

All tables include Row Level Security (RLS) policies to ensure data privacy.

## 🔧 Configuration

### Environment Variables
The app uses Supabase configuration directly in the code. For production, consider using environment variables:

```javascript
// src/lib/supabase.js
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'your-url'
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-key'
```

### Customization
- **Colors**: Modify Tailwind config for custom branding
- **Workflow Stages**: Update `TaskStatus` enum in `src/types/task.js`
- **Categories**: Modify `TaskCategory` enum for your use case

## 📱 Usage

### Getting Started
1. **Sign Up** - Create your account
2. **Create Your First Idea** - Click "New Idea" to get started
3. **Organize** - Use categories, priorities, and tags
4. **Execute** - Move ideas through the workflow stages
5. **Track Progress** - Add process steps and follow-ups

### Workflow Stages
- **💡 Idea** - Initial concept capture
- **📋 Planning** - Define scope and create action plans
- **⚡ In Progress** - Active implementation
- **👀 Review** - Testing and validation
- **✅ Completed** - Successfully finished

### Pro Tips
- Use the dashboard view for high-level overview
- Switch to list view for detailed task management
- Set due dates to stay accountable
- Break big ideas into smaller process steps
- Use follow-ups for action items and reminders

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables (if using them)
3. Deploy automatically on every push

### Netlify
1. Connect your repository to Netlify
2. Build command: `npm run build`
3. Publish directory: `dist`

### Other Platforms
The app builds to static files and can be deployed anywhere that serves static content.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

If you encounter any issues:
1. Check the `SUPABASE_SETUP.md` guide
2. Review the browser console for errors
3. Check Supabase logs in the dashboard
4. Open an issue on GitHub

---

**Start transforming your ideas into reality today!** 🚀
