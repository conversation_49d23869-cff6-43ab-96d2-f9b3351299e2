{"name": "idea-task-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^1.7.17", "@supabase/supabase-js": "^2.49.8", "date-fns": "^2.30.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "vite": "^4.4.5"}}